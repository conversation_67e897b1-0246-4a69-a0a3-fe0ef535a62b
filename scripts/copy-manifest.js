import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import process from 'process';

// 获取当前文件的目录路径
const __dirname = fileURLToPath(new URL('.', import.meta.url));
const rootDir = path.resolve(__dirname, '..');

// 源文件路径
const manifestSrc = path.join(rootDir, 'public', 'manifest.json');
// 目标文件路径
const manifestDest = path.join(rootDir, 'dist', 'manifest.json');

// 复制文件
try {
  // 确保目标目录存在
  if (!fs.existsSync(path.dirname(manifestDest))) {
    fs.mkdirSync(path.dirname(manifestDest), { recursive: true });
  }
  
  // 读取并写入文件
  const manifestContent = fs.readFileSync(manifestSrc, 'utf8');
  fs.writeFileSync(manifestDest, manifestContent, 'utf8');
  
  console.log('✅ manifest.json 复制成功');
  
  // 复制图标文件
  const iconsDir = path.join(rootDir, 'public', 'icons');
  const destIconsDir = path.join(rootDir, 'dist', 'icons');
  
  if (!fs.existsSync(destIconsDir)) {
    fs.mkdirSync(destIconsDir, { recursive: true });
  }
  
  const iconFiles = fs.readdirSync(iconsDir);
  iconFiles.forEach(iconFile => {
    const srcIconPath = path.join(iconsDir, iconFile);
    const destIconPath = path.join(destIconsDir, iconFile);
    fs.copyFileSync(srcIconPath, destIconPath);
  });
  
  console.log('✅ 图标文件复制成功');
} catch (error) {
  console.error('❌ 复制文件失败:', error);
  process.exit(1);
} 