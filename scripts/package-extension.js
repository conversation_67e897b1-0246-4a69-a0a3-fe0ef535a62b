import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import process from 'process';
import { execSync } from 'child_process';
import { createConsola } from 'consola';

const logger = createConsola({
  level: 4,
  fancy: true,
});

// 获取当前文件的目录路径
const __dirname = fileURLToPath(new URL('.', import.meta.url));
const rootDir = path.resolve(__dirname, '..');

// 获取包信息
const packageJson = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));
const { name } = packageJson;

// 读取manifest.json文件
const MANIFEST_PATH = path.join(rootDir, 'public', 'manifest.json');
const manifestJson = JSON.parse(fs.readFileSync(MANIFEST_PATH, 'utf8'));
let { version } = manifestJson;

// 递增版本号函数
function incrementVersion(version) {
  const versionParts = version.split('.');
  if (versionParts.length !== 3) {
    throw new Error(`无效的版本号格式: ${version}，必须是x.y.z格式`);
  }

  // 将版本号部分转换为数字
  let [x, y, z] = versionParts.map(part => parseInt(part, 10));

  // 检查版本号范围有效性
  if (x > 99 || y > 99 || z > 9) {
    throw new Error(`版本号超出范围: ${version}，限制为：x(0-99).y(0-99).z(0-9)`);
  }

  // 递增版本号逻辑
  z += 1;

  // 处理进位
  if (z > 9) {
    z = 0;
    y += 1;

    if (y > 99) {
      y = 0;
      x += 1;

      if (x > 99) {
        throw new Error('版本号已达到最大值 99.99.9，无法继续递增');
      }
    }
  }

  return [x, y, z].join('.');
}

// 更新manifest.json中的版本号
function updateManifestVersion() {
  const newVersion = incrementVersion(version);
  logger.info(`递增版本号: ${version} -> ${newVersion}`);

  // 更新版本号
  manifestJson.version = newVersion;

  // 写回manifest.json文件
  fs.writeFileSync(MANIFEST_PATH, JSON.stringify(manifestJson, null, 2), 'utf8');
  logger.success(`manifest.json 版本已更新至 ${newVersion}`);

  // 返回新版本号以供后续使用
  return newVersion;
}

// 更新版本号
version = updateManifestVersion();

// 执行构建命令，确保使用新版本号
logger.info('使用新版本号执行构建...');
try {
  execSync('pnpm build:extension', { stdio: 'inherit' });
  logger.success('构建完成，生成的文件包含新版本号');
} catch (error) {
  logger.error('构建失败:', error);
  process.exit(1);
}

// 配置路径
const DIST_DIR = path.join(rootDir, 'dist');
const OUTPUT_DIR = path.join(rootDir, 'release');
const PRIVATE_KEY_PATH = path.join(rootDir, 'key.pem');
const OUTPUT_CRX = path.join(OUTPUT_DIR, `${name}-v${version}.crx`);
const OUTPUT_ZIP = path.join(OUTPUT_DIR, `${name}-v${version}.zip`);

// 确保输出目录存在
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

function packageExtension() {
  logger.start('开始打包Chrome扩展...');

  try {
    // 检查dist目录是否存在
    if (!fs.existsSync(DIST_DIR)) {
      logger.error('dist目录不存在，请先运行 npm run build:extension');
      process.exit(1);
    }

    // 如果私钥不存在，使用crx命令行工具生成新的私钥
    if (!fs.existsSync(PRIVATE_KEY_PATH)) {
      logger.info('私钥不存在，正在生成新的私钥...');
      execSync(`npx crx keygen "${rootDir}"`, { stdio: 'inherit' });
      logger.success(`私钥已生成并保存至: ${PRIVATE_KEY_PATH}`);
    }

    // 使用crx命令行工具打包成.crx文件
    logger.info('正在生成.crx文件...');
    execSync(`npx crx pack "${DIST_DIR}" -o "${OUTPUT_CRX}" -p "${PRIVATE_KEY_PATH}"`, { stdio: 'inherit' });
    logger.success(`CRX文件已生成: ${OUTPUT_CRX}`);

    // 创建ZIP文件（用于Chrome商店上传）
    logger.info('正在生成.zip文件...');
    if (process.platform === 'win32') {
      // Windows系统使用PowerShell命令
      execSync(`powershell Compress-Archive -Path "${DIST_DIR}\\*" -DestinationPath "${OUTPUT_ZIP}" -Force`, { stdio: 'inherit' });
    } else {
      // macOS/Linux系统使用zip命令
      execSync(`cd "${DIST_DIR}" && zip -r "${OUTPUT_ZIP}" ./*`, { stdio: 'inherit' });
    }
    logger.success(`ZIP文件已生成: ${OUTPUT_ZIP}`);

    logger.success(`扩展打包完成! 版本: v${version}`);
    logger.info('------------------------------------------');
    logger.info('CRX文件用于直接分发给用户安装');
    logger.info('ZIP文件用于上传至Chrome商店');
    logger.info('------------------------------------------');
  } catch (error) {
    logger.error('打包过程中发生错误:', error);
    process.exit(1);
  }
}

// 执行打包
packageExtension();
