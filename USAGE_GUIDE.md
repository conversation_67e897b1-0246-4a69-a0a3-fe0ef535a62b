# 广汽国际平台插件集合 - 用户使用手册

## 目录

- [简介](#简介)
- [安装插件](#安装插件)
- [核心功能](#核心功能)
  - [1. OBS 文件批量上传](#1-obs-文件批量上传)
  - [2. 多语言内容处理与翻译](#2-多语言内容处理与翻译)
  - [3. 批量添加菜单项](#3-批量添加菜单项)
- [配置站点信息](#配置站点信息)
- [常见问题](#常见问题)

## 简介

欢迎使用广汽国际平台插件集合！这款Chrome浏览器扩展旨在帮助您高效处理广汽国际业务相关的多站点任务，主要包括OBS文件批量上传、多语言内容处理与翻译以及后台菜单的批量添加。通过本插件，您可以简化繁琐的手动操作，提升工作效率。

## 安装插件

1.  **获取插件文件**：

    - 如果您收到的是一个 `.zip` 包，请先解压到一个固定的文件夹。
    - 如果您收到的是一个 `.crx` 文件，可以直接使用。

2.  **打开Chrome扩展程序页面**：

    - 在Chrome浏览器地址栏输入 `chrome://extensions/` 并按回车。

3.  **开启"开发者模式"**：

    - 在打开的扩展程序页面右上角，找到并打开"开发者模式"开关。

4.  **加载插件**：

    - **对于解压后的文件夹（推荐用于开发和测试）**：
      - 点击左上角的"加载已解压的扩展程序"按钮。
      - 在弹出的对话框中，选择您之前解压插件的 `dist` 文件夹 (通常是项目构建后的输出目录)。
    - **对于 `.crx` 文件**：
      - 直接将 `.crx` 文件拖拽到 `chrome://extensions/` 页面中。
      - 根据提示确认添加扩展。

5.  **完成安装**：
    - 安装成功后，您会在Chrome工具栏（通常在右上角，可能需要点击拼图图标展开）看到插件的图标。点击图标即可开始使用。

## 核心功能

### 1. OBS 文件批量上传

本插件支持向多个已配置的站点批量上传文件到OBS（对象存储服务）。

**使用步骤**：

1.  **打开插件弹出窗口**：点击浏览器工具栏中的插件图标。
2.  **配置站点**：
    - 如果尚未配置站点或需要修改，请前往"选项"页面（通常可以通过右键点击插件图标选择"选项"，或在插件弹出窗口中找到入口）。
    - 在"站点配置"部分，添加或编辑目标站点的URL、登录凭据（如用户名、密码或Token）等信息。确保信息准确无误。
3.  **选择文件**：
    - 在插件弹出窗口的主界面，找到文件上传区域。
    - 点击"选择文件"或拖拽文件到指定区域，选择您需要上传的一个或多个文件。
4.  **选择目标站点**：
    - 从已配置的站点列表中，勾选您希望将文件上传到的一个或多个站点。
5.  **开始上传**：
    - 点击"开始上传"按钮。插件将自动登录各选定站点并执行文件上传操作。
6.  **查看结果**：
    - 上传过程中和完成后，您可以在插件界面的"结果展示"区域查看每个文件在每个站点的上传状态（成功/失败）和相关提示信息。

### 2. 多语言内容处理与翻译

插件提供了多语言内容导入、处理和翻译辅助功能，特别适用于需要维护多语言版本内容的场景。

**使用步骤**：

1.  **打开翻译页面**：
    - 在插件弹出窗口中，通常会有一个按钮或链接，如"打开翻译页面"或"多语言处理"，点击它会打开一个新的浏览器标签页专门用于翻译任务。
2.  **导入多语言文件**：
    - 在翻译页面，使用"导入多语言文件"（通常是一个Excel文件，如 `.xlsx` 格式）功能，选择包含待翻译内容或已有翻译的Excel文件。
    - 插件会解析文件内容，并展示键名 (Key) 和各语言列。
3.  **进行翻译或编辑**：
    - 您可以直接在表格中编辑或填写不同语言的翻译文本。
    - 插件可能集成第三方翻译服务（如Google翻译、百度翻译等）的API接口，提供"一键翻译"或"辅助翻译"功能，帮助您快速填充译文。请确保已在插件"选项"中正确配置了翻译服务的API密钥（如果需要）。
4.  **处理与导出**：
    - 完成翻译和编辑后，您可以将处理后的多语言内容导出为特定格式的文件（如JSON、新的Excel等），以便导入到您的业务系统中。
    - 插件也可能支持直接将翻译内容通过API推送到已配置的站点后台。

### 3. 批量添加菜单项

对于需要在多个站点后台批量创建导航菜单或侧边栏菜单项的场景，插件提供了便捷的批量操作功能。

**使用步骤**：

1.  **打开批量添加菜单页面**：
    - 在插件弹出窗口中，找到"批量添加菜单"或类似功能的入口，点击后会打开一个新的浏览器标签页。
2.  **准备菜单数据**：
    - 通常需要按照特定格式准备菜单数据，例如一个Excel文件，其中包含菜单名称、链接、层级关系、目标站点等信息。页面上通常会有格式说明或模板下载。
3.  **导入菜单数据文件**：
    - 在批量添加菜单页面，上传您准备好的菜单数据文件。
4.  **选择目标站点**：
    - 勾选需要执行批量添加菜单操作的一个或多个目标站点。确保这些站点已在插件"选项"中正确配置并通过认证。
5.  **执行添加**：
    - 点击"开始添加"或类似按钮。插件将按照数据文件中的定义，自动在选定的站点后台创建菜单项。
6.  **查看结果**：
    - 操作完成后，页面会显示每个站点批量添加菜单的结果，包括成功和失败的条目及原因。

## 配置站点信息

插件的大部分功能都依赖于正确的站点配置。您可以在插件的"选项"页面管理这些配置。

1.  **访问选项页面**：
    - **方法一**：右键点击浏览器工具栏中的插件图标，选择"选项"。
    - **方法二**：在插件的弹出窗口中，通常会有"设置"、"选项"或齿轮图标等入口。
2.  **管理站点列表**：
    - 在选项页面，您可以找到"站点配置管理"或类似的区域。
    - **添加站点**：点击"添加站点"按钮，填写站点名称、访问URL、登录API地址、认证方式（如账号密码、Token）、以及其他必要参数。
    - **编辑站点**：修改已存在的站点信息。
    - **删除站点**：移除不再需要的站点配置。
3.  **其他配置**：
    - 选项页面可能还包含其他全局设置，如默认上传路径、翻译服务API密钥配置等。

## 常见问题

1.  **插件图标不显示怎么办？**

    - 确保插件已成功安装并在 `chrome://extensions/` 页面中处于启用状态。
    - 点击Chrome浏览器工具栏右侧的拼图图标（扩展程序），找到本插件并点击图钉图标将其固定在工具栏上。

2.  **文件上传失败怎么办？**

    - 检查插件"选项"中对应站点的配置信息是否准确（URL、用户名、密码/Token）。
    - 检查您的网络连接是否正常。
    - 查看插件界面提供的错误提示，了解失败的具体原因。
    - 确保目标站点服务运行正常。

3.  **无法登录站点/认证失败？**

    - 确认您在插件"选项"中填写的登录凭据完全正确。
    - 部分站点可能需要特定的登录流程或验证码，插件可能无法完全自动化处理所有情况。
    - 检查站点是否有安全策略（如IP限制）阻止了插件的访问。

4.  **批量添加菜单/翻译功能不工作？**
    - 确保您准备的数据文件格式符合插件要求。通常在对应功能页面会有格式说明或模板下载。
    - 检查目标站点的配置和登录状态。

如果遇到本手册未涵盖的问题，您可以尝试重新安装插件，或联系插件开发者获取支持。
