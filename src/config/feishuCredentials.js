/**
 * 飞书应用凭证配置
 * 请在此文件中配置您的飞书应用信息
 */
// https://dyz7is63sl.feishu.cn/base/JsEQbwl74ax6Das5aoScvMaentC?table=tbll5TPVQiri5aSC&view=vewQa1YsiM 飞书表格链接
const FEISHU_CREDENTIALS = {
  // 飞书应用基础信息
  APP_ID: 'cli_a894fa4389f8d00e', // 请填入您的飞书应用 App ID
  APP_SECRET: '18TnEBfchWRrSGRradYSCeiVCQxvcByP', // 请填入您的飞书应用 App Secret

  // 多维表格配置
  APP_TOKEN: 'JsEQbwl74ax6Das5aoScvMaentC', // 请填入多维表格的 App Token
  TABLE_ID: 'tbll5TPVQiri5aSC', // 请填入目标数据表的 Table ID

  // 可选配置
  ENVIRONMENT: 'production', // 环境标识：development | production
  DEBUG: false, // 是否开启调试模式
};

/**
 * 验证凭证配置是否完整
 * @returns {Object} 验证结果
 */
export const validateCredentials = () => {
  const missing = [];

  if (!FEISHU_CREDENTIALS.APP_ID) missing.push('APP_ID');
  if (!FEISHU_CREDENTIALS.APP_SECRET) missing.push('APP_SECRET');
  if (!FEISHU_CREDENTIALS.APP_TOKEN) missing.push('APP_TOKEN');
  if (!FEISHU_CREDENTIALS.TABLE_ID) missing.push('TABLE_ID');

  return {
    isValid: missing.length === 0,
    missing,
    message: missing.length > 0 ? `缺少必要配置: ${missing.join(', ')}` : '配置验证通过',
  };
};

/**
 * 获取飞书凭证配置
 * @returns {Object} 凭证配置对象
 */
export const getFeishuCredentials = () => {
  const validation = validateCredentials();

  if (!validation.isValid) {
    console.warn('飞书凭证配置不完整:', validation.message);
    if (FEISHU_CREDENTIALS.DEBUG) {
      console.warn('请检查 src/config/feishuCredentials.js 文件中的配置');
    }
  }

  return {
    ...FEISHU_CREDENTIALS,
    validation,
  };
};

export default FEISHU_CREDENTIALS;
