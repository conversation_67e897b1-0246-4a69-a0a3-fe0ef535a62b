import { API_PATHS, TIMEOUTS, CACHE_EXPIRES_IN_MINUTES } from '../constants';
import { tokenCache } from '../services/authService';

/**
 * 登录API
 * @param {Object} params - 登录参数
 * @param {string} params.domain - 域名
 * @param {string} params.account - 账号
 * @param {string} params.password - 密码
 * @param {string} params.code - 验证码
 */
export const loginApi = async ({ domain, account, password, code }) => {
  // 构建完整登录API路径
  const loginUrl = `${domain}${API_PATHS.LOGIN}`;

  // 检查是否有有效的缓存token
  const cachedToken = tokenCache.getToken(domain);

  if (cachedToken) {
    return cachedToken;
  }

  console.log('开始API登录:', account, loginUrl);

  try {
    // 构建登录请求
    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        account,
        password,
        code,
      }),
      timeout: TIMEOUTS.API_REQUEST,
    });

    if (!response.ok) {
      throw new Error(`登录请求失败: ${response.status} ${response.statusText}`);
    }

    const responseData = (await response.json()) ?? {};

    // 检查登录响应
    if (responseData.code !== 0) {
      throw new Error(`${responseData.msg}`);
    }

    // 从响应中提取token
    const token = responseData.content;

    if (!token) {
      throw new Error('登录成功但未获取到有效token');
    }
    console.log('API登录成功，已获取token');

    // 将token保存到缓存中，默认30分钟过期
    tokenCache.saveToken(domain, token, CACHE_EXPIRES_IN_MINUTES);

    return token;
  } catch (error) {
    console.error('API登录失败:', error);
    throw error;
  }
};

/**
 *
 * @param {object} params - 请求参数
 * @param {string} params.domain - 站点
 * @param {string} params.fileUrl - 文件存储 url
 * @returns
 */
export const importLanguage = async ({ domain, fileUrl }) => {
  const apiUrl = `${domain}${API_PATHS.IMPORT_LANGUAGE}`;

  const token = tokenCache.getToken(domain);

  const response = await fetch(apiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      b_accesstoken: token,
      Locallanguage: 'zh-CN',
    },
    body: JSON.stringify({
      fileUrl,
    }),
  });

  if (!response.ok) {
    throw new Error(`导入请求失败: ${response.status} ${response.statusText}`);
  }

  const responseData = await response.json();

  if (responseData.code !== 0) {
    throw new Error(`导入接口返回错误: ${responseData.msg || '未知错误'}`);
  }

  return responseData;
};

/**
 * 获取菜单数结构
 * @param {string} domainUrl - 站点
 * @returns {Promise<Array>} - 菜单树数组
 */
export const fetchMenuTree = async domainUrl => {
  try {
    const domain = domainUrl;

    const token = tokenCache.getToken(domainUrl);

    const url = `${domain}${API_PATHS.MENU_TREE}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        b_accesstoken: token,
        Locallanguage: 'zh-CN',
      },
      body: JSON.stringify({}),
    });

    if (!response.ok) {
      throw new Error(`获取菜单树失败: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    // 检查响应
    if (responseData.code !== 0) {
      throw new Error(`获取菜单树失败: ${responseData.msg || '服务器返回错误'}`);
    }

    return responseData.content?.tree || [];
  } catch (error) {
    console.error('获取菜单树结构失败:', error);
    return [];
  }
};

/**
 * 获取当前用户信息
 * @param {string} domainUrl - 域名URL
 * @param {string} token - 访问令牌
 * @returns {Promise<Object>} - 用户信息对象
 */
export const fetchCurrentUserInfo = async domainUrl => {
  try {
    const url = `${domainUrl}${API_PATHS.CURRENT_USER_INFO}`;

    const token = tokenCache.getToken(domainUrl);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        b_accesstoken: token,
        Locallanguage: 'zh-CN',
      },
      body: JSON.stringify({}),
    });

    if (!response.ok) {
      throw new Error(`获取用户信息失败: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    // 检查响应
    if (responseData.code !== 0) {
      throw new Error(`获取用户信息失败: ${responseData.msg || '服务器返回错误'}`);
    }

    return responseData.content?.userId || '';
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
    throw error;
  }
};

/**
 * 获取用户角色和菜单
 * @param {string} domainUrl - 域名URL
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} - 角色和菜单对象
 */
export const fetchUserRoleMenus = async (domainUrl, userId) => {
  try {
    const domain = domainUrl;

    const token = tokenCache.getToken(domain);

    const url = `${domain}${API_PATHS.USER_ROLE_MENU}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        b_accesstoken: token,
        Locallanguage: 'zh-CN',
      },
      body: JSON.stringify({ userId }),
    });

    if (!response.ok) {
      throw new Error(`获取用户角色菜单权限失败: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    // 检查响应
    if (responseData.code !== 0) {
      throw new Error(`获取用户角色菜单权限失败: ${responseData.msg || '服务器返回错误'}`);
    }

    return responseData.content || {};
  } catch (error) {
    console.error('获取用户角色菜单权限失败:', error);
    throw error;
  }
};

/**
 * 获取角色的菜单树
 * @param {string} domainUrl - 域名URL
 * @param {string} roleId - 角色ID
 * @returns {Promise<Object>} - 角色拥有的菜单树
 */
export const fetchRoleMenuTree = async (domainUrl, roleId) => {
  try {
    // 格式化域名
    const domain = domainUrl;

    const token = tokenCache.getToken(domain);

    const url = `${domain}${API_PATHS.ROLE_MENU_TREE}`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        b_accesstoken: token,
        Locallanguage: 'zh-CN',
      },
      body: JSON.stringify([roleId]),
    });

    if (!response.ok) {
      throw new Error(`获取角色菜单树失败: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    // 检查响应
    if (responseData.code !== 0) {
      throw new Error(`获取角色菜单树失败: ${responseData.msg || '服务器返回错误'}`);
    }

    return responseData.content || [];
  } catch (error) {
    console.error('获取角色菜单树失败:', error);
    return [];
  }
};
