import React from "react";
import { ProCard } from "@ant-design/pro-components";
import { DownOutlined } from "@ant-design/icons";
import "./index.css";

/**
 * 可折叠卡片组件
 * @param {Object} props 组件属性
 * @param {string|ReactNode} props.title 卡片标题
 * @param {boolean} props.collapsed 折叠状态
 * @param {function} props.onCollapse 折叠状态变更回调
 * @param {ReactNode} props.extra 卡片右上角额外内容
 * @param {string} props.className 额外的类名
 * @param {React.Ref} props.cardRef 卡片的引用
 * @param {ReactNode} props.children 卡片内容
 * @param {Object} props.cardProps 传递给ProCard的其他属性
 * @returns {ReactNode}
 */
const CollapsibleCard = ({
  title,
  collapsed,
  onCollapse,
  extra,
  className = "",
  cardRef,
  children,
  ...cardProps
}) => {
  // 处理折叠状态切换
  const toggleCollapse = () => {
    if (onCollapse) {
      onCollapse(!collapsed);
    }
  };

  // 修改自定义标题，确保整个标题区域可点击
  const customTitle = (
    <div
      style={{ display: "flex", alignItems: "center", cursor: "pointer" }}
      onClick={toggleCollapse}
      className="collapsible-card-title"
    >
      <>
        <span className="custom-collapse-icon">
          <DownOutlined />
        </span>
        <span>{title}</span>
      </>
    </div>
  );

  return (
    <ProCard
      ref={cardRef}
      headerBordered
      bordered={false}
      ghost={false}
      className={`collapsible-card ${className} ${
        collapsed ? "custom-collapsed" : ""
      }`}
      extra={extra}
      {...cardProps}
      collapsible={false}
      title={customTitle}
    >
      <div className="card-content-wrapper">{children}</div>
    </ProCard>
  );
};

export default CollapsibleCard;
