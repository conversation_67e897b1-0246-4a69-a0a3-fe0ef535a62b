.collapsible-card {
  transition: box-shadow 0.3s ease;
}

/* 折叠状态 */
.custom-collapsed .card-content-wrapper {
  max-height: 0;
  opacity: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  transform: translateY(-10px);
  transition: max-height 0.25s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              padding 0.15s ease,
              margin 0.15s ease;
}

/* 展开状态 */
.card-content-wrapper {
  transition: max-height 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
              opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
              transform 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
              padding 0.2s ease,
              margin 0.2s ease;
  /* max-height: 1000px; */
  opacity: 1;
  overflow: auto;
  overflow-x: hidden;
  transform: translateY(0);
  will-change: max-height, opacity, transform;
  transform-origin: top;
  padding: 16px 0;
}

/* 折叠图标 */
.custom-collapse-icon {
  margin-right: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  color: #1677ff;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  justify-content: center;
  transition: all 0.3s;
}

.custom-collapse-icon:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 折叠图标动画 */
.custom-collapse-icon .anticon {
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.custom-collapsed .custom-collapse-icon .anticon-down {
  transform: rotate(-90deg);
}

/* 移除不再需要的样式 */ 