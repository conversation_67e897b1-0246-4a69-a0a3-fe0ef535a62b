/* global chrome */
import React, { useState, useEffect } from 'react';
import { Modal, Button, Space, Alert, Typography, Card, message as antMessage, notification } from 'antd';
import { TranslationOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { MESSAGE_TYPES, OPERATION_TYPES } from '@/constants';
import ResultDisplay from '../ResultDisplay';
import './index.css';

const { Text } = Typography;

/**
 * 导入多语言弹窗组件
 * @param {Object} props
 * @param {boolean} props.visible - 弹窗是否可见
 * @param {Function} props.onCancel - 取消/关闭的回调函数
 * @param {Array} props.siteConfigs - 站点配置
 * @param {Array} props.files - 文件列表
 * @param {string} props.fileDirectory - 文件路径前缀
 */
const ImportMultiLanguageModal = ({ visible, onCancel, siteConfigs = [], files = [], fileDirectory = '' }) => {
  // 内部状态管理
  const [importing, setImporting] = useState(false);
  const [importResults, setImportResults] = useState([]);
  const [messageApi, contextHolder] = antMessage.useMessage();

  // 当前状态：初始状态、导入中、导入完成
  const [currentStatus, setCurrentStatus] = useState('initial');

  // 重置状态
  useEffect(() => {
    if (visible) {
      // 如果弹窗打开时有上一次的结果，重置状态
      if (importResults.length > 0) {
        setImportResults([]);
      }
      setCurrentStatus('initial');
    }
  }, [visible]);

  // 监听导入多语言状态更新
  useEffect(() => {
    const handleImportStatusUpdate = message => {
      try {
        // 只处理多语言导入的完成消息
        if (message.type === MESSAGE_TYPES.BROWSER_AUTOMATION_COMPLETED && message.operationType === OPERATION_TYPES.IMPORT_LANGUAGE) {
          console.log('收到多语言导入完成消息:', message);

          // 更新状态
          setImporting(false);
          setImportResults(message.results || []);

          // 显示通知
          if (message.success) {
            messageApi.success('多语言导入成功');
            notification.success({
              message: '导入成功',
              description: '所有多语言文件导入完成！',
              duration: 4,
            });
          } else {
            messageApi.error('导入失败: ' + (message.error || message.results?.[0]?.error || '未知错误'));
            notification.error({
              message: '导入失败',
              description: message.error || message.results?.[0]?.error || '处理过程中出现错误',
              duration: 6,
            });
          }
        }
      } catch (error) {
        console.error('导入多语言状态更新错误:', error);
      }
    };

    // 添加消息监听器
    if (chrome?.runtime?.onMessage) {
      chrome.runtime.onMessage.addListener(handleImportStatusUpdate);
    }

    // 清理函数
    return () => {
      if (chrome?.runtime?.onMessage) {
        chrome.runtime.onMessage.removeListener(handleImportStatusUpdate);
      }
    };
  }, []);

  // 根据导入多语言状态更新当前界面状态
  useEffect(() => {
    if (importing) {
      setCurrentStatus('importing');
    } else if (importResults.length > 0) {
      setCurrentStatus('completed');
    } else {
      setCurrentStatus('initial');
    }
  }, [importing, importResults]);

  // 格式化目录路径，确保以/结尾，不以/开头
  const formatDirectory = dir => {
    if (!dir) return '';

    // 去除开头的斜杠
    let formatted = dir.startsWith('/') ? dir.slice(1) : dir;

    // 确保末尾有斜杠，除非为空
    if (formatted && !formatted.endsWith('/')) {
      formatted += '/';
    }

    return formatted;
  };

  // 开始导入多语言数据
  const startImportMultiLanguage = () => {
    // 确保有站点配置和文件
    if (siteConfigs.length === 0 || files.length === 0) {
      messageApi.error('请确保有站点配置和上传的文件');
      return;
    }

    // 过滤掉禁用的站点配置
    const currentSiteConfigs = siteConfigs.filter(config => config && config.enabled !== false);

    if (currentSiteConfigs.length === 0) {
      messageApi.error('请至少启用一个站点配置');
      return;
    }

    // 获取第一个站点名称，用于初始状态显示
    const firstSite = currentSiteConfigs[0]?.domain || currentSiteConfigs[0]?.site || '未知站点';


    // 更新状态
    setImporting(true);

    // 发送导入请求到后台脚本
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage(
        {
          type: 'IMPORT_MULTI_LANGUAGE',
          siteConfigs: currentSiteConfigs,
          files: files.map(file => ({
            name: file.name,
            path: formatDirectory(fileDirectory) + file.name,
          })),
        },
        response => {
          // 初始响应处理
          if (!response || !response.success) {
            setImporting(false);
            messageApi.error(response?.error || '启动导入处理失败');
          }
        }
      );
    }
  };

  // 转换importResults为ResultDisplay组件所需的格式
  const formatImportResults = () => {
    if (!importResults || importResults.length === 0) return null;

    // 判断整体成功状态 - 判断是否有站点成功
    const anySuccess = importResults.some(item => item.success);

    return {
      success: anySuccess,
      // 明确标记为多语言导入结果
      isLanguageImportResult: true,
      sites: importResults.map(result => {
        const files = result.files || [];

        const languageItems = files.map(file => ({
          name: file.file || file.name || '未知文件',
          status: file.success ? 'success' : 'error',
          message: file.message || file.error || '',
          success: file.success,
        }));

        return {
          domain: result.domain || result.site || '未知站点',
          status: result.success ? 'success' : 'error',
          message: result.message || result.error || '',
          files: files,
          languageItems: languageItems,
        };
      }),
    };
  };

  // 处理关闭弹窗
  const handleCancel = () => {
    if (importing) {
      return; // 正在导入时不允许关闭
    }
    onCancel();
  };

  // 渲染初始状态
  const renderInitialState = () => {
    return (
      <Card className="import-initial-card">
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center', padding: '20px 0' }}>
          <InfoCircleOutlined style={{ fontSize: 48, color: '#1890ff' }} />
          <Text>准备导入多语言数据</Text>
          <Alert message="点击「开始导入」按钮开始进行多语言导入" type="info" showIcon />
        </Space>
      </Card>
    );
  };

  // 根据当前状态渲染不同内容
  const renderContent = () => {
    switch (currentStatus) {
      case 'importing':
      case 'completed':
        return <ResultDisplay result={formatImportResults()} loading={importing} />;
      case 'initial':
      default:
        return renderInitialState();
    }
  };

  // 内容区域样式
  const contentStyle = {
    maxHeight: '60vh', // 设置内容区域最大高度为视口高度的60%
    overflowY: 'auto', // 内容超出时显示垂直滚动条
    padding: '8px'
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <TranslationOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <span>导入多语言</span>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={1200}
      footer={[
        <Button key="back" onClick={handleCancel} disabled={importing}>
          {importing ? '导入中...' : '关闭'}
        </Button>,
        currentStatus === 'initial' && (
          <Button
            key="submit"
            type="primary"
            onClick={startImportMultiLanguage}
            disabled={importing || siteConfigs.length === 0 || files.length === 0}
          >
            开始导入
          </Button>
        ),
      ]}
    >
      {contextHolder}
      <div className="import-multilanguage-content" style={contentStyle}>
        {renderContent()}
      </div>
    </Modal>
  );
};

export default ImportMultiLanguageModal;
