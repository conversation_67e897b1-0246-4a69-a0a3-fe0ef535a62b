/* 导入多语言弹窗样式 */
.import-status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.import-multilanguage-content {
  min-height: 400px;
  padding: 8px 0;
}

.import-initial-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 导入中卡片样式 */
.import-importing-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.import-progress-container {
  width: 100%;
  padding: 16px;
}

.import-status-title {
  margin-bottom: 24px !important;
  text-align: center;
  color: #1890ff;
}

.import-steps-container {
  margin-top: 36px;
  padding: 0 16px;
}

/* 结果显示区域样式 */
.result-display-container {
  min-height: 300px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .import-multilanguage-content {
    min-height: 300px;
  }
  
  .import-progress-container {
    padding: 8px;
  }
  
  .import-steps-container {
    margin-top: 24px;
  }
}

.import-status-info-item {
  margin-bottom: 8px;
} 