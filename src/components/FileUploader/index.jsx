import React, { useRef } from 'react';
import { ProFormText, ProFormUploadDragger } from '@ant-design/pro-components';
import { Button, Tag, Empty, message } from 'antd';
import { ClearOutlined, LinkOutlined, DeleteOutlined } from '@ant-design/icons';
import CollapsibleCard from '@/components/CollapsibleCard';
import { getFileIcon, formatFileSize } from '@/utils/fileUtils';

const FileUploader = ({
  value = [],
  onChange,
  onReset,
  directory = 'public',
  onDirectoryChange,
  max = 10,
  collapsed = false,
  onCollapse,
  messageApi,
}) => {
  // 消息提示记录，用于防止重复消息
  const lastMessageRef = useRef({ content: '', timestamp: 0 });

  // 处理文件上传变化
  const handleFileChange = info => {
    const { fileList } = info;

    if (fileList && fileList.length > 0) {
      // 检测重复文件并进行覆盖处理
      const fileMap = new Map();
      let hasDuplicate = false;
      const duplicateNames = new Set();

      // 第一次遍历，找出所有重复的文件名
      fileList.forEach(file => {
        const fileName = file.name;
        if (fileMap.has(fileName)) {
          hasDuplicate = true;
          duplicateNames.add(fileName);
        } else {
          fileMap.set(fileName, file);
        }
      });

      // 如果存在重复文件，保留最新上传的版本
      if (hasDuplicate) {
        // 清空之前的映射，重新构建
        fileMap.clear();

        // 反向遍历，确保保留最新上传的重复文件
        for (let i = fileList.length - 1; i >= 0; i--) {
          const file = fileList[i];
          if (!fileMap.has(file.name)) {
            fileMap.set(file.name, file);
          }
        }

        // 构建新的文件列表，保持原有顺序
        const newFileList = [];
        fileList.forEach(file => {
          if (fileMap.get(file.name) === file) {
            newFileList.push(file);
          }
        });

        if (onChange) {
          onChange(newFileList);
        }

        // 显示覆盖提示（增加消息去重逻辑）
        const duplicateNamesStr = Array.from(duplicateNames).join('、');
        const messageContent = `文件"${duplicateNamesStr}"已覆盖为最新上传的版本`;
        const now = Date.now();

        // 只有当消息内容不同，或者距离上次显示超过1秒，才显示新消息
        if (lastMessageRef.current.content !== messageContent || now - lastMessageRef.current.timestamp > 1000) {
          if (messageApi) {
            messageApi.info(messageContent);
          } else {
            message.info(messageContent);
          }
          // 更新最近显示的消息记录
          lastMessageRef.current = {
            content: messageContent,
            timestamp: now,
          };
        }
      } else {
        if (onChange) {
          onChange(fileList);
        }
      }
    } else {
      if (onChange) {
        onChange([]);
      }
    }
  };

  // 处理目录路径变化
  const handleDirectoryChange = e => {
    if (onDirectoryChange) {
      onDirectoryChange(e.target.value || '');
    }
  };

  // 重置上传的文件
  const handleReset = () => {
    if (onReset) {
      onReset();
    } else if (onChange) {
      onChange([]);
      if (messageApi) {
        messageApi.success('已清除所有上传文件');
      } else {
        message.success('已清除所有上传文件');
      }
    }
  };

  // 文件列表的自定义渲染函数
  const customItemRender = (originNode, file, fileList, actions) => {
    const index = fileList.indexOf(file) + 1; // 序号从1开始

    // 获取文件大小
    const fileSize = file.size ? formatFileSize(file.size) : '';

    return (
      <div className="custom-upload-item">
        <div className="file-item-number">{index}</div>
        <div className="file-item-icon">{getFileIcon(file.name, { fontSize: '24px' })}</div>
        <div className="file-item-info">
          <div className="file-item-name">{file.name}</div>
          {fileSize && <div className="file-item-size">{fileSize}</div>}
        </div>
        <div className="file-item-actions">
          <Button type="text" icon={<DeleteOutlined />} onClick={() => actions.remove()} className="file-delete-btn" />
        </div>
      </div>
    );
  };

  // 构建标题内容，包含文件计数标签
  const titleContent = (
    <>
      文件上传
      {value.length > 0 && (
        <Tag color="blue" style={{ marginLeft: 8 }}>
          已选择 {value.length} 个文件
        </Tag>
      )}
    </>
  );

  return (
    <CollapsibleCard
      title={titleContent}
      collapsed={collapsed}
      onCollapse={onCollapse}
      headerBordered
      style={{ marginBottom: 24 }}
      extra={
        value.length > 0 && (
          <Button icon={<ClearOutlined />} onClick={handleReset}>
            清除文件
          </Button>
        )
      }
    >
      <ProFormText
        name="fileDirectory"
        label="文件目录"
        placeholder="请输入文件目录路径（可选）"
        fieldProps={{
          onChange: handleDirectoryChange,
          prefix: <LinkOutlined />,
          value: directory,
        }}
        initialValue={directory}
        width="md"
      />

      <div className="file-upload-list-container">
        <ProFormUploadDragger
          name="excelFiles"
          label={
            <div className="upload-label-with-count">
              <span>文件上传</span>
              {value.length > 0 && <span className="upload-file-count">(已选择 {value.length} 个文件)</span>}
            </div>
          }
          max={max}
          fieldProps={{
            name: 'file',
            multiple: true,
            accept: '.xlsx,.xls,.csv,.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.bmp,.webp,.txt,.md',
            listType: 'text',
            fileList: value,
            onChange: handleFileChange,
            showUploadList: {
              showPreviewIcon: false,
              showRemoveIcon: false,
            },
            iconRender: file => getFileIcon(file.name),
            itemRender: customItemRender,
          }}
          title="点击或拖拽上传文件"
          description={
            <div>
              <div>支持Excel、Word、PDF、图片等常见格式文件</div>
              <div style={{ color: '#ff7875' }}>重复文件将自动覆盖为最新上传的版本</div>
            </div>
          }
        />

        {/* 添加文件列表为空时的提示 */}
        {value.length === 0 && (
          <div className="empty-file-list-tip">
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无已选择文件" style={{ image: { height: 40 } }} />
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default FileUploader;
