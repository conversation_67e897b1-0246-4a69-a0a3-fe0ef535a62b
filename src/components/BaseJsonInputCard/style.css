/* JSON编辑器样式 */
.json-editor-container {
  border-radius: 4px;
  overflow: hidden;
}

/* 错误行样式 */
.cm-errorLine {
  background-color: rgba(255, 77, 79, 0.2);
  text-decoration: wavy underline rgba(255, 77, 79, 0.6);
}

/* 错误行号样式 */
.cm-errorGutter {
  background-color: rgba(255, 77, 79, 0.3);
  color: #ff4d4f !important;
  font-weight: bold;
}

/* 错误高亮动画 */
.cm-errorLine-highlight {
  animation: errorHighlight 1.5s ease-in-out;
}

@keyframes errorHighlight {
  0% {
    background-color: rgba(255, 77, 79, 0.2);
  }
  50% {
    background-color: rgba(255, 77, 79, 0.5);
  }
  100% {
    background-color: rgba(255, 77, 79, 0.2);
  }
}