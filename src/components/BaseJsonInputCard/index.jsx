/* global chrome */

import React, { useEffect, useRef, useState } from 'react';
import { Button, Space, Alert, message, Tooltip, Tag } from 'antd';
import {
  ExperimentOutlined,
  UploadOutlined,
  SaveOutlined,
  CheckCircleOutlined,
  FormatPainterOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import CollapsibleCard from '@/components/CollapsibleCard';
import JsonHistoryPanel from '@/components/JsonHistoryPanel';
import CodeMirror from '@uiw/react-codemirror';
import { json } from '@codemirror/lang-json';
import { oneDark } from '@codemirror/theme-one-dark';
import './style.css';

// 分析JSON字符串，找出语法错误的位置
const findJsonErrorLine = jsonString => {
  if (!jsonString) return null;

  try {
    // 如果能解析，说明没有错误
    JSON.parse(jsonString);
    return null;
  } catch (error) {
    // 尝试从错误信息中提取行号
    const lineMatch = /line (\d+)/i.exec(error.message);
    if (lineMatch) {
      return {
        line: parseInt(lineMatch[1], 10),
        message: error.message,
      };
    }

    // 如果错误信息中没有行号，尝试从位置信息中推断
    const positionMatch = /position (\d+)/i.exec(error.message);
    if (positionMatch) {
      const errorPosition = parseInt(positionMatch[1], 10);

      // 计算行号
      const lines = jsonString.substring(0, errorPosition).split('\n');
      return {
        line: lines.length,
        column: lines[lines.length - 1].length + 1,
        message: error.message,
      };
    }

    return {
      message: error.message,
    };
  }
};

const BaseJsonInputCard = ({
  // 必需属性
  title = 'JSON输入',
  storageKey = 'json_input',
  jsonInput = '',
  jsonData = null,
  jsonError = null,
  handleJsonChange,
  loadExampleJson,
  triggerFileUpload,
  fileInputRef,
  inputCardCollapsed = false,
  toggleInputCardCollapse,

  // 可选属性
  downloadFileName = timestamp => `data_${timestamp}.json`,
  tipMessage = '编辑器会自动保存和填充上次编辑的数据',
  successMessage = data => `JSON格式正确${data ? `，共发现 ${Object.keys(data).length} 个项目` : ''}`,
  customValidationError = null,
  validateData = null,
  height = '500px',
  placeholder = '请输入JSON格式的数据，或使用上方的按钮加载示例/上传JSON文件',
  extraActions = null,

  // 历史记录相关
  enableHistory = false,
  configType = null,
}) => {
  const editorRef = useRef(null);
  const [errorInfo, setErrorInfo] = useState(null);
  const [validationError, setValidationError] = useState(null);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();

  // 保存JSON文件
  const saveJsonToFile = jsonString => {
    if (!jsonString.trim()) {
      messageApi.error('没有可保存的JSON数据');
      return;
    }

    try {
      // 尝试解析JSON以验证格式是否正确
      JSON.parse(jsonString);

      // 创建Blob对象
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      // 创建下载链接
      const a = document.createElement('a');
      a.href = url;
      a.download = downloadFileName(Date.now());
      document.body.appendChild(a);
      a.click();

      // 清理
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      messageApi.success('JSON数据已保存');
    } catch (error) {
      messageApi.error(`保存失败: ${error.message}`);
    }
  };

  // 保存到Chrome存储
  const saveToStorage = data => {
    if (chrome?.storage?.local) {
      chrome.storage.local.set({ [storageKey]: data });
    } else {
      // 在非Chrome扩展环境下使用localStorage
      try {
        localStorage.setItem(storageKey, data);
      } catch (e) {
        console.warn('无法保存到本地存储:', e);
      }
    }
  };

  // 从Chrome存储加载
  const loadFromStorage = callback => {
    if (chrome?.storage?.local) {
      chrome.storage.local.get([storageKey], result => {
        if (result[storageKey]) {
          callback(result[storageKey]);
        }
      });
    } else {
      // 在非Chrome扩展环境下使用localStorage
      try {
        const savedData = localStorage.getItem(storageKey);
        if (savedData) {
          callback(savedData);
        }
      } catch (e) {
        console.warn('无法从本地存储读取:', e);
      }
    }
  };

  // 检查JSON错误位置
  useEffect(() => {
    if (jsonError && jsonInput) {
      const error = findJsonErrorLine(jsonInput);
      setErrorInfo(error);

      // 在编辑器中标记错误位置
      if (error && error.line) {
        // 延迟一点执行，确保编辑器已经渲染
        setTimeout(() => {
          try {
            // 在文档中查找编辑器容器
            const editorContainer = document.querySelector('.json-editor-container');
            if (!editorContainer) {
              console.error('找不到编辑器容器');
              return;
            }

            // 查找所有行
            const lineElements = editorContainer.querySelectorAll('.cm-line');

            // 重置所有行的样式
            if (lineElements && lineElements.length > 0) {
              lineElements.forEach(line => {
                line.classList.remove('cm-errorLine');
              });

              // 标记错误行
              if (error.line <= lineElements.length) {
                const errorLine = lineElements[error.line - 1];
                if (errorLine) {
                  errorLine.classList.add('cm-errorLine');
                  errorLine.setAttribute('title', error.message);
                }
              }
            }

            // 同样处理行号
            const gutterElements = editorContainer.querySelectorAll('.cm-gutterElement');
            if (gutterElements && gutterElements.length > 0) {
              gutterElements.forEach(gutter => {
                gutter.classList.remove('cm-errorGutter');
              });

              if (error.line <= gutterElements.length) {
                const errorGutter = gutterElements[error.line - 1];
                if (errorGutter) {
                  errorGutter.classList.add('cm-errorGutter');
                }
              }
            }
          } catch (e) {
            console.error('标记错误行失败:', e);
          }
        }, 100);
      }
    } else {
      setErrorInfo(null);

      // 清除错误标记
      try {
        const editorContainer = document.querySelector('.json-editor-container');
        if (editorContainer) {
          const lineElements = editorContainer.querySelectorAll('.cm-line');
          if (lineElements && lineElements.length > 0) {
            lineElements.forEach(line => {
              line.classList.remove('cm-errorLine');
            });
          }

          const gutterElements = editorContainer.querySelectorAll('.cm-gutterElement');
          if (gutterElements && gutterElements.length > 0) {
            gutterElements.forEach(gutter => {
              gutter.classList.remove('cm-errorGutter');
            });
          }
        }
      } catch (e) {
        console.error('清除错误标记失败:', e);
      }
    }
  }, [jsonError, jsonInput]);

  // 验证数据格式
  useEffect(() => {
    if (jsonData && !jsonError && validateData) {
      const error = validateData(jsonData);
      setValidationError(error);
    } else {
      setValidationError(null);
    }
  }, [jsonData, jsonError, validateData]);

  // 使用自定义验证错误
  useEffect(() => {
    if (customValidationError !== undefined) {
      setValidationError(customValidationError);
    }
  }, [customValidationError]);

  // 组件挂载时从存储中加载上次的内容
  useEffect(() => {
    loadFromStorage(savedData => {
      handleJsonChange(savedData);
    });
  }, []);

  // 处理JSON输入变化，并保存到存储
  const handleInputChange = value => {
    handleJsonChange(value);
    // 延迟保存，避免频繁写入
    setTimeout(() => {
      saveToStorage(value);
    }, 500);
  };

  // 处理示例加载
  const handleLoadExample = () => {
    try {
      messageApi.loading({ content: '正在加载示例数据...', key: 'loadExample' });
      // 调用传入的加载示例函数
      loadExampleJson()
        .then(() => {
          messageApi.success({ content: '示例数据加载成功', key: 'loadExample' });
        })
        .catch(error => {
          console.error('加载示例失败:', error);
          messageApi.error({
            content: `加载示例失败: ${error.message}`,
            key: 'loadExample',
          });
        });
    } catch (error) {
      console.error('加载示例失败:', error);
      messageApi.error({
        content: `加载示例失败: ${error.message}`,
        key: 'loadExample',
      });
    }
  };

  // 格式化JSON数据
  const formatJson = () => {
    if (!jsonInput?.trim()) {
      messageApi.error('没有可格式化的JSON数据');
      return;
    }

    try {
      // 解析并重新格式化JSON
      const parsedJson = JSON.parse(jsonInput);
      const formattedJson = JSON.stringify(parsedJson, null, 2);
      handleJsonChange(formattedJson);
      messageApi.success('JSON格式化成功');
    } catch (error) {
      messageApi.error(`格式化失败: ${error.message}`);
    }
  };

  // 跳转到错误行
  const scrollToErrorLine = () => {
    if (!errorInfo || !errorInfo.line) return;

    try {
      const editorContainer = document.querySelector('.json-editor-container');
      if (!editorContainer) {
        messageApi.error('找不到编辑器容器，无法跳转到错误位置');
        return;
      }

      const lineElements = editorContainer.querySelectorAll('.cm-line');

      if (lineElements && lineElements.length > 0 && errorInfo.line <= lineElements.length) {
        const errorLine = lineElements[errorInfo.line - 1];
        if (errorLine) {
          // 使用滚动到视图功能
          errorLine.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });

          // 添加临时高亮效果
          errorLine.classList.add('cm-errorLine-highlight');
          setTimeout(() => {
            errorLine.classList.remove('cm-errorLine-highlight');
          }, 2000);

          messageApi.info(`已定位到第 ${errorInfo.line} 行`);
        } else {
          messageApi.error(`无法定位到第 ${errorInfo.line} 行`);
        }
      } else {
        messageApi.error(`编辑器中找不到相应的行 (总行数: ${lineElements ? lineElements.length : 0})`);
      }
    } catch (e) {
      console.error('滚动到错误行失败:', e);
      messageApi.error('跳转到错误位置失败');
    }
  };

  // 打开历史记录面板
  const handleOpenHistory = () => {
    if (!enableHistory || !configType) {
      messageApi.error('历史记录功能未启用或配置类型未设置');
      return;
    }
    setHistoryVisible(true);
  };

  // 从历史记录加载配置
  const handleLoadFromHistory = configJson => {
    console.log('BaseJsonInputCard: 从历史记录加载配置:', {
      configJsonLength: configJson?.length,
      configJsonSample: configJson?.slice(0, 100),
      storageKey: storageKey,
    });

    console.log('configJson', configJson);

    if (configJson && typeof configJson === 'string') {
      // 更新编辑器内容
      handleJsonChange(configJson);

      // 立即保存到持久化存储
      try {
        localStorage.setItem(storageKey, configJson);
        console.log('BaseJsonInputCard: 配置已保存到持久化存储:', storageKey);
      } catch (error) {
        console.error('BaseJsonInputCard: 保存到持久化存储失败:', error);
      }

      messageApi.success('配置已从历史记录加载并保存');
    } else {
      console.error('BaseJsonInputCard: 无效的配置数据:', configJson);
      messageApi.error('配置数据格式错误');
    }
  };

  return (
    <>
      {contextHolder}
      <CollapsibleCard
        title={title}
        collapsed={inputCardCollapsed}
        onCollapse={toggleInputCardCollapse}
        className="input-card"
        extra={
          <Space>
            {extraActions && extraActions}
            <Button icon={<ExperimentOutlined />} onClick={handleLoadExample}>
              加载示例
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: 'none' }}
              accept=".json"
              onChange={e => {
                const file = e.target.files[0];
                if (file) {
                  triggerFileUpload(file);
                }
              }}
            />
            <Button
              icon={<UploadOutlined />}
              onClick={() => fileInputRef.current?.click()}
              style={{ outline: 'none' }}
              className="upload-button-no-focus"
              onMouseUp={e => e.currentTarget.blur()}
            >
              上传JSON
            </Button>
            <Button
              icon={<FormatPainterOutlined />}
              onClick={formatJson}
              disabled={!jsonInput || jsonError}
              style={{ outline: 'none' }}
              className="upload-button-no-focus"
              onMouseUp={e => e.currentTarget.blur()}
            >
              格式化JSON
            </Button>
            {enableHistory && configType && (
              <Button
                icon={<HistoryOutlined />}
                onClick={handleOpenHistory}
                style={{ outline: 'none' }}
                className="upload-button-no-focus"
                onMouseUp={e => e.currentTarget.blur()}
              >
                历史记录
              </Button>
            )}
            <Button icon={<SaveOutlined />} onClick={() => saveJsonToFile(jsonInput)} disabled={!jsonInput || jsonError}>
              保存JSON
            </Button>
          </Space>
        }
      >
        <div style={{ marginBottom: 16 }}>
          <Tooltip title="会自动记住你上次填写的内容">
            <Tag color="processing" icon={<InfoCircleOutlined />}>
              自动保存
            </Tag>
          </Tooltip>
          <span
            style={{
              fontSize: '14px',
              color: 'rgba(0, 0, 0, 0.65)',
              marginLeft: 8,
            }}
          >
            {React.isValidElement(tipMessage) ? tipMessage : <span>{tipMessage}</span>}
          </span>
        </div>

        {jsonError && (
          <Alert
            message={
              <Space>
                <WarningOutlined style={{ color: '#ff4d4f' }} />
                <span>JSON格式错误</span>
                {errorInfo && errorInfo.line && <span>（错误位置: 第 {errorInfo.line} 行）</span>}
              </Space>
            }
            description={jsonError}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
            action={
              errorInfo &&
              errorInfo.line && (
                <Button size="small" type="primary" danger onClick={scrollToErrorLine}>
                  跳转到错误位置
                </Button>
              )
            }
          />
        )}

        {validationError && !jsonError && (
          <Alert
            message={
              <Space>
                <WarningOutlined style={{ color: '#faad14' }} />
                <span>数据格式验证失败</span>
              </Space>
            }
            description={validationError}
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <CodeMirror
          ref={editorRef}
          value={jsonInput || ''}
          height={height}
          extensions={[json()]}
          theme={oneDark}
          onChange={handleInputChange}
          className="json-editor-container"
          style={{ marginTop: 16 }}
          minHeight="1000px"
          placeholder={placeholder}
          basicSetup={{
            lineNumbers: true,
            highlightActiveLineGutter: true,
            foldGutter: true,
            dropCursor: true,
            allowMultipleSelections: true,
            indentOnInput: true,
            bracketMatching: true,
            closeBrackets: true,
            autocompletion: true,
            rectangularSelection: true,
            highlightActiveLine: true,
            highlightSelectionMatches: true,
            closeBracketsKeymap: true,
            searchKeymap: true,
          }}
        />
        <div style={{ marginTop: 16 }}>
          {jsonData && !jsonError && !validationError ? (
            <Alert
              message={
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <span>{typeof successMessage === 'function' ? successMessage(jsonData) : successMessage}</span>
                </Space>
              }
              type="success"
            />
          ) : null}
        </div>
      </CollapsibleCard>

      {/* 历史记录面板 */}
      {enableHistory && configType && (
        <JsonHistoryPanel
          configType={configType}
          visible={historyVisible}
          onClose={() => setHistoryVisible(false)}
          onLoadConfig={handleLoadFromHistory}
        />
      )}
    </>
  );
};

export default BaseJsonInputCard;
