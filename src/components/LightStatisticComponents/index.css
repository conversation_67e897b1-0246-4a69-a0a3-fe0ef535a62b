/* 轻量级StatisticCard组件样式 */
.light-statistic {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  height: 100%;
}

.light-statistic-title {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  margin-bottom: 8px;
}

.light-statistic-content {
  display: flex;
  align-items: baseline;
}

.light-statistic-value {
  font-size: 30px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
}

.light-statistic-prefix,
.light-statistic-suffix {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.45);
  margin: 0 4px;
}

.light-statistic-group {
  display: flex;
  width: 100%;
  gap: 16px;
}

.light-statistic-group-row {
  flex-direction: row;
}

.light-statistic-group-column {
  flex-direction: column;
}

/* 轻量级ProList组件样式 */
.light-list {
  background-color: #fff;
  border-radius: 8px;
}

.light-list-bordered {
  border: 1px solid #f0f0f0;
}

.light-list-item {
  padding: 12px 24px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.light-list-item:hover {
  background-color: #f5f5f5;
}

.light-list-item:last-child {
  border-bottom: none;
}

/* 成功/失败样式，对应原ResultDisplay组件中的样式 */
.light-list-item.success-item {
  background-color: rgba(82, 196, 26, 0.1);
}

.light-list-item.error-item {
  background-color: rgba(255, 77, 79, 0.1);
}

/* 暗黑模式适配 */
@media (prefers-color-scheme: dark) {
  .light-statistic {
    background-color: #1f1f1f;
    box-shadow: 0 1px 2px rgba(255, 255, 255, 0.03);
  }

  .light-statistic-title {
    color: rgba(255, 255, 255, 0.65);
  }

  .light-statistic-value {
    color: rgba(255, 255, 255, 0.85);
  }

  .light-statistic-prefix,
  .light-statistic-suffix {
    color: rgba(255, 255, 255, 0.45);
  }

  .light-list {
    background-color: #1f1f1f;
  }

  .light-list-bordered {
    border: 1px solid #303030;
  }

  .light-list-item {
    border-bottom: 1px solid #303030;
  }

  .light-list-item:hover {
    background-color: #2a2a2a;
  }

  .light-list-item.success-item {
    background-color: rgba(82, 196, 26, 0.15);
  }

  .light-list-item.error-item {
    background-color: rgba(255, 77, 79, 0.15);
  }
} 