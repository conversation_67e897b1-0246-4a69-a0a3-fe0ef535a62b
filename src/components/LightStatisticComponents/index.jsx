import React from "react";
import { List } from "antd";
import "./index.css";

/**
 * 轻量级StatisticCard组件
 * 替代@ant-design/pro-components中的StatisticCard，大幅减小体积
 */
export const LightStatistic = ({ statistic }) => {
  const { title, value, valueStyle, suffix, prefix } = statistic || {};

  return (
    <div className="light-statistic">
      {title && <div className="light-statistic-title">{title}</div>}
      <div className="light-statistic-content">
        {prefix && <span className="light-statistic-prefix">{prefix}</span>}
        <span className="light-statistic-value" style={valueStyle}>
          {value}
        </span>
        {suffix && <span className="light-statistic-suffix">{suffix}</span>}
      </div>
    </div>
  );
};

/**
 * 轻量级StatisticCard.Group组件
 */
LightStatistic.Group = ({ children, direction = "row" }) => {
  return (
    <div className={`light-statistic-group light-statistic-group-${direction}`}>
      {children}
    </div>
  );
};

/**
 * 轻量级ProList组件
 * 替代@ant-design/pro-components中的ProList，大幅减小体积
 */
export const LightList = ({
  dataSource,
  rowKey,
  metas,
  bordered,
  rowClassName,
}) => {
  return (
    <List
      dataSource={dataSource}
      rowKey={rowKey}
      className={`light-list ${bordered ? "light-list-bordered" : ""}`}
      renderItem={(item) => {
        // 处理自定义渲染函数
        const title = metas?.title?.render
          ? metas.title.render(item[metas.title.dataIndex], item)
          : item[metas.title?.dataIndex];

        const description = metas?.description?.render
          ? metas.description.render(item[metas.description?.dataIndex], item)
          : item[metas.description?.dataIndex];

        const avatar = metas?.avatar?.render
          ? metas.avatar.render(item[metas.avatar?.dataIndex], item)
          : item[metas.avatar?.dataIndex];

        const itemClassName = rowClassName ? rowClassName(item) : "";

        return (
          <List.Item className={`light-list-item ${itemClassName}`}>
            <List.Item.Meta
              avatar={avatar}
              title={title}
              description={description}
            />
          </List.Item>
        );
      }}
    />
  );
};
