import React, { forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { ProFormList, ProCard, ProFormText, ProFormSelect, ProFormSwitch } from '@ant-design/pro-components';
import { Typography, Space, Row, Col, Form } from 'antd';
import { GlobalOutlined, CloseCircleOutlined, TeamOutlined, UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { staticDomainOptions } from '../constants';
import { scrollToSiteItem } from '../../utils/domUtils';

const { Text } = Typography;

// 将原始选项保存为常量，以便后续处理
const originOptions = [...staticDomainOptions];

// 账号类型选项
const accountTypeOptions = [
  { label: '测试账号1', value: 'testAdmin' },
  { label: '测试账号2', value: 'testAdmin2' },
];

// 预设账号信息
const accountPresets = {
  // 测试账号1
  testAdmin: {
    username: '+***********',
    password: 'Hal159357001.+',
    captchaCode: '123123',
  },
  // 测试账号2
  testAdmin2: {
    username: '+*************',
    password: 'GAC*UOP!',
    captchaCode: '123123',
  },
};

const SiteFormList = forwardRef(({ initialValues = [], formRef }, ref) => {
  const formListRef = useRef();

  // 使用Form.useWatch监听表单值变化，提供默认值避免初始渲染时的空值
  const sitesValues = Form.useWatch('sites', formRef) || initialValues;

  useImperativeHandle(ref, () => ({
    ...(formListRef.current ?? {}),
  }));

  // 设置账号信息的辅助函数
  const setAccountInfo = (index, accountType) => {
    if (!accountType || !accountPresets[accountType]) return;

    const preset = accountPresets[accountType];

    // 使用父组件传递的formRef
    const formInstance = formRef;
    if (!formInstance) return;

    // 直接获取当前表单数组值
    const currentSites = formInstance.getFieldValue('sites') || [];

    // 如果索引有效，更新特定项
    if (index >= 0 && index < currentSites.length) {
      const newSites = [...currentSites];
      newSites[index] = {
        ...newSites[index],
        username: preset.username,
        password: preset.password,
        captchaCode: preset.captchaCode,
      };

      // 更新整个 sites 数组
      formInstance.setFieldsValue({
        sites: newSites,
      });
    }
  };

  // 在组件挂载和sitesValues变化时，确保每个站点的账号信息正确设置
  useEffect(() => {
    if (sitesValues?.length > 0) {
      // 处理每个站点的账号信息
      sitesValues.forEach((site, index) => {
        // 检查是否需要设置字段值（如果当前值为undefined）
        if (site?.accountType && (!site.username || !site.password || !site.captchaCode)) {
          setAccountInfo(index, site.accountType);
        }
      });
    }
  }, [sitesValues]);

  return (
    <ProFormList
      name="sites"
      initialValue={
        initialValues.length > 0
          ? initialValues.map(item => ({
              ...item,
              enabled: item.enabled !== false, // 默认启用
            }))
          : [{ enabled: true }]
      }
      creatorButtonProps={false}
      deleteIconProps={{
        Icon: CloseCircleOutlined,
        tooltipText: '删除此站点配置',
      }}
      onAfterRemove={() => {
        // 在删除后，滚动到列表的最后一项(实际上是被删除项的前一项)
        // 获取最新的sites数据
        const currentSites = formRef?.getFieldValue('sites') || [];

        // 如果存在formRef且列表不为空，滚动到最后一项
        if (formRef && currentSites.length > 0) {
          // 由于删除已完成，此时列表长度已经减少，最后一项即为原删除项的前一项
          scrollToSiteItem({
            target: 'index',
            index: currentSites.length - 1, // 滚动到当前列表的最后一项
          });
        }
      }}
      copyIconProps={false}
      actionRef={formListRef}
      itemRender={({ listDom, action }, { index }) => {
        // 从表单中获取当前项的enabled值
        const isEnabled = formRef && formRef.getFieldValue(['sites', index, 'enabled']) !== false;

        return (
          <ProCard
            key={index}
            title={
              <Space>
                <Text strong style={{ opacity: isEnabled ? 1 : 0.5 }}>
                  站点 {index + 1}
                </Text>
                {!isEnabled && <Text type="secondary">(已禁用)</Text>}
              </Space>
            }
            headerBordered
            bordered
            style={{
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
              opacity: isEnabled ? 1 : 0.6,
              marginBottom: 16,
            }}
            extra={
              <Space
                direction="horizontal"
                align="center"
                styles={{
                  item: {
                    height: 32,
                  },
                }}
              >
                <ProFormSwitch
                  name="enabled"
                  tooltip="默认启用，禁用后将不会处理该站点"
                  fieldProps={{
                    checkedChildren: '启用',
                    unCheckedChildren: '禁用',
                    onChange: () => {
                      // 手动触发表单渲染更新
                      setTimeout(() => {
                        formRef?.setFields([
                          {
                            name: ['sites', index],
                            errors: [],
                          },
                        ]);
                      }, 0);
                    },
                  }}
                  initialValue={true}
                />
                {action}
              </Space>
            }
          >
            {listDom}
          </ProCard>
        );
      }}
    >
      {(_, index) => {
        // 根据当前表单值动态生成options
        const disabledOptions = [...originOptions].map(option => {
          // 检查该选项是否被其他表单项选中
          const isDisabled = sitesValues?.some((site, siteIndex) => siteIndex !== index && site?.domain === option.value);

          return {
            ...option,
            disabled: isDisabled,
          };
        });

        // 检查当前项是否被禁用
        const isCurrentItemDisabled = sitesValues[index]?.enabled === false;

        return (
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <ProFormSelect
                name="domain"
                label="站点名称"
                placeholder="请选择站点名称"
                options={disabledOptions}
                fieldProps={{
                  prefix: <GlobalOutlined className="site-form-item-icon" />,
                  size: 'middle',
                  showSearch: true,
                  allowClear: true,
                  disabled: isCurrentItemDisabled,
                }}
                rules={[{ required: true, message: '请选择站点名称' }]}
              />
            </Col>

            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <ProFormSelect
                name="accountType"
                label="账号类型"
                placeholder="请选择账号类型"
                tooltip="测试账号将自动填充账号、密码、验证码信息"
                options={accountTypeOptions}
                fieldProps={{
                  prefix: <TeamOutlined className="site-form-item-icon" />,
                  size: 'middle',
                  disabled: isCurrentItemDisabled,
                }}
                rules={[{ required: true, message: '请选择账号类型' }]}
                onChange={value => {
                  // 当账号类型变化时，设置相应的账号信息
                  if (value) {
                    setAccountInfo(index, value);
                  }
                }}
                disabled={isCurrentItemDisabled}
              />
            </Col>

            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <ProFormText
                name="username"
                label="账户"
                placeholder="请输入账户"
                fieldProps={{
                  prefix: <UserOutlined className="site-form-item-icon" />,
                  size: 'middle',
                  disabled: isCurrentItemDisabled,
                }}
                rules={[{ required: true, message: '请输入账户' }]}
              />
            </Col>

            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <ProFormText.Password
                name="password"
                label="密码"
                placeholder="请输入密码"
                fieldProps={{
                  size: 'middle',
                  disabled: isCurrentItemDisabled,
                  prefix: <LockOutlined className="site-form-item-icon" />,
                }}
                rules={[{ required: true, message: '请输入密码' }]}
              />
            </Col>

            <Col xs={24} sm={24} md={8} lg={8} xl={8}>
              <ProFormText
                name="captchaCode"
                label="验证码"
                placeholder="请输入验证码"
                fieldProps={{
                  prefix: <SafetyOutlined className="site-form-item-icon" />,
                  size: 'middle',
                  disabled: isCurrentItemDisabled,
                }}
                rules={[{ required: true, message: '请输入验证码' }]}
              />
            </Col>
          </Row>
        );
      }}
    </ProFormList>
  );
});

export default SiteFormList;
