// SiteConfigTable 工具函数

/**
 * 验证导入数据
 * @param {Object} data 导入的JSON数据
 * @returns {string} 错误信息，如果没有错误则返回空字符串
 */
export const validateImportData = data => {
  if (!data || typeof data !== 'object') {
    return '导入数据格式无效';
  }

  if (!Array.isArray(data.sites)) {
    return '导入数据缺少有效的站点配置数组';
  }

  for (const site of data.sites) {
    if (!site.domain) {
      return '站点配置缺少必需的域名字段';
    }
    if (!site.accountType) {
      return '站点配置缺少必需的账号类型字段';
    }
  }

  return '';
};

/**
 * 设置账号信息的辅助函数
 * @param {Object} values 表单值
 * @param {string} accountType 账号类型
 * @param {Object} accountPresets 账号预设配置
 * @returns {Object} 更新后的表单值
 */
export const setAccountInfo = (values, accountType, accountPresets) => {
  if (!accountType || !accountPresets[accountType]) return values;
  const preset = accountPresets[accountType];
  return {
    ...values,
    username: preset.username,
    password: preset.password,
    captchaCode: preset.captchaCode,
  };
};

/**
 * 更新站点状态（启用/禁用）
 * @param {Object} record 站点记录
 * @param {number} index 记录索引
 * @param {Array} value 站点配置数组
 * @param {Function} onChange 变更回调
 * @param {Object} outerFormRef 外部表单引用
 * @param {boolean} enabled 是否启用
 */
export const updateSiteStatus = (record, index, value, onChange, outerFormRef, enabled) => {
  // 根据domain找到原始数据中的真实索引
  const realIndex = value.findIndex(item => item.domain === record.domain);

  // 如果找到了对应的记录，使用真实索引进行更新
  if (realIndex !== -1) {
    const newValue = [...value];
    newValue[realIndex] = { ...record, enabled };
    onChange?.(newValue);

    // 同步更新外部表单
    syncFormValue(outerFormRef, ['sites', realIndex, 'enabled'], enabled);
  } else {
    // 使用表格行索引作为后备方案
    const newValue = [...value];
    newValue[index] = { ...record, enabled };
    onChange?.(newValue);

    // 同步更新外部表单
    syncFormValue(outerFormRef, ['sites', index, 'enabled'], enabled);
  }
};

/**
 * 导出站点配置为JSON文件
 * @param {Array} sites 站点配置数组
 * @param {Function} messageCallback 消息回调函数
 */
export const exportSiteConfig = (sites, messageCallback) => {
  try {
    // 创建要导出的数据
    const exportData = {
      sites,
      exportTime: new Date().toISOString(),
      version: '1.0',
    };

    // 转换为JSON字符串
    const jsonStr = JSON.stringify(exportData, null, 2);

    // 创建Blob对象
    const blob = new Blob([jsonStr], { type: 'application/json' });

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `站点配置_${new Date().toISOString().split('T')[0]}.json`;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    if (messageCallback) messageCallback('success', '导出站点配置成功');
  } catch (error) {
    console.error('导出站点配置失败:', error);
    if (messageCallback) messageCallback('error', `导出站点配置失败: ${error.message}`);
  }
};

/**
 * 获取表格数据，处理key
 * @param {Array} sites 站点配置数组
 * @returns {Array} 处理后的表格数据
 */
export const getTableData = sites => {
  return sites.map((item, index) => ({
    ...item,
    key: index.toString(),
  }));
};

/**
 * 获取行样式类名
 * @param {Object} record 行记录
 * @returns {string} 行样式类名
 */
export const getRowClassName = record => {
  return '';
};

/**
 * 同步更新外部表单数据
 * @param {Object} formRef 表单引用
 * @param {string|Array} path 表单字段路径
 * @param {any} value 要设置的值
 */
export const syncFormValue = (formRef, path, value) => {
  if (!formRef) return;
  formRef.setFieldValue(path, value);
};

/**
 * 批量同步更新外部表单数据
 * @param {Object} formRef 表单引用
 * @param {Array} keyIndices 要更新的索引数组
 * @param {string} fieldName 字段名称
 * @param {any} value 要设置的值
 */
export const batchSyncFormValues = (formRef, keyIndices, fieldName, value) => {
  if (!formRef) return;

  keyIndices.forEach(key => {
    const index = parseInt(key);
    if (!isNaN(index)) {
      formRef.setFieldValue(['sites', index, fieldName], value);
    }
  });
};

/**
 * 更新整个站点配置数组
 * @param {Object} formRef 表单引用
 * @param {Array} sites 站点配置数组
 */
export const updateAllSites = (formRef, sites) => {
  if (!formRef) return;
  formRef.setFieldValue('sites', sites);
};

/**
 * 批量更新站点状态
 * @param {Array} selectedIndices 选中的索引数组
 * @param {Array} value 站点配置数组
 * @param {Function} onChange 变更回调
 * @param {Object} outerFormRef 外部表单引用
 * @param {boolean} enabled 是否启用
 */
export const batchUpdateSiteStatus = (selectedIndices, value, onChange, outerFormRef, enabled) => {
  const newValue = [...value];

  // 记录已更新的站点真实索引
  const updatedRealIndices = [];

  selectedIndices.forEach(key => {
    const index = parseInt(key);
    if (!isNaN(index) && newValue[index]) {
      const record = newValue[index];

      // 根据domain找到原始数据中的真实索引
      const realIndex = value.findIndex(item => item.domain === record.domain);

      // 如果找到了对应的记录，使用真实索引进行更新
      if (realIndex !== -1 && !updatedRealIndices.includes(realIndex)) {
        newValue[realIndex] = { ...record, enabled };
        updatedRealIndices.push(realIndex);
      } else if (realIndex === -1) {
        // 使用表格行索引作为后备方案
        newValue[index] = { ...record, enabled };
      }
    }
  });

  // 一次性更新所有站点
  onChange?.(newValue);

  // 同步更新外部表单
  if (outerFormRef) {
    // 使用已经找到的真实索引更新表单
    updatedRealIndices.forEach(realIndex => {
      outerFormRef.setFieldValue(['sites', realIndex, 'enabled'], enabled);
    });

    // 对于未找到真实索引的记录，使用原始索引
    selectedIndices.forEach(key => {
      const index = parseInt(key);
      if (!isNaN(index)) {
        const record = value[index];
        const realIndex = value.findIndex(item => item.domain === record?.domain);

        // 如果没有找到真实索引或者该索引尚未更新，使用原始索引
        if (realIndex === -1 || !updatedRealIndices.includes(realIndex)) {
          outerFormRef.setFieldValue(['sites', index, 'enabled'], enabled);
        }
      }
    });
  }
};
