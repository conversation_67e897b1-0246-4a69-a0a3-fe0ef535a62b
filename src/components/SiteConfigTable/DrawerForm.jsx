import React from 'react';
import { DrawerForm, ProFormText, ProFormSelect, ProFormSwitch } from '@ant-design/pro-components';
import { UserOutlined, LockOutlined, SafetyOutlined, GlobalOutlined, TeamOutlined } from '@ant-design/icons';
import { Space, Tag } from 'antd';

/**
 * 站点配置抽屉表单组件
 *
 * @param {Object} props 组件属性
 * @param {boolean} props.visible 抽屉是否可见
 * @param {Function} props.onVisibleChange 抽屉可见性变更回调
 * @param {Object} props.editingRecord 当前编辑的记录
 * @param {Function} props.onFinish 表单提交完成回调
 * @param {Object} props.formRef 表单引用
 * @param {Array} props.staticDomainOptions 站点域名选项
 * @param {Array} props.accountTypeOptions 账号类型选项
 * @param {Array} props.predefinedTags 预定义标签
 * @param {Object} props.accountPresets 账号预设配置
 * @param {Array} props.allSites 所有站点配置，用于检查已选择的站点
 * @returns {JSX.Element} 抽屉表单组件
 */
const SiteConfigDrawerForm = ({
  visible,
  onVisibleChange,
  editingRecord,
  onFinish,
  formRef,
  staticDomainOptions,
  accountTypeOptions,
  predefinedTags,
  accountPresets,
  allSites = [],
}) => {
  // 设置账号信息的辅助函数
  const setAccountInfo = (values, accountType) => {
    if (!accountType || !accountPresets[accountType]) return values;
    const preset = accountPresets[accountType];
    return {
      ...values,
      username: preset.username,
      password: preset.password,
      captchaCode: preset.captchaCode,
      enablePasswordEncryption: preset.enablePasswordEncryption !== false, // 默认启用加密
    };
  };

  return (
    <DrawerForm
      title={editingRecord ? '编辑站点配置' : '新增站点配置'}
      formRef={formRef}
      open={visible}
      onOpenChange={onVisibleChange}
      initialValues={editingRecord ? { ...editingRecord } : { enabled: true, enablePasswordEncryption: true }}
      onFinish={onFinish}
      drawerProps={{
        destroyOnClose: true,
        width: 600,
      }}
    >
      <ProFormSwitch
        name="enabled"
        label="启用状态"
        initialValue={true}
        fieldProps={{
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        }}
      />

      <ProFormSelect
        name="domain"
        label="站点名称"
        rules={[{ required: true, message: '请选择站点名称' }]}
        options={staticDomainOptions.map(option => {
          // 检查该选项是否被其他记录选中
          const isDisabled = editingRecord
            ? allSites.some(site => site.domain === option.value && site.domain !== editingRecord.domain)
            : allSites.some(site => site.domain === option.value);

          return {
            ...option,
            disabled: isDisabled,
          };
        })}
        fieldProps={{
          showSearch: true,
          optionItemRender: item => (
            <Space>
              <GlobalOutlined />
              {item.label}
              <Tag color={predefinedTags.find(tag => tag.value === item.tag)?.color}>{item.tag}</Tag>
            </Space>
          ),
        }}
      />

      <ProFormSelect
        name="accountType"
        label="账号类型"
        rules={[{ required: true, message: '请选择账号类型' }]}
        options={accountTypeOptions}
        tooltip="测试账号将自动填充账号、密码、验证码信息"
        fieldProps={{
          optionItemRender: item => (
            <Space>
              <TeamOutlined />
              {item.label}
            </Space>
          ),
          onChange: value => {
            if (value && accountPresets[value]) {
              const currentValues = formRef.current?.getFieldsValue();
              const updatedValues = setAccountInfo(currentValues, value);
              formRef.current?.setFieldsValue(updatedValues);
            }
          },
        }}
      />

      <ProFormText
        name="username"
        label="账户"
        rules={[{ required: true, message: '请输入账户' }]}
        fieldProps={{
          prefix: <UserOutlined />,
        }}
      />

      <ProFormText.Password
        name="password"
        label="密码"
        rules={[{ required: true, message: '请输入密码' }]}
        fieldProps={{
          prefix: <LockOutlined />,
        }}
      />

      <ProFormSwitch
        name="enablePasswordEncryption"
        label="开启密码加密"
        initialValue={true}
        tooltip="启用后将使用RSA公钥对密码进行加密传输"
        fieldProps={{
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        }}
      />

      <ProFormText
        name="captchaCode"
        label="验证码"
        rules={[{ required: true, message: '请输入验证码' }]}
        fieldProps={{
          prefix: <SafetyOutlined />,
        }}
      />
    </DrawerForm>
  );
};

export default SiteConfigDrawerForm;
