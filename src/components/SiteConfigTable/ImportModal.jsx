import React, { useEffect, useState } from 'react';
import { Modal, message, Button, Space } from 'antd';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { validateImportData, updateAllSites } from './utils';

/**
 * 站点配置导入模态框组件
 *
 * @param {Object} props 组件属性
 * @param {boolean} props.visible 模态框是否可见
 * @param {Function} props.onCancel 取消回调
 * @param {string} props.importText 导入的文本内容
 * @param {string} props.importError 导入错误信息
 * @param {Function} props.setImportError 设置导入错误回调
 * @param {Function} props.onChange 配置变更回调
 * @param {Object} props.outerFormRef 外部表单引用
 * @returns {JSX.Element} 导入模态框组件
 */
const ImportModal = ({ visible, onCancel, importText, importError, setImportError, onChange, outerFormRef }) => {
  const [parsedData, setParsedData] = useState(null);
  const [isConfirmStep, setIsConfirmStep] = useState(false);

  // 在组件挂载或importText变化时解析数据
  useEffect(() => {
    if (visible && importText) {
      try {
        console.log('解析导入数据');
        const importData = JSON.parse(importText);

        // 格式化数据确保符合要求
        let dataToValidate = importData;
        if (!dataToValidate.sites && Array.isArray(dataToValidate)) {
          console.log('导入的是纯数组，自动包装为sites结构');
          dataToValidate = { sites: dataToValidate };
        }

        const validationError = validateImportData(dataToValidate);

        if (validationError) {
          console.log('数据验证失败:', validationError);
          setImportError(validationError);
          setParsedData(null);
        } else {
          console.log('数据验证通过，站点数量:', dataToValidate.sites.length);
          setParsedData(dataToValidate);
          setImportError('');
        }
      } catch (error) {
        console.error('JSON解析错误:', error);
        setImportError(`JSON解析错误: ${error.message}`);
        setParsedData(null);
      }
    }
  }, [visible, importText, setImportError]);

  // 处理导入确认
  const handleConfirmImport = () => {
    setIsConfirmStep(true);
  };

  // 执行导入
  const handleImport = () => {
    if (!parsedData || !parsedData.sites) {
      message.error('没有有效的导入数据');
      return;
    }

    try {
      console.log('执行导入，更新内部状态');
      // 更新内部状态
      if (onChange) {
        onChange(parsedData.sites);
      }

      // 同步更新外部表单
      if (outerFormRef) {
        updateAllSites(outerFormRef, parsedData.sites);
      }

      // 关闭导入模态框
      onCancel();

      // 显示成功消息
      message.success(`成功导入${parsedData.sites.length}个站点配置`);
    } catch (error) {
      console.error('导入过程发生错误:', error);
      message.error(`导入失败: ${error.message}`);
    }
  };

  // 确认步骤内容
  const renderConfirmContent = () => (
    <div>
      <p style={{ marginBottom: 16 }}>
        <QuestionCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
        确定要导入{parsedData?.sites?.length || 0}个站点配置吗？此操作将覆盖之前的配置。
      </p>
      <Space>
        <Button onClick={() => setIsConfirmStep(false)}>返回查看</Button>
        <Button type="primary" onClick={handleImport}>
          确认导入
        </Button>
      </Space>
    </div>
  );

  // 预览步骤内容
  const renderPreviewContent = () => (
    <div>
      <p>请确认以下配置信息是否正确：</p>
      <div style={{ maxHeight: 400, overflow: 'auto', border: '1px solid #d9d9d9', padding: 8, backgroundColor: '#f5f5f5' }}>
        <pre>{importText}</pre>
      </div>
      {importError && (
        <div style={{ color: 'red', marginTop: 8 }}>
          <ExclamationCircleOutlined /> {importError}
        </div>
      )}
    </div>
  );

  return (
    <Modal
      title="导入站点配置"
      open={visible}
      onCancel={onCancel}
      width={700}
      footer={
        isConfirmStep
          ? null
          : [
              <Button key="cancel" onClick={onCancel} className="upload-button-no-focus">
                取消
              </Button>,
              <Button
                key="confirm"
                type="primary"
                className="upload-button-no-focus"
                onClick={handleConfirmImport}
                disabled={!parsedData || importError}
              >
                下一步
              </Button>,
            ]
      }
    >
      <div style={{ marginBottom: 16 }}>{isConfirmStep ? renderConfirmContent() : renderPreviewContent()}</div>
    </Modal>
  );
};

export default ImportModal;
