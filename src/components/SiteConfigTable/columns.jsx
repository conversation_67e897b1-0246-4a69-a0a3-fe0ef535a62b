import React from 'react';
import { Space, Tag, Button, Switch } from 'antd';
import { GlobalOutlined, TeamOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { syncFormValue, updateSiteStatus } from './utils';

// 定义状态颜色
export const colorMap = {
  disabled: '#8c8c8c',
  enabled: '#52c41a',
};

/**
 * 创建表格列定义
 *
 * @param {Object} params 参数对象
 * @param {Array} params.staticDomainOptions 站点域名选项
 * @param {Array} params.accountTypeOptions 账号类型选项
 * @param {Array} params.predefinedTags 预定义标签
 * @param {Array} params.value 当前站点配置数组
 * @param {Function} params.onChange 配置变化回调
 * @param {Object} params.outerFormRef 外部表单引用
 * @param {Function} params.setEditingRecord 设置编辑记录函数
 * @param {Function} params.setDrawerVisible 设置抽屉可见性函数
 * @param {Object} params.messageApi 消息提示API
 * @returns {Array} 表格列定义
 */
export const getTableColumns = ({
  staticDomainOptions,
  accountTypeOptions,
  predefinedTags,
  value,
  onChange,
  outerFormRef,
  setEditingRecord,
  setDrawerVisible,
  messageApi,
}) => [
  {
    title: '站点名称',
    dataIndex: 'domain',
    key: 'domain',
    width: 220,
    filters: staticDomainOptions.map(option => ({
      text: option.label,
      value: option.value,
    })),
    onFilter: (value, record) => record.domain === value,
    render: domain => {
      const option = staticDomainOptions.find(opt => opt.value === domain);
      return (
        <Space>
          <GlobalOutlined />
          {option?.label || domain}
        </Space>
      );
    },
  },
  {
    title: '账号类型',
    dataIndex: 'accountType',
    key: 'accountType',
    width: 120,
    filters: accountTypeOptions.map(option => ({
      text: option.label,
      value: option.value,
    })),
    onFilter: (value, record) => record.accountType === value,
    render: accountType => {
      const option = accountTypeOptions.find(opt => opt.value === accountType);
      return (
        <Space>
          <TeamOutlined />
          {option?.label || accountType}
        </Space>
      );
    },
  },
  {
    title: '状态',
    dataIndex: 'enabled',
    key: 'enabled',
    width: 100,
    filters: [
      { text: '启用', value: true },
      { text: '禁用', value: false },
    ],
    onFilter: (value, record) => {
      if (value === true) return record.enabled !== false;
      return record.enabled === false;
    },
    render: (enabled, record, index) => (
      <Switch
        checked={enabled !== false}
        checkedChildren="启用"
        unCheckedChildren="禁用"
        className="upload-button-no-focus"
        onChange={checked => {
          updateSiteStatus(record, index, value, onChange, outerFormRef, checked);
        }}
      />
    ),
  },
  {
    title: '环境',
    dataIndex: 'domain',
    key: 'environment',
    width: 110,
    filters: predefinedTags.map(tag => ({
      text: tag.label,
      value: tag.value,
    })),
    onFilter: (value, record) => {
      const site = staticDomainOptions.find(opt => opt.value === record.domain);
      return site && site.tag === value;
    },
    render: domain => {
      const site = staticDomainOptions.find(opt => opt.value === domain);
      if (!site) return '-';

      const tagOption = predefinedTags.find(t => t.value === site.tag);
      return <Tag color={tagOption?.color}>{site.tag}</Tag>;
    },
  },
  {
    title: '操作',
    key: 'option',
    width: 160,
    valueType: 'option',
    render: (_, record, index) => [
      <Button
        key="edit"
        type="link"
        icon={<EditOutlined />}
        className="upload-button-no-focus"
        onClick={() => {
          // 根据唯一标识(domain)找到原始数据中的真实索引
          const realIndex = value.findIndex(item => item.domain === record.domain);

          // 如果找到了对应的记录，使用真实索引；否则使用当前行索引
          const indexToUse = realIndex !== -1 ? realIndex : index;

          setEditingRecord({ ...record, index: indexToUse });
          setDrawerVisible(true);
        }}
      >
        编辑
      </Button>,
      <Button
        key="delete"
        type="link"
        danger
        icon={<DeleteOutlined />}
        className="upload-button-no-focus"
        onClick={() => {
          // 根据domain找到原始数据中的真实索引
          const realIndex = value.findIndex(item => item.domain === record.domain);

          // 如果找到了对应的记录，使用真实索引进行删除
          if (realIndex !== -1) {
            // 过滤掉要删除的项
            const newValue = value.filter((_, i) => i !== realIndex);
            onChange?.(newValue);

            // 同步更新表单
            if (outerFormRef) {
              const currentSites = outerFormRef.getFieldValue('sites') || [];
              const newSites = currentSites.filter((_, i) => i !== realIndex);
              syncFormValue(outerFormRef, 'sites', newSites);
            }
          } else {
            // 使用表格行索引作为后备方案
            const newValue = value.filter((_, i) => i !== index);
            onChange?.(newValue);

            // 同步更新表单
            if (outerFormRef) {
              const currentSites = outerFormRef.getFieldValue('sites') || [];
              const newSites = currentSites.filter((_, i) => i !== index);
              syncFormValue(outerFormRef, 'sites', newSites);
            }
          }

          messageApi.success('已删除站点配置');
        }}
      >
        删除
      </Button>,
    ],
  },
];
