import React, { useState, useRef, useEffect } from 'react';
import { ProTable } from '@ant-design/pro-components';
import { Button, Dropdown, message, Statistic } from 'antd';
import { PlusOutlined, SettingOutlined, ImportOutlined, ExportOutlined, CloudUploadOutlined, CloudSyncOutlined } from '@ant-design/icons';
import { staticDomainOptions, accountTypeOptions, accountPresets, predefinedTags } from '../constants';
import SiteConfigDrawerForm from './DrawerForm';
import ImportModal from './ImportModal';
import { getTableColumns } from './columns.jsx';
import { exportSiteConfig, getTableData, updateAllSites, syncFormValue, batchUpdateSiteStatus } from './utils';
import { MESSAGE_TYPES } from '../../constants';
import { CONFIG_TYPES } from '../../constants/feishuConfig';
import './style.css';

/**
 * 站点配置表格组件
 *
 * @param {Object} props 组件属性
 * @param {Array} props.value 当前站点配置数组
 * @param {Function} props.onChange 配置变更回调
 * @param {Object} props.formRef 外部表单引用
 * @returns {JSX.Element} 站点配置表格组件
 */
const SiteConfigTable = ({ value = [], onChange, formRef: outerFormRef }) => {
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importText, setImportText] = useState('');
  const [importError, setImportError] = useState('');
  const [enabledSitesCount, setEnabledSitesCount] = useState(0);
  const [cloudSyncLoading, setCloudSyncLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const actionRef = useRef();
  const formRef = useRef();
  const fileInputRef = useRef();

  // 计算启用站点数量
  useEffect(() => {
    const enabledCount = value.filter(site => site.enabled !== false).length;
    setEnabledSitesCount(enabledCount);
  }, [value]);

  // 处理JSON文件上传
  const handleFileUpload = event => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = e => {
      try {
        const content = e.target.result;
        setImportText(content);
        setImportModalVisible(true);
        // 重置文件输入，以便同一文件可以再次上传
        fileInputRef.current.value = '';
      } catch (error) {
        console.error('读取文件失败:', error);
        messageApi.error(`读取文件失败: ${error.message}`);
        // 重置文件输入，以便同一文件可以再次上传
        fileInputRef.current.value = '';
      }
    };
    reader.onerror = () => {
      messageApi.error('读取文件失败');
      // 重置文件输入，以便同一文件可以再次上传
      fileInputRef.current.value = '';
    };
    reader.readAsText(file);
  };

  // 添加站点
  const handleAdd = () => {
    setEditingRecord(null);
    setDrawerVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async values => {
    const newValues = { ...values };
    let newValue;

    if (editingRecord) {
      // 编辑模式
      const index = editingRecord.index;
      if (index >= 0 && index < value.length) {
        newValue = [...value];
        newValue[index] = newValues;
        // 更新外部表单
        syncFormValue(outerFormRef, ['sites', index], newValues);
      } else {
        newValue = [...value, newValues];
        // 将新记录添加到外部表单
        const currentSites = outerFormRef?.getFieldValue('sites') || [];
        updateAllSites(outerFormRef, [...currentSites, newValues]);
      }
    } else {
      // 新增模式
      newValue = [...value, newValues];
      // 将新记录添加到外部表单
      const currentSites = outerFormRef?.getFieldValue('sites') || [];
      updateAllSites(outerFormRef, [...currentSites, newValues]);
    }

    onChange?.(newValue);
    messageApi.success(`${editingRecord ? '更新' : '添加'}站点配置成功`);
    return true;
  };

  // 关闭导入模态框
  const handleCloseImportModal = () => {
    console.log('关闭导入模态框');
    setImportModalVisible(false);
    setImportText('');
    setImportError('');
  };

  // 上传站点配置到云端
  const handleUploadToCloud = async () => {
    if (!value || value.length === 0) {
      messageApi.error('没有站点配置可以上传');
      return;
    }

    setCloudSyncLoading(true);
    try {
      console.log('正在上传站点配置到云端...', value.length);

      // 发送消息给 background 处理云端上传
      const response = await new Promise(resolve => {
        chrome.runtime.sendMessage(
          {
            type: MESSAGE_TYPES.UPLOAD_CONFIG_SNAPSHOT,
            configData: value,
            configType: CONFIG_TYPES.SITE_CONFIG,

            context: `手动上传${value.length}个站点配置`,
            description: `站点配置手动上传，共${value.length}个站点`,
          },
          resolve
        );
      });

      if (response && response.success) {
        console.log('站点配置上传成功:', response.recordId);
        messageApi.success({
          content: `站点配置已上传到云端（${value.length}个站点）`,
          duration: 4,
          key: 'upload-cloud',
        });
      } else {
        throw new Error(response?.error || '上传失败');
      }
    } catch (error) {
      console.error('上传站点配置失败:', error);
      messageApi.error({
        content: `上传失败: ${error.message}`,
        duration: 6,
        key: 'upload-cloud',
      });
    } finally {
      setCloudSyncLoading(false);
    }
  };

  // 从云端同步站点配置
  const handleSyncFromCloud = async () => {
    setCloudSyncLoading(true);
    try {
      console.log('正在从云端同步站点配置...');

      // 发送消息给 background 获取最新配置
      const response = await new Promise(resolve => {
        chrome.runtime.sendMessage(
          {
            type: MESSAGE_TYPES.GET_LATEST_SITE_CONFIGS,
          },
          resolve
        );
      });

      if (response && response.success && response.configs) {
        console.log('从云端获取到站点配置:', response.configs.length);

        // 更新本地配置
        onChange(response.configs);

        // 同步更新外部表单
        if (outerFormRef) {
          updateAllSites(outerFormRef, response.configs);
        }

        messageApi.success({
          content: `已从云端同步${response.configs.length}个站点配置`,
          duration: 4,
          key: 'sync-cloud',
        });
      } else {
        messageApi.info({
          content: response?.message || '云端暂无站点配置',
          duration: 3,
          key: 'sync-cloud',
        });
      }
    } catch (error) {
      console.error('从云端同步站点配置失败:', error);
      messageApi.error({
        content: `同步失败: ${error.message}`,
        duration: 6,
        key: 'sync-cloud',
      });
    } finally {
      setCloudSyncLoading(false);
    }
  };

  // 导入/导出操作菜单
  const importExportMenu = {
    items: [
      {
        key: 'import',
        label: '导入配置',
        icon: <ImportOutlined />,
        onClick: () => {
          // 触发隐藏的文件输入
          fileInputRef.current.click();
        },
      },
      {
        key: 'export',
        label: '导出配置',
        icon: <ExportOutlined />,
        onClick: () => exportSiteConfig(value, (type, content) => message[type](content)),
      },
    ],
  };

  // 云端操作菜单
  const cloudOperationMenu = {
    items: [
      {
        key: 'upload',
        label: '上传到云端',
        icon: <CloudUploadOutlined />,
        onClick: handleUploadToCloud,
      },
      {
        key: 'sync',
        label: '从云端同步',
        icon: <CloudSyncOutlined />,
        onClick: handleSyncFromCloud,
      },
    ],
  };

  // 批量操作菜单
  const batchOperationMenu = {
    items: [
      {
        key: 'enable',
        label: '批量启用',
        onClick: () => {
          batchUpdateSiteStatus(selectedRowKeys, value, onChange, outerFormRef, true);
          messageApi.success(`已启用 ${selectedRowKeys.length} 个站点`);
          setSelectedRowKeys([]);
        },
      },
      {
        key: 'disable',
        label: '批量禁用',
        onClick: () => {
          batchUpdateSiteStatus(selectedRowKeys, value, onChange, outerFormRef, false);
          messageApi.success(`已禁用 ${selectedRowKeys.length} 个站点`);
          setSelectedRowKeys([]);
        },
      },
      {
        key: 'delete',
        label: '批量删除',
        danger: true,
        onClick: () => {
          const indices = selectedRowKeys
            .map(key => parseInt(key))
            .filter(index => !isNaN(index))
            .sort((a, b) => b - a); // 倒序排列，避免删除时索引变化

          const newValue = [...value];

          // 按索引倒序删除，确保索引正确
          indices.forEach(index => {
            newValue.splice(index, 1);
          });

          onChange?.(newValue);

          // 同步更新表单
          updateAllSites(outerFormRef, newValue);

          messageApi.success(`已删除 ${indices.length} 个站点`);
          setSelectedRowKeys([]);
        },
      },
    ],
  };

  return (
    <>
      {/* 隐藏的文件输入框，用于导入功能 */}
      <input type="file" accept=".json" ref={fileInputRef} style={{ display: 'none' }} onChange={handleFileUpload} />
      {contextHolder}
      {/* 导入配置的模态框 */}
      <ImportModal
        visible={importModalVisible}
        onCancel={handleCloseImportModal}
        importText={importText}
        importError={importError}
        setImportError={setImportError}
        onChange={onChange}
        outerFormRef={outerFormRef}
      />

      <ProTable
        rowKey="key"
        loading={cloudSyncLoading}
        columns={getTableColumns({
          staticDomainOptions,
          accountTypeOptions,
          predefinedTags,
          value,
          onChange,
          outerFormRef,
          setEditingRecord,
          setDrawerVisible,
          messageApi: message,
        })}
        dataSource={getTableData(value)}
        rowSelection={{
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        }}
        search={false}
        options={{
          density: true,
          fullScreen: false,
          reload: false,
          setting: true,
        }}
        headerTitle={
          <div className="site-statistic-wrapper">
            <Statistic title="启用站点数量" value={enabledSitesCount} suffix={`/ ${value.length}`} style={{ marginRight: 16 }} />
          </div>
        }
        toolbar={{
          actions: [
            <Button key="add" type="primary" icon={<PlusOutlined />} onClick={handleAdd} className="upload-button-no-focus">
              新增站点
            </Button>,
            <Dropdown key="importExport" menu={importExportMenu}>
              <Button type="default" icon={<SettingOutlined />} className="upload-button-no-focus">
                导入/导出
              </Button>
            </Dropdown>,
            <Dropdown key={'cloudSync'} menu={cloudOperationMenu}>
              <Button type="default" icon={<CloudSyncOutlined />} className="upload-button-no-focus">
                云端同步
              </Button>
            </Dropdown>,
            <Dropdown key="batchActions" menu={batchOperationMenu} disabled={selectedRowKeys.length === 0}>
              <Button type="default" icon={<SettingOutlined />} className="upload-button-no-focus">
                批量操作 ({selectedRowKeys.length})
              </Button>
            </Dropdown>,
          ],
        }}
        pagination={{
          pageSize: 20,
          showSizeChanger: false,
        }}
        actionRef={actionRef}
      />

      <SiteConfigDrawerForm
        visible={drawerVisible}
        onVisibleChange={setDrawerVisible}
        editingRecord={editingRecord}
        onFinish={handleFormSubmit}
        formRef={formRef}
        staticDomainOptions={staticDomainOptions}
        accountTypeOptions={accountTypeOptions}
        predefinedTags={predefinedTags}
        accountPresets={accountPresets}
        allSites={value}
      />
    </>
  );
};

export default SiteConfigTable;
