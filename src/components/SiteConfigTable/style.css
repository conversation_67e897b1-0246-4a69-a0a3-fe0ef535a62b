/* 表格行过渡效果 */
.ant-table-tbody > tr {
  transition: all 0.3s ease;
}

/* 站点统计样式 */
.site-statistic-wrapper {
  display: flex;
  align-items: center;
}

.site-statistic-wrapper .ant-statistic {
  margin-right: 16px;
}

.site-statistic-wrapper .ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
}

.site-statistic-wrapper .ant-statistic-content {
  font-size: 20px;
  font-weight: 500;
}

/* 操作按钮样式 */
.ant-btn-link {
  border: 1px solid #d9d9d9;
  padding: 0 8px;
  transition: all 0.3s ease;
}

.ant-btn-link:hover {
  border-color: #1890ff;
}

.ant-btn-link[aria-disabled='true'] {
  border-color: #d9d9d9;
}

.ant-btn-dangerous.ant-btn-link {
  border-color: #ff4d4f;
}

.ant-btn-dangerous.ant-btn-link:hover {
  border-color: #ff7875;
}

/* 状态标签过渡 */
.ant-tag {
  transition: all 0.3s ease;
}

/* 统计数字过渡 */
.ant-statistic-content {
  transition: all 0.3s ease;
}

/* 云端同步按钮样式 */
.ant-btn[data-key='uploadCloud'],
.ant-btn[data-key='syncCloud'] {
  margin-left: 8px;
}

.ant-btn[data-key='uploadCloud'] .anticon {
  color: #52c41a;
}

.ant-btn[data-key='syncCloud'] .anticon {
  color: #1890ff;
}

.ant-btn[data-key='uploadCloud']:hover .anticon {
  color: #73d13d;
}

.ant-btn[data-key='syncCloud']:hover .anticon {
  color: #40a9ff;
}
