import React, { useState, useEffect } from 'react';
import { Button, Space, Typography, Tag, Modal, message, Tooltip, Drawer } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import {
  HistoryOutlined,
  EyeOutlined,
  DownloadOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { configSnapshotService } from '../../services/configSnapshotService';
import { CONFIG_TYPES } from '../../constants/feishuConfig';
import { QueryFilters } from '../../constants/queryFilters';
import { formatShortDateTime, formatDateTime } from '../../utils/dateUtils';

const { Text } = Typography;

/**
 * JSON历史记录面板组件
 * 支持查看、预览和加载历史配置
 */
const JsonHistoryPanel = ({ configType = CONFIG_TYPES.TRANSLATION_CONFIG, onLoadConfig, visible = false, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [historyList, setHistoryList] = useState([]);
  const [filteredList, setFilteredList] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [currentPageSize, setCurrentPageSize] = useState(10);
  const [currentSearchParams, setCurrentSearchParams] = useState({});
  const [messageApi, contextHolder] = message.useMessage();

  // 获取历史记录
  const fetchHistory = async (searchParams = {}, pagination = {}) => {
    setLoading(true);
    try {
      console.log('JsonHistoryPanel: 获取历史记录，配置类型:', configType, '查询参数:', searchParams, '分页参数:', pagination);

      // 更新当前状态
      setCurrentSearchParams(searchParams);
      if (pagination.pageSize) {
        setCurrentPageSize(pagination.pageSize);
      }

      // 构建查询过滤条件
      const queryFilter = QueryFilters.historyQuery.buildQuery(configType, searchParams);

      const queryOptions = {
        filter: queryFilter,
        pageSize: pagination.pageSize || currentPageSize || 10,
        // 按创建时间倒序排列
        sort: [
          {
            field_name: 'created_at',
            desc: true,
          },
        ],
      };

      // 根据配置类型和查询参数查询相应的历史记录
      const result = await configSnapshotService.getConfigHistory(configType, queryOptions);

      if (result.success) {
        setHistoryList(result.data || []);
        setFilteredList(result.data || []);
        console.log('JsonHistoryPanel: 获取到历史记录数量:', result.data?.length || 0);
      } else {
        messageApi.error(result.message || '获取历史记录失败');
        setHistoryList([]);
        setFilteredList([]);
      }
    } catch (error) {
      console.error('JsonHistoryPanel: 获取历史记录失败:', error);
      messageApi.error(`获取历史记录失败: ${error.message}`);
      setHistoryList([]);
      setFilteredList([]);
    } finally {
      setLoading(false);
    }
  };

  // 组件显示时获取历史记录
  useEffect(() => {
    if (visible) {
      fetchHistory();
    }
  }, [visible, configType]);

  // 当历史记录更新时，同步更新过滤列表
  useEffect(() => {
    setFilteredList(historyList);
  }, [historyList]);

  // 预览配置
  const handlePreview = item => {
    try {
      const configData = JSON.parse(item.configJson);
      setPreviewData({
        ...item,
        parsedConfig: configData,
      });
      setPreviewVisible(true);
    } catch (error) {
      messageApi.error('配置数据解析失败');
      console.error('JsonHistoryPanel: 配置解析失败:', error);
    }
  };

  // 加载配置到编辑器
  const handleLoadConfig = item => {
    Modal.confirm({
      title: '确认加载配置',
      content: (
        <div>
          <p>确定要加载以下配置到编辑器吗？</p>
          <p>
            <strong>配置名称：</strong>
            {item.snapshotName}
          </p>
          <p>
            <strong>创建时间：</strong>
            {formatDateTime(item.createdAt)}
          </p>
          <p>
            <strong>创建者：</strong>
            {item.createdBy}
          </p>
          <p style={{ color: '#ff4d4f', fontSize: '12px' }}>注意：这将替换当前编辑器中的所有内容</p>
        </div>
      ),
      onOk: () => {
        try {
          console.log('JsonHistoryPanel: 开始加载配置到编辑器:', {
            itemId: item.recordId,
            configJsonLength: item.configJson?.length,
            onLoadConfigExists: !!onLoadConfig,
          });

          const configData = JSON.parse(item.configJson);
          const formattedJson = JSON.stringify(configData, null, 2);

          console.log('JsonHistoryPanel: 调用onLoadConfig:', {
            formattedJsonLength: formattedJson.length,
            formattedJsonSample: formattedJson.slice(0, 100),
          });

          onLoadConfig?.(formattedJson);
          messageApi.success('配置已加载到编辑器');
          onClose?.();
        } catch (error) {
          messageApi.error('配置数据格式错误，无法加载');
          console.error('JsonHistoryPanel: 加载配置失败:', error);
        }
      },
    });
  };

  // 下载配置
  const handleDownload = item => {
    try {
      const configData = JSON.parse(item.configJson);
      const formattedJson = JSON.stringify(configData, null, 2);
      const blob = new Blob([formattedJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${item.snapshotName || 'config'}_${formatShortDateTime}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      messageApi.success('配置文件已下载');
    } catch (error) {
      messageApi.error('下载失败');
      console.error('JsonHistoryPanel: 下载失败:', error);
    }
  };

  // 获取配置类型显示名称
  const getConfigTypeName = type => {
    const typeMap = {
      [CONFIG_TYPES.SITE_CONFIG]: '站点配置',
      [CONFIG_TYPES.TRANSLATION_CONFIG]: '翻译配置',
      [CONFIG_TYPES.MENU_CONFIG]: '菜单配置',
    };
    return typeMap[type] || type;
  };

  // ProTable列定义

  /**
   * @type {import('@ant-design/pro-components').ProColumnType[]}
   */
  const columns = [
    {
      title: '配置名称',
      dataIndex: 'snapshotName',
      key: 'snapshotName',
      width: 250,
      ellipsis: true,
      search: true,
      render: text => (
        <Space direction="vertical" size={2}>
          <Text strong>{text}</Text>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      search: false,
      render: text => (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {text || '无描述'}
        </Text>
      ),
    },
    {
      title: '创建者',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
      search: false,
      render: text => (
        <Space size={4}>
          <UserOutlined style={{ fontSize: '12px', color: '#666' }} />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {text}
          </Text>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'dateTimeRange',
      hideInTable: true,
      search: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      search: false,
      render: text => (
        <Space size={4}>
          <ClockCircleOutlined style={{ fontSize: '12px', color: '#666' }} />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {formatShortDateTime(text)}
          </Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      search: false,
      render: (_, record) => (
        <Space size={8}>
          <Tooltip title="预览配置">
            <Button type="text" size="small" icon={<EyeOutlined />} onClick={() => handlePreview(record)} />
          </Tooltip>
          <Tooltip title="加载到编辑器">
            <Button type="text" size="small" icon={<DownloadOutlined />} onClick={() => handleLoadConfig(record)} />
          </Tooltip>
          <Tooltip title="下载配置文件">
            <Button type="text" size="small" icon={<FileTextOutlined />} onClick={() => handleDownload(record)} />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <>
      {contextHolder}
      <Drawer
        title={
          <Space>
            <HistoryOutlined />
            <span>{getConfigTypeName(configType)}历史记录</span>
            <Tag color="blue">{filteredList.length} 条记录</Tag>
          </Space>
        }
        open={visible}
        onClose={onClose}
        width={'80%'}
        placement="right"
        destroyOnClose
        extra={
          <Button icon={<ReloadOutlined />} onClick={() => fetchHistory(currentSearchParams, { pageSize: currentPageSize })}>
            刷新
          </Button>
        }
      >
        {/* 历史记录表格 */}
        <ProTable
          columns={columns}
          dataSource={filteredList}
          rowKey="recordId"
          loading={loading}
          pagination={{
            pageSize: currentPageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50'],
            onShowSizeChange: (current, size) => {
              console.log('ProTable: 分页大小变化:', { current, size });
              setCurrentPageSize(size);
              // 重新查询数据，使用新的分页大小
              fetchHistory(currentSearchParams, { pageSize: size });
            },
          }}
          form={{
            layout: 'vertical',
            wrapperCol: {
              span: 24,
            },
          }}
          search={{
            labelWidth: 'auto',
            collapsed: false,
            collapseRender: false,
            searchText: '查询',
            resetText: '重置',
            optionRender: (_, __, dom) => [...dom.reverse()],
          }}
          onSubmit={(params, ...args) => {
            console.log('ProTable: 查询参数:', params, '所有参数:', args);
            // 处理时间范围参数
            const searchParams = { ...params };
            if (params.createdAt && Array.isArray(params.createdAt) && params.createdAt.length === 2) {
              // 保持Date对象格式，让queryFilters处理时间戳转换
              searchParams.createdAt = [new Date(params.createdAt[0]), new Date(params.createdAt[1])];
              console.log('JsonHistoryPanel: 处理后的时间范围:', {
                start: searchParams.createdAt[0].toISOString(),
                end: searchParams.createdAt[1].toISOString(),
                startTimestamp: searchParams.createdAt[0].getTime(),
                endTimestamp: searchParams.createdAt[1].getTime(),
              });
            }
            // 使用当前的分页大小
            fetchHistory(searchParams, { pageSize: currentPageSize });
          }}
          onReset={(...args) => {
            console.log('ProTable: 重置查询，所有参数:', args);
            // 使用当前的分页大小
            fetchHistory({}, { pageSize: currentPageSize });
          }}
          options={{
            reload: () => fetchHistory(currentSearchParams, { pageSize: currentPageSize }),
            density: false,
            fullScreen: false,
            setting: false,
          }}
          locale={{
            emptyText: loading ? '正在加载...' : '暂无历史记录',
          }}
          size="small"
          scroll={{ y: 400 }}
          toolBarRender={false}
          dateFormatter="string"
          headerTitle={false}
        />
      </Drawer>

      {/* 预览模态框 */}
      <Modal
        title={`预览配置 - ${previewData?.snapshotName}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={900}
        footer={[
          <Button
            key="load"
            type="primary"
            onClick={() => {
              if (previewData) {
                try {
                  console.log('JsonHistoryPanel: 预览模态框加载配置:', {
                    previewDataId: previewData.recordId,
                    configJsonLength: previewData.configJson?.length,
                  });

                  const configData = JSON.parse(previewData.configJson);
                  const formattedJson = JSON.stringify(configData, null, 2);
                  onLoadConfig?.(formattedJson);
                  setPreviewVisible(false);
                  onClose?.();
                } catch (error) {
                  messageApi.error('配置数据格式错误，无法加载');
                  console.error('JsonHistoryPanel: 预览加载配置失败:', error);
                }
              }
            }}
          >
            加载到编辑器
          </Button>,
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
        ]}
        centered
        destroyOnClose
        mask={false}
      >
        {previewData && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space direction="vertical" size={4}>
                <Text>
                  <strong>配置名称：</strong>
                  {previewData.snapshotName}
                </Text>
                <Text>
                  <strong>创建时间：</strong>
                  {formatDateTime(previewData.createdAt)}
                </Text>
                <Text>
                  <strong>创建者：</strong>
                  {previewData.createdBy}
                </Text>
                <Text>
                  <strong>描述：</strong>
                  {previewData.description}
                </Text>
              </Space>
            </div>
            <div style={{ maxHeight: '400px', overflow: 'auto' }}>
              <SyntaxHighlighter
                language="json"
                style={oneDark}
                customStyle={{
                  margin: 0,
                  borderRadius: '6px',
                }}
              >
                {JSON.stringify(previewData.parsedConfig, null, 2)}
              </SyntaxHighlighter>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default JsonHistoryPanel;
