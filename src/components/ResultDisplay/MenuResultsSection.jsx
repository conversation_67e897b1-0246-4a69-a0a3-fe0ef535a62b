import React, { forwardRef } from 'react';
import { Space, Tag, Typography, Alert } from 'antd';
import { MenuOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { LightList, LightStatistic } from '../LightStatisticComponents';
import BaseResultDisplay from './BaseResultDisplay';

const { Text } = Typography;

const MenuResultsSection = forwardRef(({ result, loading }, ref) => {
  // 获取结果统计信息
  const getResultStatistics = () => {
    if (!result?.sites?.length) {
      return {
        totalSites: 0,
        successSites: 0,
        totalMenus: 0,
        successMenus: 0,
      };
    }

    const totalSites = result.sites.length;
    const successSites = result.sites.filter(site => site.status === 'success' || site.success).length;

    // 计算菜单数
    let totalMenus = 0;
    let successMenus = 0;

    result.sites.forEach(site => {
      // 添加对菜单的统计
      if (site.menus?.length) {
        totalMenus += site.menus.length;

        // 计算成功菜单数，包括：
        // 1. 成功添加的菜单
        // 2. 菜单已存在但权限分配成功的情况
        successMenus += site.menus.filter(menu => {
          return menu.success || (menu.message && menu.message.includes('已存在'));
        }).length;
      }
    });

    return {
      totalSites,
      successSites,
      totalMenus,
      successMenus,
    };
  };

  // 渲染菜单统计信息
  const renderMenuStatistics = () => {
    const stats = getResultStatistics();

    if (stats.totalMenus === 0) return null;

    return (
      <>
        <LightStatistic
          statistic={{
            title: '菜单总数',
            value: stats.totalMenus,
            valueStyle: {
              color: '#1677ff',
            },
            prefix: <MenuOutlined />,
          }}
        />
        <LightStatistic
          statistic={{
            title: '成功菜单',
            value: stats.successMenus,
            valueStyle: {
              color: '#3f8600',
            },
            suffix: `/ ${stats.totalMenus}`,
            prefix: <CheckCircleOutlined />,
          }}
        />
        <LightStatistic
          statistic={{
            title: '失败菜单',
            value: stats.totalMenus - stats.successMenus,
            valueStyle: {
              color: stats.totalMenus - stats.successMenus > 0 ? '#cf1322' : '#3f8600',
            },
            suffix: `/ ${stats.totalMenus}`,
            prefix: <CloseCircleOutlined />,
          }}
        />
      </>
    );
  };

  // 渲染单个站点的菜单结果
  const renderSiteContent = site => {
    const hasMenus = site.menus?.length > 0;

    if (!hasMenus) return null;

    return (
      <div className="menu-results">

        {/* 添加权限分配状态显示 */}
        {site.permissionAssigned !== undefined && (
          <div className="permission-status">
            {site.permissionAssigned ? (
              <Alert
                message="菜单权限分配成功"
                description={
                  site.permissionMessage ||
                  (site.menuIdsNeedingPermissions?.length > 0
                    ? `已成功为${site.menuIdsNeedingPermissions?.length || '所有'}个菜单分配权限`
                    : '所有菜单已有权限，无需重新分配')
                }
                type="success"
                showIcon
                style={{ marginBottom: 8 }}
              />
            ) : site.permissionMessage ? (
              <Alert message="菜单权限状态" description={site.permissionMessage} type="info" showIcon style={{ marginBottom: 8 }} />
            ) : site.permissionError?.includes('没有') ? (
              <Alert message="无需分配菜单权限" description={site.permissionError} type="warning" showIcon style={{ marginBottom: 8 }} />
            ) : (
              <Alert
                message="菜单权限分配失败"
                description={site.permissionError || '无法为菜单分配权限，请检查角色权限'}
                type="error"
                showIcon
                style={{ marginBottom: 8 }}
              />
            )}
          </div>
        )}

        <LightList
          dataSource={site.menus || []}
          rowKey={item => item.menuId || item.name}
          rowClassName={record => {
            // 判断菜单是否为"已存在但权限分配成功"的状态
            const isExistingWithPermission = !record.success && record.message && record.message.includes('已存在') && site.permissionAssigned;
            const isExistingMenu = !record.success && record.message && record.message.includes('已存在');

            if (isExistingWithPermission || isExistingMenu) {
              return 'success-item'; // 使用成功样式
            } else {
              return record.success ? 'success-item' : 'error-item';
            }
          }}
          metas={{
            title: {
              dataIndex: 'name',
              render: (_, record) => {
                // 判断菜单是否为"已存在但权限分配成功"的状态
                const isExistingWithPermission = !record.success && record.message && record.message.includes('已存在') && site.permissionAssigned;
                const isExistingMenu = !record.success && record.message && record.message.includes('已存在');

                return (
                  <Space>
                    <MenuOutlined className="menu-icon" />
                    <span className="menu-name">{record.name}</span>
                    {record.success ? (
                      <Tag color="success" icon={<CheckCircleOutlined />}>
                        成功
                      </Tag>
                    ) : isExistingWithPermission ? (
                      <Tag color="success" icon={<CheckCircleOutlined />}>
                        已存在(权限已分配)
                      </Tag>
                    ) : isExistingMenu ? (
                      <Tag color="success" icon={<CheckCircleOutlined />}>
                        已存在
                      </Tag>
                    ) : (
                      <Tag color="error" icon={<CloseCircleOutlined />}>
                        失败
                      </Tag>
                    )}
                    {!record.success && record.message && record.message.includes('父菜单') && <Tag color="warning">父菜单问题</Tag>}
                  </Space>
                );
              },
            },
            description: {
              dataIndex: 'message',
              render: (message, record) => {
                // 判断菜单是否为"已存在但权限分配成功"的状态
                const isExistingWithPermission = !record.success && record.message && record.message.includes('已存在') && site.permissionAssigned;
                const isExistingMenu = !record.success && record.message && record.message.includes('已存在');

                return (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {record.parentName && record.parentName.trim() !== '' && (
                      <Text type="secondary">
                        <span style={{ fontWeight: 'bold' }}>所属菜单:</span> {record.parentName}
                      </Text>
                    )}
                    {!record.success && !isExistingMenu && record.message && (
                      <Alert
                        message={record.message.includes('父菜单') ? '菜单添加失败: 父菜单问题' : '菜单添加失败'}
                        description={message || record.error}
                        type="error"
                        showIcon
                        style={{ marginBottom: 8 }}
                        className={`menu-error-alert ${record.message.includes('父菜单') ? 'menu-error-parent' : ''}`}
                      />
                    )}
                    {isExistingWithPermission && (
                      <Alert
                        message="菜单已存在，无需添加"
                        description="该菜单已存在，已成功分配权限"
                        type="success"
                        showIcon
                        style={{ marginBottom: 8 }}
                        className="menu-success-duplicate"
                      />
                    )}
                    {isExistingMenu && !isExistingWithPermission && (
                      <Alert
                        message="菜单已存在，无需添加"
                        description={site.permissionAssigned ? '该菜单已存在且已有权限，无需分配' : '该菜单已存在，可直接使用'}
                        type="success"
                        showIcon
                        style={{ marginBottom: 8 }}
                        className="menu-success-duplicate"
                      />
                    )}
                    {record.success && <Text type="secondary">{message || record.error}</Text>}
                  </Space>
                );
              },
            },
          }}
        />
      </div>
    );
  };

  // 使用基础组件并传入特定的渲染函数
  return (
    <BaseResultDisplay
      ref={ref}
      result={result}
      loading={loading}
      renderSiteContent={renderSiteContent}
      className="menu-results-section"
      data-result-type="menu"
      statistics={getResultStatistics()}
    >
      {renderMenuStatistics()}
    </BaseResultDisplay>
  );
});

export default MenuResultsSection;
