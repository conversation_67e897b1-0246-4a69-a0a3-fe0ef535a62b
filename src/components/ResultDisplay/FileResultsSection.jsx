import React, { forwardRef } from 'react';
import { Space, Tag, Typography } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, FolderOutlined } from '@ant-design/icons';
import { LightList, LightStatistic } from '../LightStatisticComponents';
import BaseResultDisplay from './BaseResultDisplay';
import { getFileIcon } from '@/utils/fileUtils';

const { Text } = Typography;

/**
 * 文件结果展示组件
 */
const FileResultsSection = forwardRef(({ result, loading }, ref) => {
  // 获取结果统计信息
  const getResultStatistics = () => {
    if (!result?.sites?.length) {
      return {
        totalSites: 0,
        successSites: 0,
        totalFiles: 0,
        successFiles: 0,
      };
    }

    const totalSites = result.sites.length;
    const successSites = result.sites.filter(site => site.status === 'success' || site.success).length;

    // 计算总文件数和成功文件数
    let totalFiles = 0;
    let successFiles = 0;

    result.sites.forEach(site => {
      if (site.files?.length) {
        totalFiles += site.files.length;
        successFiles += site.files.filter(file => file.success).length;
      } else if (site.uploadResults?.length) {
        totalFiles += site.uploadResults.length;
        successFiles += site.uploadResults.filter(file => file.success || file.status === 'success').length;
      }
    });

    return {
      totalSites,
      successSites,
      totalFiles,
      successFiles,
    };
  };

  // 渲染文件统计信息
  const renderFileStatistics = () => {
    const stats = getResultStatistics();

    if (stats.totalFiles === 0) return null;

    return (
      <>
        <LightStatistic
          statistic={{
            title: '文件总数',
            value: stats.totalFiles,
            valueStyle: {
              color: '#1677ff',
            },
            prefix: <FolderOutlined />,
          }}
        />
        <LightStatistic
          statistic={{
            title: '成功文件',
            value: stats.successFiles,
            valueStyle: {
              color: '#3f8600',
            },
            suffix: `/ ${stats.totalFiles}`,
            prefix: <CheckCircleOutlined />,
          }}
        />
        <LightStatistic
          statistic={{
            title: '失败文件',
            value: stats.totalFiles - stats.successFiles,
            valueStyle: {
              color: stats.totalFiles - stats.successFiles > 0 ? '#cf1322' : '#3f8600',
            },
            suffix: `/ ${stats.totalFiles}`,
            prefix: <CloseCircleOutlined />,
          }}
        />
      </>
    );
  };

  // 渲染单个站点的文件结果
  const renderSiteContent = site => {
    const hasFiles = site.files?.length > 0 || site.uploadResults?.length > 0;

    if (!hasFiles) return null;

    // 获取文件数组
    const fileArray = site.files || site.uploadResults || [];

    return (
      <div className="file-results">
        <LightList
          dataSource={fileArray}
          rowKey={(item, index) => `file-${index}-${item.name || item.file}`}
          rowClassName={record => (record.status === 'success' || record.success ? 'success-item' : 'error-item')}
          metas={{
            title: {
              dataIndex: 'name',
              render: (_, record) => {
                const fileName = record.name || record.file;
                return (
                  <Space>
                    {getFileIcon(fileName)}
                    <span className="file-name">{fileName}</span>
                    {record.status === 'success' || record.success ? (
                      <Tag color="success" icon={<CheckCircleOutlined />}>
                        成功
                      </Tag>
                    ) : (
                      <Tag color="error" icon={<CloseCircleOutlined />}>
                        失败
                      </Tag>
                    )}
                  </Space>
                );
              },
            },
            description: {
              dataIndex: 'message',
              render: (message, record) => <Text type="secondary">{message || record.error}</Text>,
            },
          }}
        />
      </div>
    );
  };

  // 使用基础组件并传入特定的子组件和渲染函数
  return (
    <BaseResultDisplay
      ref={ref}
      result={result}
      loading={loading}
      renderSiteContent={renderSiteContent}
      className="file-results-section"
      data-result-type="file"
      statistics={getResultStatistics()}
    >
      {renderFileStatistics()}
    </BaseResultDisplay>
  );
});

export default FileResultsSection;
