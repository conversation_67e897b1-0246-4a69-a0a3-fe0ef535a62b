import React, { forwardRef } from 'react';
import { Space, Tag, Typography } from 'antd';
import { TranslationOutlined, CheckCircleOutlined, CloseCircleOutlined, FileExcelOutlined } from '@ant-design/icons';
import { LightList, LightStatistic } from '../LightStatisticComponents';
import BaseResultDisplay from './BaseResultDisplay';

const { Text } = Typography;

/**
 * 多语言导入结果展示组件
 */
const LanguageResultsSection = forwardRef(({ result, loading }, ref) => {
  // 获取结果统计信息
  const getResultStatistics = () => {
    if (!result?.sites?.length) {
      return {
        totalSites: 0,
        successSites: 0,
        totalFiles: 0,
        successFiles: 0,
      };
    }

    const totalSites = result.sites.length;
    const successSites = result.sites.filter(site => site.status === 'success' || site.success).length;


    // 计算文件数和成功文件数
    let totalFiles = 0;
    let successFiles = 0;

    result.sites.forEach(site => {

      // 计算文件数量
      if (site.files?.length > 0) {
        totalFiles += site.files.length;
        successFiles += site.files.filter(file => file.success || file.status === 'success').length;
      }
    });

    return {
      totalSites,
      successSites,
      totalFiles,
      successFiles,
    };
  };

  // 渲染语言条目统计信息
  const renderLanguageStatistics = () => {
    const stats = getResultStatistics();

    if (stats.totalFiles === 0) return null;

    return (
      <>
        {stats.totalFiles > 0 && (
          <LightStatistic
            statistic={{
              title: '导入文件数',
              value: stats.totalFiles,
              valueStyle: {
                color: '#1677ff',
              },
              prefix: <FileExcelOutlined />,
            }}
          />
        )}
        {stats.totalFiles > 0 && (
          <LightStatistic
            statistic={{
              title: '成功文件',
              value: stats.successFiles,
              valueStyle: {
                color: '#3f8600',
              },
              suffix: `/ ${stats.totalFiles}`,
              prefix: <CheckCircleOutlined />,
            }}
          />
        )}
        {stats.totalFiles > 0 && (
          <LightStatistic
            statistic={{
              title: '失败文件',
              value: stats.totalFiles - stats.successFiles,
              valueStyle: {
                color: stats.totalFiles - stats.successFiles > 0 ? '#cf1322' : '#3f8600',
              },
              suffix: `/ ${stats.totalFiles}`,
              prefix: <CloseCircleOutlined />,
            }}
          />
        )}
      </>
    );
  };

  // 渲染单个站点的多语言导入结果
  const renderSiteContent = (site, result) => {
    // 如果是导入中状态，展示特殊的进度信息
    if (site.status === 'importing') {
      return (
        <div className="language-results">
          <LightList
            dataSource={site.languageItems || []}
            rowKey={(item, index) => `importing-${index}`}
            rowClassName={() => 'importing-item'}
            metas={{
              title: {
                dataIndex: 'name',
                render: (_, record) => (
                  <Space>
                    <TranslationOutlined className="language-icon" />
                    <span className="language-name">{record.name || '导入进度'}</span>
                    <Tag color="processing">进行中</Tag>
                  </Space>
                ),
              },
              description: {
                dataIndex: 'message',
                render: message => (
                  <div>
                    <Text type="secondary">{message || `已完成 ${site.progress || 0}%`}</Text>
                    {site.message && <Text type="secondary"> - {site.message}</Text>}
                  </div>
                ),
              },
            }}
          />
        </div>
      );
    }

    // 提取文件数组
    const fileArray = site.files || [];

    // 如果没有文件，则不显示任何内容
    if (fileArray.length === 0) return null;

    // 渲染文件结果
    return (
      <div className="language-results">
        <div className="file-results">
          <LightList
            dataSource={fileArray}
            rowKey={(item, index) => `file-${index}-${item.name || item.file}`}
            rowClassName={record => (record.success || record.status === 'success' ? 'success-item' : 'error-item')}
            metas={{
              title: {
                dataIndex: 'name',
                render: (_, record) => (
                  <Space>
                    <FileExcelOutlined className="file-icon excel" />
                    <span className="file-name">{record.name || record.file || record.fileName || '未命名文件'}</span>
                    {record.success || record.status === 'success' ? (
                      <Tag color="success" icon={<CheckCircleOutlined />}>
                        成功
                      </Tag>
                    ) : (
                      <Tag color="error" icon={<CloseCircleOutlined />}>
                        失败
                      </Tag>
                    )}
                  </Space>
                ),
              },
              description: {
                dataIndex: 'message',
                render: (message, record) => <Text type="secondary">{message || record.error}</Text>,
              },
            }}
          />
        </div>
      </div>
    );
  };

  // 使用基础组件并传入特定的子组件和渲染函数
  return (
    <BaseResultDisplay
      ref={ref}
      result={result}
      loading={loading}
      renderSiteContent={site => renderSiteContent(site, result)}
      className="language-results-section"
      data-result-type="language"
      statistics={getResultStatistics()}
    >
      {renderLanguageStatistics()}
    </BaseResultDisplay>
  );
});

export default LanguageResultsSection;
