import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Empty, Typography, Card, Spin } from 'antd';
import FileResultsSection from './FileResultsSection';
import MenuResultsSection from './MenuResultsSection';
import LanguageResultsSection from './LanguageResultsSection';
import { OPERATION_TYPES } from '../../constants';
import { getFileIcon } from '@/utils/fileUtils';
import './index.css';

const { Text } = Typography;

/**
 * 结果展示组件，obs 文件上传、批量新增菜单、多语言导入等
 */
const ResultDisplay = forwardRef(({ result, loading, title, loadingText }, ref) => {
  // 创建一个内部ref引用主容器
  const containerRef = useRef(null);
  const fileResultsRef = useRef(null);
  const menuResultsRef = useRef(null);
  const languageResultsRef = useRef(null);

  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    // 添加scrollIntoView方法用于滚动
    scrollIntoView: options => {
      if (containerRef.current) {
        containerRef.current.scrollIntoView(options);
      }
    },
  }));

  // console.log('ResultDisplay 接收到的数据:', { result, title });

  // 是否为多语言导入结果 - 明确标识
  const isLanguageImport = result?.isLanguageImportResult === true;

  // 确定结果类型
  const hasFiles = result?.sites?.some(site => (site.files && site.files.length > 0) || (site.uploadResults && site.uploadResults.length > 0));

  const hasMenus = result?.sites?.some(site => site.menus && site.menus.length > 0);

  // 确保任何错误结果都被处理，即使没有具体的menus属性
  const hasErrors = result?.sites?.some(site => site.status === 'error' || site.success === false || site.error);

  // 确定是否有多语言导入结果
  const hasLanguages = result?.sites?.some(
    site => (site.languages && site.languages.length > 0) || (site.languageItems && site.languageItems.length > 0)
  );

  // 渲染处理结果
  const renderProcessResults = () => {
    if (loading) {
      return (
        <div className="process-spinner">
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>{loadingText || '正在处理中，请稍候...'}</Text>
          </div>
        </div>
      );
    }

    if (!result || !result.sites || result.sites.length === 0) {
      return <Empty description="暂无处理结果" />;
    }

    // 确定渲染模式
    const renderMode = isLanguageImport ? OPERATION_TYPES.IMPORT_LANGUAGE : hasMenus ? OPERATION_TYPES.MENU : OPERATION_TYPES.UPLOAD;

    return (
      <div className="process-results-wrapper">
        {/* 根据结果类型渲染不同的组件 */}
        {(() => {
          switch (renderMode) {
            case OPERATION_TYPES.IMPORT_LANGUAGE:
              // 多语言导入结果模式
              return <LanguageResultsSection ref={languageResultsRef} result={result} loading={false} />;

            case OPERATION_TYPES.MENU:
              // 菜单管理结果模式
              return <MenuResultsSection ref={menuResultsRef} result={result} loading={false} />;

            case OPERATION_TYPES.UPLOAD:
            default:
              // 文件上传结果模式
              return <FileResultsSection ref={fileResultsRef} result={result} loading={false} />;
          }
        })()}

        {/* 如果没有匹配到任何具体结果类型，但有其他结果，显示基本结果 */}
        {!hasMenus && !hasFiles && !hasLanguages && !hasErrors && result.sites.some(site => site.results?.length > 0) && (
          <Card
            title={
              <div className="site-header">
                {getFileIcon('text.txt')}
                <span>其他处理结果</span>
              </div>
            }
            className="other-results-card"
          >
            <Empty description="详情请查看各站点具体结果" />
          </Card>
        )}
      </div>
    );
  };

  return (
    <div ref={containerRef} className="result-display-container">
      {title ? (
        <Card title={title} className="result-pro-card">
          {renderProcessResults()}
        </Card>
      ) : (
        renderProcessResults()
      )}
    </div>
  );
});

export default ResultDisplay;
