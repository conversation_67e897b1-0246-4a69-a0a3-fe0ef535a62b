import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect, useId, useCallback } from 'react';
import { Empty, Alert, Space, Tag, Typography, Card, Spin } from 'antd';
import {
  FileTextOutlined,
  RightOutlined,
  DownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  BarChartOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import { LightStatistic } from '../LightStatisticComponents';
import { staticDomainOptions } from '../constants';
import './index.css';

const { Text, Title } = Typography;

/**
 * 基础结果展示组件，提供结果统计和站点结果展示功能
 * 使用实例 ID、ref 引用等技术隔离多实例动画冲突
 */
const BaseResultDisplay = forwardRef(({ result, loading, children, renderSiteContent, className = '', statistics }, ref) => {
  // 创建一个内部ref引用主容器
  const containerRef = useRef(null);
  // 创建统计内容区域的ref
  const statisticsContentRef = useRef(null);
  // 生成组件实例唯一ID
  const instanceId = useId();
  // 站点内容ref映射
  const siteContentRefs = useRef({});
  // 记录活动站点内容ref
  const activeSiteContentRefs = useRef(new Set());

  // 折叠状态管理
  const [statisticsCollapsed, setStatisticsCollapsed] = useState(false);
  const [collapsedSites, setCollapsedSites] = useState({});
  
  // 收集所有站点ID
  useEffect(() => {
    if (result?.sites) {
      // 更新活动站点列表
      const newActiveSites = new Set(result.sites.map(site => site.domain));
      activeSiteContentRefs.current = newActiveSites;
      
      // 清理不再活动的站点
      Object.keys(siteContentRefs.current).forEach(siteId => {
        if (!newActiveSites.has(siteId)) {
          delete siteContentRefs.current[siteId];
        }
      });
    }
  }, [result?.sites]);

  // 监听折叠状态变化，延迟处理内容显示状态
  useEffect(() => {
    // 处理统计区域的显示状态
    if (statisticsCollapsed) {
      const timer = setTimeout(() => {
        if (statisticsContentRef.current) {
          statisticsContentRef.current.style.visibility = 'hidden';
        }
      }, 300); // 与CSS过渡时间一致
      return () => clearTimeout(timer);
    } else {
      if (statisticsContentRef.current) {
        statisticsContentRef.current.style.visibility = 'visible';
      }
    }
  }, [statisticsCollapsed]);

  // 监听站点折叠状态变化
  useEffect(() => {
    // 为每个站点更新内容可见性
    const timers = [];

    Object.keys(collapsedSites).forEach(siteId => {
      const siteContentRef = siteContentRefs.current[siteId];
      
      if (!siteContentRef || !activeSiteContentRefs.current.has(siteId)) return;
      
      if (collapsedSites[siteId]) {
        // 如果折叠，设置定时器在动画结束后隐藏
        const timer = setTimeout(() => {
          if (siteContentRef) {
            siteContentRef.style.visibility = 'hidden';
          }
        }, 300); // 与CSS过渡时间一致
        timers.push(timer);
      } else {
        // 如果展开，立即设置可见
        if (siteContentRef) {
          siteContentRef.style.visibility = 'visible';
        }
      }
    });

    return () => {
      // 清除所有定时器
      timers.forEach(timer => clearTimeout(timer));
    };
  }, [collapsedSites]);

  // 切换统计区域折叠状态
  const toggleStatisticsCollapse = useCallback(() => {
    setStatisticsCollapsed(prevState => !prevState);
  }, []);

  // 切换特定站点的折叠状态
  const toggleSiteCollapse = useCallback(siteId => {
    setCollapsedSites(prevState => ({
      ...prevState,
      [siteId]: !prevState[siteId],
    }));
  }, []);

  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    // 添加scrollIntoView方法用于滚动
    scrollIntoView: options => {
      if (containerRef.current) {
        containerRef.current.scrollIntoView(options);
      }
    },
    // 暴露组件实例ID
    getInstanceId: () => instanceId,
    // 暴露卡片元素
    getCardElement: () => containerRef.current,
  }));

  // 设置站点内容ref回调
  const setSiteContentRef = useCallback((siteId, element) => {
    if (element) {
      siteContentRefs.current[siteId] = element;
    } else if (siteContentRefs.current[siteId]) {
      delete siteContentRefs.current[siteId];
    }
  }, []);

  // 渲染处理结果统计
  const renderResultStatistics = () => {
    const stats = statistics || { totalSites: 0, successSites: 0 };
    if (stats.totalSites === 0) return null;

    return (
      <div className="result-statistics" data-instance-id={instanceId}>
        <div className="statistics-header" onClick={toggleStatisticsCollapse}>
          <Space>
            <span className={`collapse-icon ${statisticsCollapsed ? 'collapsed' : ''}`}>
              {statisticsCollapsed ? <RightOutlined /> : <DownOutlined />}
            </span>
            <BarChartOutlined />
            <Title level={5} style={{ margin: 0 }}>
              处理统计
            </Title>
          </Space>
        </div>

        <div 
          className={`statistics-content ${statisticsCollapsed ? 'collapsed' : ''}`}
          ref={statisticsContentRef}
          data-instance-id={instanceId}
        >
          <LightStatistic.Group>
            <LightStatistic
              statistic={{
                title: '站点总数',
                value: stats.totalSites,
                valueStyle: {
                  color: '#1677ff',
                },
                prefix: <GlobalOutlined />,
              }}
            />
            <LightStatistic
              statistic={{
                title: '成功站点',
                value: stats.successSites,
                valueStyle: {
                  color: '#3f8600',
                },
                suffix: `/ ${stats.totalSites}`,
                prefix: <CheckCircleOutlined />,
              }}
            />
            <LightStatistic
              statistic={{
                title: '失败站点',
                value: stats.totalSites - stats.successSites,
                valueStyle: {
                  color: stats.totalSites - stats.successSites > 0 ? '#cf1322' : '#3f8600',
                },
                suffix: `/ ${stats.totalSites}`,
                prefix: <CloseCircleOutlined />,
              }}
            />
            {children}
          </LightStatistic.Group>
        </div>
      </div>
    );
  };

  // 渲染单个站点的处理结果
  const renderSiteResult = site => {
    const siteDomain = site.domain;
    const siteDomainLabel = staticDomainOptions.find(option => option.value === site.domain)?.label;
    const isSiteCollapsed = collapsedSites[siteDomain] || false;
    const hasResults = renderSiteContent ? true : false;

    // 判断站点处理状态，修复success状态误判为error的问题
    const isSiteSuccess = site.status === 'success' || site.success === true;

    // 生成站点唯一ID
    const siteUniqueId = `${instanceId}-${siteDomain}`;

    return (
      <div className="site-result" key={siteUniqueId} data-site-unique-id={siteUniqueId}>
        <Card
          title={
            <div className="site-header" onClick={() => toggleSiteCollapse(siteDomain)}>
              <Space>
                <span className={`collapse-icon ${isSiteCollapsed ? 'collapsed' : ''}`}>
                  {isSiteCollapsed ? <RightOutlined /> : <DownOutlined />}
                </span>
                <GlobalOutlined className="site-icon" />
                <span>
                  {siteDomainLabel ? `${siteDomainLabel}: ` : ''}
                  {siteDomain}
                </span>
                {isSiteSuccess ? (
                  <Tag color="success" icon={<CheckCircleOutlined />}>
                    成功
                  </Tag>
                ) : (
                  <Tag color="error" icon={<CloseCircleOutlined />}>
                    失败
                  </Tag>
                )}
              </Space>
            </div>
          }
          extra={<Text type="secondary">{site.message || site.error}</Text>}
          className={`site-result-card ${isSiteCollapsed ? 'collapsed' : ''}`}
          data-instance-id={instanceId}
          data-site-id={siteDomain}
        >
          <div 
            className={`site-content ${isSiteCollapsed ? 'collapsed' : ''}`}
            data-site-id={siteDomain}
            data-instance-id={instanceId}
            ref={element => setSiteContentRef(siteDomain, element)}
          >
            {/* 渲染站点自定义内容 */}
            {hasResults && renderSiteContent(site)}
          </div>
        </Card>
      </div>
    );
  };

  // 渲染处理结果
  const renderProcessResults = () => {
    if (loading) {
      return (
        <div className="process-spinner">
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在处理中，请稍候...</Text>
          </div>
        </div>
      );
    }

    if (!result || !result.sites || result.sites.length === 0) {
      return <Empty description="暂无处理结果" />;
    }

    const results = result.sites || [];

    return (
      <div className="process-results-wrapper" data-instance-id={instanceId}>
        {renderResultStatistics()}

        <div className="site-results-list" data-instance-id={instanceId}>
          {results.map(site => renderSiteResult(site))}
        </div>
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <FileTextOutlined />
          <span>处理结果</span>
        </Space>
      }
      className={`result-pro-card ${className}`}
      ref={containerRef}
      data-instance-id={instanceId}
      data-component="base-result-display"
    >
      {renderProcessResults()}
    </Card>
  );
});

export default BaseResultDisplay;
