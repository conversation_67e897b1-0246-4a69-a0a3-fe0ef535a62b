/* 处理结果卡片样式 */
.result-pro-card {
  margin: 16px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
}

/* 为不同类型的结果组件添加特定样式 */
.file-results-section.result-pro-card {
  border-top: 3px solid #217346; /* Excel绿色边框 */
}

.language-results-section.result-pro-card {
  border-top: 3px solid #1890ff; /* 蓝色边框 */
}

/* 处理结果容器样式 */
.process-results-wrapper {
  padding: 16px;
}

.process-spinner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

/* 结果统计区域样式 */
.result-statistics {
  margin-bottom: 24px;
  border-radius: 12px;
  background-color: #f0f7ff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 为不同类型结果的统计区域添加特定样式 */
[data-result-type='language'] .result-statistics {
  background-color: #e6f7ff;
}

[data-result-type='file'] .result-statistics {
  background-color: #f6ffed;
}

.statistics-header {
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #e6f4ff;
  border-bottom: 1px solid #d9e8ff;
}

/* 为不同类型结果的统计头部添加特定样式 */
[data-result-type='language'] .statistics-header {
  background-color: #d6e8ff;
}

[data-result-type='file'] .statistics-header {
  background-color: #e6f5e6;
}

.statistics-header:hover {
  background-color: #d9e8ff;
}

/* 使用属性选择器来隔离样式作用域 */
[data-instance-id] .statistics-content {
  padding: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 1;
  max-height: 800px; /* 设置一个足够大的值 */
  visibility: visible;
  will-change: max-height, opacity, transform;
}

[data-instance-id] .statistics-content.collapsed {
  padding: 0;
  max-height: 0;
  opacity: 0;
  pointer-events: none;
  margin: 0;
  border: none;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  /* visibility 由 JS 设置，以确保动画结束后完全隐藏 */
}

.collapse-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  transition: transform 0.3s;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

/* 站点结果列表样式 */
.site-results-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 站点结果卡片样式 */
.site-result-card {
  border-radius: 12px;
  transition: all 0.3s;
  background-color: #ffffff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 为不同类型的结果卡片添加特定样式 */
[data-result-type='language'] .site-result-card {
  border-left: 3px solid #1890ff;
}

[data-result-type='file'] .site-result-card {
  border-left: 3px solid #217346;
}

.site-result-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.site-result-card.collapsed {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.site-header {
  cursor: pointer;
  width: 100%;
  padding: 4px 0;
}

/* 使用属性选择器和实例 ID 隔离站点内容样式 */
[data-instance-id] .site-content {
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 1;
  max-height: 10000px; /* 设置一个足够大的值，覆盖各种情况 */
  transform-origin: top;
  transform: scaleY(1);
  visibility: visible;
  will-change: max-height, opacity, transform;
}

[data-instance-id] .site-content.collapsed {
  padding: 0;
  max-height: 0;
  opacity: 0;
  pointer-events: none;
  margin: 0;
  transform: scaleY(0);
  /* visibility 由 JS 设置，以确保动画结束后完全隐藏 */
}

/* 进一步隔离不同组件实例的站点内容样式 */
[data-instance-id][data-result-type='language'] .site-content,
[data-unique-id][data-result-type='language'] .site-content {
  background-color: #f8faff;
}

[data-instance-id][data-result-type='file'] .site-content,
[data-unique-id][data-result-type='file'] .site-content {
  background-color: #f8fff8;
}

.site-icon {
  color: #1677ff;
  font-size: 16px;
}

/* 文件图标样式 */
.file-icon {
  font-size: 18px;
  margin-right: 4px;
}

.file-icon.excel {
  color: #217346;
}

.file-icon.image {
  color: #1890ff;
}

.file-icon.pdf {
  color: #ff4d4f;
}

.file-icon.word {
  color: #2b579a;
}

.file-icon.text {
  color: #722ed1;
}

.file-icon.zip {
  color: #f9a825;
}

.file-icon.unknown {
  color: #faad14;
}

.file-name {
  font-weight: 500;
}

/* 修改 LightList 组件的列表项样式 */
/* 通过父元素选择器覆盖外部组件的样式 */
.process-results-wrapper .light-list-item {
  margin: 8px 0 !important;
  border-radius: 8px !important;
  padding: 16px !important;
  border: 1px solid #f0f0f0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
  transition: all 0.3s ease !important;
}

.process-results-wrapper .light-list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1) !important;
}

.process-results-wrapper .light-list-item:last-child {
  border-bottom: 1px solid #f0f0f0 !important;
}

/* 成功/失败项样式 */
.process-results-wrapper .success-item {
  background-color: rgba(82, 196, 26, 0.08) !important;
  border-left: 3px solid #52c41a !important;
  border-radius: 8px !important;
  margin: 8px 0 !important;
  padding: 16px !important;
}

.process-results-wrapper .error-item {
  background-color: rgba(255, 77, 79, 0.08) !important;
  border-left: 3px solid #ff4d4f !important;
  border-radius: 8px !important;
  margin: 8px 0 !important;
  padding: 16px !important;
}

.process-results-wrapper .warning-item {
  background-color: rgba(250, 173, 20, 0.08) !important;
  border-left: 3px solid #faad14 !important;
  border-radius: 8px !important;
  margin: 8px 0 !important;
  padding: 16px !important;
}

/* 添加更具描述性的类名样式 */
.process-results-wrapper .existing-permission-item {
  background-color: rgba(250, 173, 20, 0.08) !important;
  border-left: 3px solid #faad14 !important;
  border-radius: 8px !important;
  margin: 8px 0 !important;
  padding: 16px !important;
}

/* 文件结果容器样式 */
.file-results-container {
  padding: 8px 0;
}

/* 文件结果项样式 */
.file-result-item {
  width: 100%;
  padding: 4px 0;
  border-radius: 8px;
  margin: 8px 0;
}

.file-result-success {
  border-left: 2px solid #52c41a;
}

.file-result-error {
  border-left: 2px solid #ff4d4f;
}

/* 菜单相关样式 */
.result-section-title {
  font-weight: 500;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  color: #1677ff;
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-icon {
  color: #722ed1;
}

.menu-results {
  margin-bottom: 24px;
}

.file-results {
  margin-top: 8px;
}

.menu-name {
  font-weight: 500;
}

/* 菜单错误提示相关样式 */
.menu-error-alert {
  margin-bottom: 8px !important;
  border-radius: 6px !important;
}

.menu-error-duplicate {
  background-color: rgba(250, 173, 20, 0.08) !important;
  border-left: 3px solid #faad14 !important;
}

.menu-error-parent {
  background-color: rgba(250, 140, 22, 0.08) !important;
  border-left: 3px solid #fa8c16 !important;
}

.menu-warning-duplicate {
  border-left: 3px solid #faad14 !important;
}

/* 权限分配状态样式 */
.permission-status {
  margin-bottom: 16px;
}

.permission-status .ant-alert {
  border-radius: 6px !important;
}

/* 添加成功状态的菜单样式 */
.menu-success-duplicate {
  border-left: 3px solid #52c41a !important;
  background-color: rgba(82, 196, 26, 0.08) !important;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .result-pro-card {
    background-color: #141414;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .process-spinner {
    background-color: #1f1f1f;
  }

  .result-statistics {
    background-color: #132339;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  }

  .statistics-header {
    background-color: #112037;
    border-bottom: 1px solid #0d1a30;
  }

  .statistics-header:hover {
    background-color: #0e1c33;
  }

  /* 暗黑模式下的菜单样式 */
  .result-section-title {
    border-bottom: 1px solid #303030;
    color: #4096ff;
  }

  .menu-icon {
    color: #9254de;
  }

  .site-result-card {
    background-color: #1f1f1f;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  }

  .site-result-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .site-result-card.collapsed {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .process-results-wrapper .light-list-item {
    border: 1px solid #303030 !important;
    border-bottom: 1px solid #303030 !important;
    background-color: #1a1a1a !important;
  }

  .process-results-wrapper .light-list-item:hover {
    background-color: #2a2a2a !important;
  }

  .process-results-wrapper .light-list-item:last-child {
    border-bottom: 1px solid #303030 !important;
  }

  .process-results-wrapper .success-item {
    background-color: rgba(82, 196, 26, 0.12) !important;
  }

  .process-results-wrapper .error-item {
    background-color: rgba(255, 77, 79, 0.12) !important;
  }

  /* 暗黑模式下已存在但权限已分配的菜单项样式 */
  .process-results-wrapper .existing-permission-item {
    background-color: rgba(250, 173, 20, 0.12) !important;
  }
}

/* 新增添加容器样式 */
.result-display-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.other-results-card {
  margin-bottom: 16px;
}

/* 为确保基础组件和子组件正确显示，添加以下样式 */
.result-pro-card {
  margin-bottom: 16px;
}

/* 确保子组件中的站点结果列表正常显示 */
.site-results-list + .site-results-list {
  margin-top: 16px;
}

/* 处理菜单和文件分区的样式 */
.menu-results + .file-results {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px dashed #f0f0f0;
}

/* 语言结果样式 */
.language-results {
  border-radius: 8px;
}

.language-icon {
  color: #1890ff;
}

.language-name {
  margin-right: 8px;
  font-weight: 500;
}

.result-section-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 通用结果项样式 */
.success-item {
  background-color: #f6ffed;
}

.error-item {
  background-color: #fff2f0;
}
