/**
 * 飞书API配置常量
 */

// 飞书开放平台API基础URL
export const FEISHU_API_BASE_URL = 'https://open.feishu.cn/open-apis';

// API端点
export const FEISHU_API_ENDPOINTS = {
  // 获取tenant_access_token
  TENANT_ACCESS_TOKEN: '/auth/v3/tenant_access_token/internal',

  // 多维表格相关API
  BITABLE: {
    // 获取多维表格信息
    GET_APP: '/bitable/v1/apps/{app_token}',
    // 获取数据表信息
    GET_TABLES: '/bitable/v1/apps/{app_token}/tables',
    // 新增记录
    CREATE_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records',
    // 查询记录
    LIST_RECORDS: '/bitable/v1/apps/{app_token}/tables/{table_id}/records/search',
    // 更新记录
    UPDATE_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}',
    // 删除记录
    DELETE_RECORD: '/bitable/v1/apps/{app_token}/tables/{table_id}/records/{record_id}',
  },
};

// 配置快照表字段映射
export const CONFIG_SNAPSHOT_FIELDS = {
  SNAPSHOT_NAME: 'snapshot_name',
  CONFIG_JSON: 'config_json',
  CONFIG_TYPE: 'config_type',
  CREATED_BY: 'created_by',
  CREATED_AT: 'created_at',
  DESCRIPTION: 'description',
};

// 配置类型枚举
export const CONFIG_TYPES = {
  SITE_CONFIG: '站点配置',
  TRANSLATION_CONFIG: '翻译配置',
  MENU_CONFIG: '菜单配置',
};

// 默认项目名称
export const DEFAULT_PROJECT_NAME = '广汽国际统括平台';

// API请求限制
export const API_LIMITS = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 毫秒
  REQUEST_TIMEOUT: 30000, // 30秒
  QPS_LIMIT: 10, // 每秒最大请求数
};

// 错误码映射
export const FEISHU_ERROR_CODES = {
  INVALID_TOKEN: 99991663,
  TOKEN_EXPIRED: 99991664,
  PERMISSION_DENIED: 99991665,
  RATE_LIMIT_EXCEEDED: 99991400,
};
