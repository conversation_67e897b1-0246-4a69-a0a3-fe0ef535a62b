/**
 * 飞书多维表格查询条件配置
 * 提供不同场景下的查询过滤条件
 */

import { CONFIG_TYPES, CONFIG_SNAPSHOT_FIELDS } from './feishuConfig.js';

/**
 * 查询条件构建器
 */
export const QueryFilters = {
  /**
   * 站点配置查询条件
   * @returns {Object} 过滤条件
   */
  siteConfig() {
    return {
      conjunction: 'and',
      conditions: [
        {
          field_name: CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE,
          operator: 'is',
          value: [CONFIG_TYPES.SITE_CONFIG],
        },
      ],
    };
  },

  /**
   * 翻译配置查询条件
   * @returns {Object} 过滤条件
   */
  translationConfig() {
    return {
      conjunction: 'and',
      conditions: [
        {
          field_name: 'config_type',
          operator: 'is',
          value: [CONFIG_TYPES.TRANSLATION_CONFIG],
        },
      ],
    };
  },

  /**
   * 菜单配置查询条件
   * @returns {Object} 过滤条件
   */
  menuConfig() {
    return {
      conjunction: 'and',
      conditions: [
        {
          field_name: 'config_type',
          operator: 'is',
          value: [CONFIG_TYPES.MENU_CONFIG],
        },
      ],
    };
  },

  /**
   * 历史记录查询条件构建器
   */
  historyQuery: {
    /**
     * 组合查询条件
     * @param {string} configType - 配置类型
     * @param {Object} params - 查询参数
     * @param {string} [params.snapshotName] - 配置名称
     * @param {Array} [params.createdAt] - 时间范围 [startTime, endTime]
     * @returns {Object} 过滤条件
     */
    buildQuery(configType, params = {}) {
      const conditions = [
        {
          field_name: CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE,
          operator: 'is',
          value: [configType],
        },
      ];

      // 添加配置名称搜索条件
      if (params.snapshotName && params.snapshotName.trim()) {
        conditions.push({
          field_name: CONFIG_SNAPSHOT_FIELDS.SNAPSHOT_NAME,
          operator: 'contains',
          value: [params.snapshotName.trim()],
        });
      }

      // 添加时间范围过滤条件
      if (params.createdAt && Array.isArray(params.createdAt) && params.createdAt.length === 2) {
        const [startTime, endTime] = params.createdAt;
        if (startTime && endTime) {
          // 确保时间精确到秒，飞书API使用毫秒时间戳
          const startTimestamp = new Date(startTime).getTime();
          const endTimestamp = new Date(endTime).getTime() + 999; // 加999毫秒确保包含整秒

          console.log('QueryFilters: 构建时间范围查询条件:', {
            startTime: startTime.toString(),
            endTime: endTime.toString(),
            startTimestamp,
            endTimestamp,
            startISO: new Date(startTimestamp).toISOString(),
            endISO: new Date(endTimestamp).toISOString(),
          });

          // 飞书多维表格API的时间字段操作符
          // 根据飞书API文档，使用正确的操作符名称
          const timeConditions = [
            {
              field_name: CONFIG_SNAPSHOT_FIELDS.CREATED_AT,
              operator: 'isGreater', // 大于
              value: [startTimestamp - 1], // 减1毫秒确保包含边界值
            },
            {
              field_name: CONFIG_SNAPSHOT_FIELDS.CREATED_AT,
              operator: 'isLess', // 小于
              value: [endTimestamp + 1], // 加1毫秒确保包含边界值
            },
          ];

          console.log('QueryFilters: 添加时间条件:', timeConditions);
          conditions.push(...timeConditions);
        }
      }

      return {
        conjunction: 'and',
        conditions,
      };
    },
  },
};

/**
 * 常用查询配置
 */
export const CommonQueries = {
  // 获取最新站点配置
  latestSiteConfig: {
    filter: QueryFilters.siteConfig(),
    pageSize: 1,
  },

  // 获取最新翻译配置
  latestTranslationConfig: {
    filter: QueryFilters.translationConfig(),
    pageSize: 1,
  },

  // 获取最新菜单配置
  latestMenuConfig: {
    filter: QueryFilters.menuConfig(),
    pageSize: 1,
  },
};
