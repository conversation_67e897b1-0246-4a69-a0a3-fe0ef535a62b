/**
 * 全局常量定义
 */

// 超时相关常量
export const TIMEOUTS = {
  LOGIN: 30000, // 30秒登录超时
  API_REQUEST: 15000, // API请求超时时间
};

// 缓存常量
export const CACHE_EXPIRES_IN_MINUTES = 60; // 登录token缓存过期时间

// OBS类型常量
export const OBS_TYPE = {
  DOWNLOAD: 1,
  UPLOAD: 2,
};

// 操作类型常量
export const OPERATION_TYPES = {
  UPLOAD: 'upload',
  MENU: 'menu',
  IMPORT_LANGUAGE: 'import_language',
};

// 页面类型常量，用于区分不同页面的处理结果
export const PAGE_TYPES = {
  OPTIONS: 'options',
  BATCH_ADD_MENU: 'batch_add_menu',
  TRANSLATION: 'translation',
  POPUP: 'popup',
};

// 消息类型常量
export const MESSAGE_TYPES = {
  GET_SITE_CONFIGS: 'GET_SITE_CONFIGS',
  SAVE_SITE_CONFIGS: 'SAVE_SITE_CONFIGS',
  CLEAR_SITE_CONFIGS: 'CLEAR_SITE_CONFIGS',
  OPEN_TRANSLATION_PAGE: 'OPEN_TRANSLATION_PAGE',
  OPEN_BATCH_MENU_PAGE: 'OPEN_BATCH_MENU_PAGE',
  PROCESS_COMPLETED: 'PROCESS_COMPLETED',
  IMPORT_MULTI_LANGUAGE: 'IMPORT_MULTI_LANGUAGE',
  START_BROWSER_AUTOMATION: 'START_BROWSER_AUTOMATION',
  GET_FILE_CONTENT: 'GET_FILE_CONTENT',
  BATCH_ADD_MENU: 'BATCH_ADD_MENU',
  BROWSER_AUTOMATION_COMPLETED: 'BROWSER_AUTOMATION_COMPLETED',
  GET_PROCESSING_STATUS: 'GET_PROCESSING_STATUS',
  CANCEL_PROCESSING: 'CANCEL_PROCESSING',
  // 云端配置上传相关
  UPLOAD_CONFIG_SNAPSHOT: 'UPLOAD_CONFIG_SNAPSHOT',
  GET_LATEST_SITE_CONFIGS: 'GET_LATEST_SITE_CONFIGS',
};

// 徽章颜色常量
export const BADGE_COLORS = {
  SUCCESS: '#4CAF50',
  ERROR: '#F44336',
};

// API路径常量
export const API_PATHS = {
  /** 登录 */
  LOGIN: '/manage/base/manage/iop/login',
  /** obs key */
  OBS_TEMP_KEY: '/manage/base/manage/iop/obs/v1/tempkey',
  /** 导入多语言 */
  IMPORT_LANGUAGE: '/manage/base/manage/iop/language/v1/import',
  /** 菜单树 */
  MENU_TREE: '/manage/base/manage/iop/sys/menu/tree',
  /** 当前登录用户信息 */
  CURRENT_USER_INFO: '/manage/base/manage/iop/sys/user/queryCurrentUserInfo',
  /** 获取用户角色和菜单 */
  USER_ROLE_MENU: '/manage/base/manage/iop/sys/role/queryRoleAndMenuByUserId',
  /** 角色菜单树 */
  ROLE_MENU_TREE: '/manage/base/manage/iop/sys/menu/treeByRole',
  /** 权限菜单分配 */
  PERMISSION_ASSIGN: '/manage/base/manage/iop/sys/menu/menuAssignPermissions',
};
