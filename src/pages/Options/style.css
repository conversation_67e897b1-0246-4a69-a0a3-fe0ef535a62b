/**
 * 整体布局样式
 */
.options-pro-layout {
  min-height: 100vh;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.options-pro-content {
  padding: 24px;
  margin-bottom: 50px;
  width: 100%;
  max-width: 1200px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

/* 卡片容器样式 */
.options-pro-content .ant-pro-page-container {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.options-pro-content .ant-pro-page-container-children-content {
  padding: 24px;
}

/* 页面标题样式 */
.options-pro-content .ant-pro-page-container-warp-page-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

/* 站点配置表单样式 - 修改为适配每行最多4个配置项 */
.ant-pro-form-list-item {
  margin-bottom: 16px;
}

.ant-pro-card.ant-pro-card-border {
  border-radius: 8px;
  transition: all 0.3s;
}

.ant-pro-card.ant-pro-card-border:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.ant-pro-form-list-action button {
  height: 36px;
}

/* 站点表单删除按钮样式 */
.ant-pro-card-title a {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.ant-pro-card-title a:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

.site-form-item-icon {
  color: #bfbfbf;
}

/* 响应式布局调整 */
@media (max-width: 575px) {
  .options-pro-content .ant-col {
    margin-bottom: 12px;
  }
}

/* 文件上传区域样式 */
.ant-upload-list {
  margin-top: 16px;
}

/* 文件上传列表样式，限制高度并添加滚动条 */
.file-upload-list-container .ant-upload-list {
  max-height: 300px; /* 最多显示约5个文件的高度 */
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #fafafa;
}

/* 自定义上传文件项样式 */
.custom-upload-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s;
  background-color: #fff;
}

.custom-upload-item:last-child {
  border-bottom: none;
}

.custom-upload-item:hover {
  background-color: #f5f5f5;
}

/* 文件序号样式 */
.file-item-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #1677ff;
  color: white;
  font-size: 12px;
  font-weight: bold;
  margin-right: 16px;
  flex-shrink: 0;
}

/* 文件图标样式 */
.file-item-icon {
  margin-right: 16px;
  flex-shrink: 0;
}

/* 文件信息样式 */
.file-item-info {
  flex: 1;
  overflow: hidden;
  min-width: 0;
}

.file-item-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item-size {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 文件操作按钮样式 */
.file-item-actions {
  margin-left: 16px;
  flex-shrink: 0;
}

.file-delete-btn {
  color: #ff4d4f;
}

.file-delete-btn:hover {
  background-color: rgba(255, 77, 79, 0.1);
}

/* 美化滚动条 */
.file-upload-list-container .ant-upload-list::-webkit-scrollbar {
  width: 6px;
}

.file-upload-list-container .ant-upload-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.file-upload-list-container .ant-upload-list::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

.ant-upload-drag {
  background-color: #fafafa !important;
  border-radius: 8px !important;
  border: 1px dashed #d9d9d9 !important;
  transition: all 0.3s;
}

.ant-upload-drag:hover {
  border-color: #1677ff !important;
  background-color: rgba(22, 119, 255, 0.02) !important;
}

/* 文件数量标签样式 */
.ant-pro-card-title .ant-tag {
  margin-left: 8px;
  font-size: 12px;
  padding: 0 8px;
  line-height: 20px;
  border-radius: 10px;
}

/* 文件上传描述样式 */
.ant-upload-drag .ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  line-height: 1.5;
}

.ant-upload-drag .ant-upload-hint div {
  margin-bottom: 4px;
}

.ant-upload-drag .ant-upload-hint div:last-child {
  margin-bottom: 0;
}

/* 文件上传标签与计数样式 */
.upload-label-with-count {
  display: flex;
  align-items: center;
}

.upload-file-count {
  margin-left: 8px;
  font-size: 14px;
  color: #1677ff;
  font-weight: normal;
}

/* 空文件列表提示 */
.empty-file-list-tip {
  padding: 16px;
  margin-top: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  text-align: center;
}

/* 进度卡片样式 */
.progress-card {
  margin: 24px 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 20px rgba(22, 119, 255, 0.2);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 处理结果样式 */
.result-pro-card {
  margin-top: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
}

.process-results-wrapper {
  padding: 16px 0;
}

.site-results-list {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.site-result-card {
  margin-bottom: 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  transition: all 0.3s;
}

.site-result-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.site-result-success {
  border-left: 3px solid #52c41a;
}

.site-result-error {
  border-left: 3px solid #ff4d4f;
}

.file-results-container {
  padding: 8px 0;
}

/* 文件结果项样式 */
.file-result-item {
  width: 100%;
  padding: 4px 0;
  border-radius: 2px;
}

.file-result-success {
  border-left: 2px solid #52c41a;
}

.file-result-error {
  border-left: 2px solid #ff4d4f;
}

/* 适配暗黑模式 */
@media (prefers-color-scheme: dark) {
  .options-pro-layout {
    background-color: #141414;
  }
  
  .options-pro-content .ant-pro-page-container {
    background-color: #1f1f1f;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }
  
  .options-pro-content .ant-pro-page-container-warp-page-header {
    border-bottom: 1px solid #303030;
  }
  
  .options-pro-content .ant-pro-page-container-warp-page-header .ant-page-header-heading-title {
    color: #1890ff;
  }
  
  .options-pro-footer {
    background-color: #141414;
    color: #aaa;
    border-top: 1px solid #303030;
  }
  
  .site-form-item-icon {
    color: #666;
  }
  
  .ant-divider {
    border-color: #303030;
  }
  
  /* 暗黑模式下的删除按钮样式 */
  .ant-pro-card-title a:hover {
    background-color: rgba(255, 77, 79, 0.2);
  }
  
  /* 暗黑模式下的ProCard样式 */
  .ant-pro-card.ant-pro-card-border {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .ant-pro-card.ant-pro-card-border:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  /* 暗黑模式下的文件上传列表滚动条 */
  .file-upload-list-container .ant-upload-list::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .file-upload-list-container .ant-upload-list::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.05);
  }
  
  .ant-upload-drag {
    background-color: #1e1e1e !important;
    border-color: #303030 !important;
  }
  
  .ant-upload-drag:hover {
    border-color: #1890ff !important;
    background-color: rgba(24, 144, 255, 0.1) !important;
  }
  
  .progress-card,
  .result-pro-card,
  .site-result-card {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .site-result-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .site-result-success {
    background-color: rgba(82, 196, 26, 0.1);
  }
  
  .site-result-error {
    background-color: rgba(255, 77, 79, 0.1);
  }
  
  .ant-pro-footer-bar {
    background-color: #1f1f1f;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
    border-top: 1px solid #303030;
  }
  
  /* 暗黑模式下的折叠图标 */
  .custom-collapse-icon {
    color: rgba(255, 255, 255, 0.45);
  }
  
  .custom-collapse-icon:hover {
    color: #1890ff;
    background-color: rgba(255, 255, 255, 0.08);
  }
  
  /* 暗黑模式下的文件上传描述 */
  .ant-upload-drag .ant-upload-hint {
    color: rgba(255, 255, 255, 0.45);
  }
  
  .ant-upload-drag .ant-upload-hint div[style*="color: #ff7875"] {
    color: #ff7875 !important;
  }
  
  /* 暗黑模式下的自定义文件列表样式 */
  .file-upload-list-container .ant-upload-list {
    border-color: #303030;
    background-color: #1a1a1a;
  }
  
  .custom-upload-item {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .custom-upload-item:hover {
    background-color: #2a2a2a;
  }
  
  .file-item-number {
    background-color: #1890ff;
  }
  
  .file-item-name {
    color: rgba(255, 255, 255, 0.85);
  }
  
  .file-item-size {
    color: rgba(255, 255, 255, 0.45);
  }
  
  .file-delete-btn {
    color: #ff7875;
  }
  
  .file-delete-btn:hover {
    background-color: rgba(255, 77, 79, 0.2);
  }
  
  /* 暗黑模式下的文件计数和空状态 */
  .upload-file-count {
    color: #1890ff;
  }
  
  .empty-file-list-tip {
    border-color: #303030;
    background-color: #1a1a1a;
  }
}

/* 站点配置卡片样式 */
.site-config-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}