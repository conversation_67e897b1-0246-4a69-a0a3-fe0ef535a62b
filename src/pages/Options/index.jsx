/* global chrome */
import React, { useState, useEffect, useRef } from 'react';
import { PageContainer, ProForm, FooterToolbar } from '@ant-design/pro-components';
import { Layout, Button, message, Progress, Alert, Space, notification, Steps } from 'antd';
import {
  SendOutlined,
  CheckCircleOutlined,
  ClearOutlined,
  LoadingOutlined,
  FileTextOutlined,
  CloudUploadOutlined,
  TranslationOutlined,
} from '@ant-design/icons';
import FileUploader from '@/components/FileUploader';
import ResultDisplay from '@/components/ResultDisplay';
import CollapsibleCard from '@/components/CollapsibleCard';
import ImportMultiLanguageModal from '@/components/ImportMultiLanguageModal';
import SiteConfigTable from '@/components/SiteConfigTable';
import { updateAllSites } from '@/components/SiteConfigTable/utils';
import { executeProcess } from '@/utils/excelHandler';
import './style.css';
import '../../styles/common.css';
import { MESSAGE_TYPES, OPERATION_TYPES, PAGE_TYPES } from '@/constants';

const { Content } = Layout;

const OptionsPage = () => {
  const [siteConfigs, setSiteConfigs] = useState([]);
  const [excelFiles, setExcelFiles] = useState([]);
  const [fileDirectory, setFileDirectory] = useState('public');
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('');
  const [processResult, setProcessResult] = useState(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [formRef] = ProForm.useForm();
  const [siteCardCollapsed, setSiteCardCollapsed] = useState(false);
  const [fileUploaderCollapsed, setFileUploaderCollapsed] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const resultCardRef = useRef(null);
  const [progressCardCollapsed, setProgressCardCollapsed] = useState(false);

  // 导入多语言相关状态
  const [importModalVisible, setImportModalVisible] = useState(false);

  const steps = [
    {
      title: '准备阶段',
      description: '准备解析文件',
      icon: <FileTextOutlined />,
    },
    {
      title: '上传阶段',
      description: '将文件上传至目标站点',
      icon: <CloudUploadOutlined />,
    },
    {
      title: '完成处理',
      description: '处理完成',
      icon: <CheckCircleOutlined />,
    },
  ];

  // 加载已保存的站点配置
  const loadSiteConfigs = () => {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({ type: 'GET_SITE_CONFIGS' }, response => {
        if (response && response.siteConfigs) {
          // 更新状态
          setSiteConfigs(response.siteConfigs);

          // 同时更新表单，保持数据同步
          updateAllSites(formRef, response.siteConfigs);
        } else {
          // 如果没有配置，初始化为空数组
          setSiteConfigs([]);
          updateAllSites(formRef, []);
        }
      });
    }
  };

  // 初始加载
  useEffect(() => {
    loadSiteConfigs();
    // 无需监听页面类型，因为Options页面是持续性接收消息，不是等待完成消息后才处理
  }, []);

  // 清除缓存的站点配置
  const clearSiteConfigs = () => {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage(
        {
          type: 'CLEAR_SITE_CONFIGS',
        },
        () => {
          setSiteConfigs([]);

          // 更新表单状态，保持数据同步
          updateAllSites(formRef, []);

          messageApi.success('站点配置已清除');
        }
      );
    }
  };

  // 处理站点配置变化
  const handleSiteConfigChange = configs => {
    // 不再过滤accountType字段，保留所有字段
    const processedConfigs = configs.map(config => {
      return config || {};
    });

    setSiteConfigs(processedConfigs);

    // 同时更新表单的sites字段，确保表单数据与状态同步
    updateAllSites(formRef, processedConfigs);

    // 保存到Chrome存储
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({
        type: 'SAVE_SITE_CONFIGS',
        siteConfigs: processedConfigs,
      });
    }
  };

  // 重置上传的文件
  const resetFiles = () => {
    setExcelFiles([]);
    setProcessResult(null);
    setProgress(0);
    setStatusMessage('');
    messageApi.success('已清除所有上传文件');
    // 重置表单中的文件字段
    formRef.setFieldValue('excelFiles', []);
  };

  // 处理目录路径变化
  const handleDirectoryChange = value => {
    setFileDirectory(value);
  };

  // 处理进度更新
  const handleProgressUpdate = (status, message, progressValue) => {
    setStatusMessage(message);
    setProgress(progressValue);
  };

  // 格式化目录路径，确保以/结尾，不以/开头
  const formatDirectory = dir => {
    if (!dir) return '';

    // 去除开头的斜杠
    let formatted = dir.startsWith('/') ? dir.slice(1) : dir;

    // 确保末尾有斜杠，除非为空
    if (formatted && !formatted.endsWith('/')) {
      formatted += '/';
    }

    return formatted;
  };

  // Helper function to convert ArrayBuffer to Base64
  function arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  // Helper function to read file as ArrayBuffer
  const readFileAsArrayBuffer = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
      reader.readAsArrayBuffer(file);
    });
  };

  // 执行处理
  const handleExecute = async () => {
    try {
      // 验证表单
      try {
        await formRef.validateFields();
      } catch (error) {
        // 表单验证失败，显示错误信息
        console.error('表单验证失败:', error);
        messageApi.error('表单验证失败，请检查所有必填项');
        return;
      }

      // 使用状态中的站点配置而不是从表单中获取
      // 从状态中获取最新的站点配置
      const allSiteConfigs = siteConfigs || [];
      console.log('allSiteConfigs', allSiteConfigs);

      // 过滤掉禁用的站点配置
      const currentSiteConfigs = allSiteConfigs.filter(config => config && config.enabled !== false);

      if (currentSiteConfigs.length === 0) {
        messageApi.error('请至少启用一个站点配置');
        return;
      }

      // 不再过滤accountType字段，保留所有字段
      const processedSiteConfigs = currentSiteConfigs.map(config => {
        return config || {};
      });

      if (excelFiles.length === 0) {
        messageApi.error('请至少上传一个Excel文件');
        return;
      }

      // 自动折叠站点配置和文件上传区域
      setSiteCardCollapsed(true);
      setFileUploaderCollapsed(true);

      // 重置状态
      setProcessing(true);
      setProgress(0);
      setCurrentStep(0);
      setStatusMessage('准备处理...');
      setProcessResult(null);

      notification.open({
        message: '开始处理',
        description: '正在准备处理文件，请耐心等待...',
        duration: 3,
        icon: <LoadingOutlined style={{ color: '#1677ff' }} />,
      });

      // 将File对象数组转换为原始文件对象，并添加目录路径信息
      const rawFiles = excelFiles.map(file => ({
        file: file.originFileObj, // This is the File object
        filePath: formatDirectory(fileDirectory) + file.name,
        contentType: file.type, // Include content type early
      }));

      // Read files and convert to Base64 before calling executeProcess
      setStatusMessage('读取文件内容...');
      const filesToSend = await Promise.all(
        rawFiles.map(async (rawFile, index) => {
          try {
            const arrayBuffer = await readFileAsArrayBuffer(rawFile.file);
            const base64Content = arrayBufferToBase64(arrayBuffer);
            // Update progress based on file reading
            const readProgress = ((index + 1) / rawFiles.length) * 20; // Assign 20% progress to reading
            handleProgressUpdate('reading', `正在读取文件: ${rawFile.filePath}`, readProgress);
            return {
              filePath: rawFile.filePath,
              contentType: rawFile.contentType,
              base64Content: base64Content,
            };
          } catch (error) {
            console.error(`读取文件 ${rawFile.filePath} 失败:`, error);
            messageApi.error(`读取文件 ${rawFile.filePath} 失败: ${error.message}`);
            // Propagate the error to stop the process
            throw new Error(`读取文件 ${rawFile.filePath} 失败: ${error.message}`);
          }
        })
      );

      // Ensure progress reflects reading completion
      handleProgressUpdate('read_complete', '文件读取完成，准备处理...', 20);

      // 文件读取完成，进入上传阶段
      setCurrentStep(1);
      notification.open({
        message: '文件读取完成',
        description: '即将开始上传处理...',
        duration: 3,
        icon: <FileTextOutlined style={{ color: '#52c41a' }} />,
      });

      // 执行处理，传入进度回调函数 和 包含Base64内容的文件数组
      const result = await executeProcess(processedSiteConfigs, filesToSend, (status, msg, prog) => {
        // Adjust progress from executeProcess (assuming it now covers the remaining 80%)
        handleProgressUpdate(status, msg, 20 + prog * 0.8);
      });

      if (result.success) {
        setCurrentStep(2);
        messageApi.success('处理完成');

        // 转换数据格式以符合ResultDisplay组件期望的结构
        const formattedResult = {
          success: result.success,
          sites: result.results.map(item => ({
            domain: item.domain,
            name: item.name,
            status: item.success ? 'success' : 'error',
            message: item.error || item.message,
            files: item.files || item.uploadResults || item.results || [],
          })),
        };

        setProcessResult(formattedResult);
        console.log('result', formattedResult);

        // 通知后台脚本
        if (chrome?.runtime?.sendMessage) {
          chrome.runtime.sendMessage({
            type: MESSAGE_TYPES.PROCESS_COMPLETED,
            operationType: OPERATION_TYPES.UPLOAD,
          });
        }
      } else {
        messageApi.error(`处理失败: ${result.error}`);
        notification.error({
          message: '处理失败',
          description: result.error,
          duration: 6,
        });

        // 转换失败结果的数据格式
        const formattedResult = {
          success: false,
          sites: [
            {
              domain: '处理失败',
              status: 'error',
              message: result.error,
              files: [],
            },
          ],
        };

        setProcessResult(formattedResult);
      }
    } catch (error) {
      console.log('error', error);
      messageApi.error(`处理出错: ${error.message}`);
      notification.error({
        message: '处理出错',
        description: error.message,
        duration: 6,
      });

      // 转换错误情况的数据格式
      const formattedResult = {
        success: false,
        sites: [
          {
            domain: '系统错误',
            status: 'error',
            message: error.message,
            files: [],
          },
        ],
      };

      setProcessResult(formattedResult);
    } finally {
      setProcessing(false);

      // 自动滚动到结果区域
      setTimeout(() => {
        if (resultCardRef.current && resultCardRef.current.scrollIntoView) {
          resultCardRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 100);
    }
  };

  // 打开导入多语言弹窗
  const showImportModal = () => {
    setImportModalVisible(true);
  };

  // 关闭导入多语言弹窗
  const closeImportModal = () => {
    setImportModalVisible(false);
  };

  // 处理站点配置卡片折叠切换
  const handleSiteCardCollapse = collapsed => {
    setSiteCardCollapsed(collapsed);
  };

  // 处理进度卡片折叠切换
  const handleProgressCardCollapse = collapsed => {
    setProgressCardCollapsed(collapsed);
  };

  // 渲染站点配置表单
  const renderSiteConfigsForm = () => {
    return (
      <CollapsibleCard
        title="站点配置"
        collapsed={siteCardCollapsed}
        onCollapse={handleSiteCardCollapse}
        className="site-config-card"
        extra={
          <Space>
            {siteConfigs.length > 0 && (
              <Button icon={<ClearOutlined />} onClick={clearSiteConfigs} danger>
                清除配置缓存
              </Button>
            )}
          </Space>
        }
      >
        <SiteConfigTable value={siteConfigs} onChange={handleSiteConfigChange} formRef={formRef} />
      </CollapsibleCard>
    );
  };

  // 替换处理进度卡片
  const renderProgressCard = () => {
    if (!processing) return null;

    return (
      <CollapsibleCard
        title="处理进度"
        className="progress-card"
        collapsed={progressCardCollapsed}
        onCollapse={handleProgressCardCollapse}
        headerBordered
        bordered
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />
          <Alert message={`正在处理: ${statusMessage}`} type="info" showIcon style={{ marginBottom: 12 }} />
          <Progress
            percent={progress}
            status="active"
            strokeColor={{
              from: '#108ee9',
              to: '#87d068',
            }}
            strokeWidth={10}
          />
        </Space>
      </CollapsibleCard>
    );
  };

  return (
    <Layout className="options-pro-layout">
      {contextHolder}
      <Content className="options-pro-content">
        <PageContainer
          header={{
            title: 'OBS 文件上传',
            subTitle: '配置站点信息并上传 Excel 文件至 OBS 服务中',
            ghost: false,
          }}
          childrenContentStyle={{ padding: 24, paddingBottom: 50 }}
          tabProps={{
            type: 'card',
          }}
          affixProps={{ offsetTop: 0 }}
        >
          <ProForm
            form={formRef}
            submitter={false}
            layout="vertical"
            onValuesChange={(_, allValues) => {
              // 处理站点配置变更
              if (allValues.sites && JSON.stringify(allValues.sites) !== JSON.stringify(siteConfigs)) {
                handleSiteConfigChange(allValues.sites);
              }
            }}
          >
            {renderSiteConfigsForm()}

            <FileUploader
              value={excelFiles}
              onChange={setExcelFiles}
              onReset={resetFiles}
              directory={fileDirectory}
              onDirectoryChange={handleDirectoryChange}
              max={10}
              collapsed={fileUploaderCollapsed}
              onCollapse={setFileUploaderCollapsed}
              messageApi={messageApi}
            />

            {renderProgressCard()}
          </ProForm>

          <FooterToolbar>
            <Space>
              <Button
                type="primary"
                size="large"
                icon={<SendOutlined />}
                onClick={handleExecute}
                loading={processing}
                style={{ outline: 'none' }}
                className="upload-button-no-focus"
                disabled={siteConfigs.length === 0 || excelFiles.length === 0 || processing}
              >
                执行
              </Button>

              <Button
                type="default"
                size="large"
                icon={<TranslationOutlined />}
                onClick={showImportModal}
                disabled={!processResult || !processResult.success || processing}
                className="upload-button-no-focus"
              >
                导入多语言
              </Button>
            </Space>
          </FooterToolbar>

          {/* 处理结果展示 */}
          {(processResult || processing) && <ResultDisplay result={processResult} loading={processing} ref={resultCardRef} />}

          {/* 导入多语言弹窗 */}
          <ImportMultiLanguageModal
            visible={importModalVisible}
            onCancel={closeImportModal}
            siteConfigs={formRef.getFieldValue('sites') || []}
            files={excelFiles}
            fileDirectory={fileDirectory}
          />
        </PageContainer>
      </Content>
    </Layout>
  );
};

export default OptionsPage;
