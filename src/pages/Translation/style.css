.translation-pro-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #f5f5f5;
}

.translation-pro-content {
  margin: 24px;
  height: 100%;
  overflow: auto;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 50px;
}

/* 表单项样式 */
.form-item {
  margin-bottom: 16px;
}

.form-item-label {
  margin-bottom: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.form-item-hint {
  margin-top: 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

/* CodeMirror 编辑器样式 */
.cm-editor {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
}

.cm-editor .cm-content {
  padding: 4px 8px;
}

.cm-editor .cm-placeholder {
  color: white;
}

/* 可能需要当使用亮色主题时的适配 */
.cm-editor.cm-focused {
  outline: none;
}

.cm-editor .cm-gutters {
  background-color: #f0f0f0;
  border-right: 1px solid #e8e8e8;
}

.cm-editor.cm-focused .cm-gutters {
  background-color: #f0f0f0;
  border-right: 1px solid #e8e8e8;
}

.cm-editor .cm-activeLineGutter {
  background-color: #e6f7ff;
}

/* JSON 编辑器错误行高亮 */
.cm-errorLine {
  background-color: rgba(255, 77, 79, 0.1) !important;
  border-left: 2px solid #ff4d4f !important;
  position: relative;
}

.cm-errorLine::before {
  content: "⚠️";
  position: absolute;
  left: -20px;
  color: #ff4d4f;
  opacity: 0.8;
}

/* 临时高亮效果，用于跳转后提示用户 */
.cm-errorLine-highlight {
  animation: errorLineFlash 2s ease-in-out;
}

@keyframes errorLineFlash {
  0%, 100% {
    background-color: rgba(255, 77, 79, 0.1);
  }
  50% {
    background-color: rgba(255, 77, 79, 0.4);
  }
}

.cm-errorGutter {
  background-color: rgba(255, 77, 79, 0.1) !important;
  color: #ff4d4f !important;
  font-weight: bold !important;
}

/* 浏览器大小下的提示工具提示 */
.cm-tooltip-lint {
  background-color: #fff;
  border: 1px solid #ff4d4f;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 4px 8px;
  font-size: 12px;
  color: #333;
  max-width: 300px;
} 