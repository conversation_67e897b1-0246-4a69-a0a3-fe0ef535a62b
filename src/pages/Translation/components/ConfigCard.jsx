import React from 'react';
import { Row, Col, Input, Select, Slider, Button, Tag, Upload, Radio } from 'antd';
import { KeyOutlined, ExperimentOutlined, GlobalOutlined, UploadOutlined, CheckCircleOutlined, FileExcelOutlined } from '@ant-design/icons';
import CollapsibleCard from '@/components/CollapsibleCard';
import { LANGUAGE_NAMES, AI_MODEL_OPTIONS, TRANSLATION_MODE, TRANSLATION_MODE_OPTIONS } from '../constants';

/**
 * 配置卡片组件
 * @param {Object} props - 组件的属性
 * @param {string} props.apiKey - 火山引擎 API 密钥
 * @param {Function} props.setApiKey - 设置火山引擎 API 密钥的函数
 * @param {string} props.aiModel - AI 模型
 * @param {Function} props.setAiModel - 设置 AI 模型的函数
 * @param {number} props.temperature - 温度系数
 * @param {string[]} props.languages - 目标语言
 * @param {Function} props.setLanguages - 设置目标语言的函数
 * @param {Object} props.existingTranslation - 已选择的翻译文件
 * @param {Function} props.handleTranslationFileUpload - 处理翻译文件上传的函数
 * @param {Function} props.removeTranslationFile - 移除翻译文件的函数
 * @param {boolean} props.configCardCollapsed - 配置卡片是否折叠
 * @param {Function} props.toggleConfigCardCollapse - 切换配置卡片折叠状态的函数
 * @param {string} props.translationMode - 翻译模式
 * @param {Function} props.setTranslationMode - 设置翻译模式的函数
 * @param {Object} props.excelFile - 已选择的Excel文件
 * @param {Function} props.handleExcelFileUpload - 处理Excel文件上传的函数
 * @param {Function} props.removeExcelFile - 移除Excel文件的函数
 * @returns {React.ReactNode} 配置卡片组件
 */
const ConfigCard = ({
  apiKey,
  setApiKey,
  aiModel,
  setAiModel,
  temperature,
  setTemperature,
  languages,
  setLanguages,
  existingTranslation,
  handleTranslationFileUpload,
  removeTranslationFile,
  configCardCollapsed,
  toggleConfigCardCollapse,
  translationMode,
  setTranslationMode,
  excelFile,
  handleExcelFileUpload,
  removeExcelFile,
}) => {
  // 语言选项
  const languageOptions = Object.entries(LANGUAGE_NAMES).map(([value, label]) => ({
    label: `${label} (${value})`,
    value,
  }));

  // 上传Excel文件前的检查和处理
  const beforeUploadExcel = file => {
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      // 阻止上传
      return Upload.LIST_IGNORE;
    }

    // 自定义上传
    const event = { target: { files: [file] } };
    if (translationMode === TRANSLATION_MODE.EXCEL_HEADER) {
      handleExcelFileUpload(event);
    } else {
      handleTranslationFileUpload(event);
    }
    // 阻止默认上传行为
    return false;
  };

  return (
    <CollapsibleCard title="API配置和翻译设置" collapsed={configCardCollapsed} onCollapse={toggleConfigCardCollapse} className="config-card">
      {/* 翻译模式选择 */}
      <Row gutter={[24, 16]}>
        <Col span={24}>
          <div className="form-item">
            <div className="form-item-label">翻译模式</div>
            <Radio.Group
              options={TRANSLATION_MODE_OPTIONS}
              onChange={e => setTranslationMode(e.target.value)}
              value={translationMode}
              optionType="button"
              buttonStyle="solid"
            />
          </div>
        </Col>
      </Row>

      <Row gutter={[24, 16]}>
        <Col xs={24} md={12}>
          <div className="form-item">
            <div className="form-item-label">
              <KeyOutlined /> 火山引擎 API 密钥 <span style={{ color: '#ff4d4f' }}>*</span>
            </div>
            <Input.Password
              value={apiKey}
              onChange={e => setApiKey(e.target.value)}
              placeholder="请输入火山引擎 API 密钥"
              style={{ width: '90%' }}
              status={!apiKey || apiKey.trim() === '' ? 'error' : ''}
            />
            <div className="form-item-hint">用于调用火山引擎 AI 模型进行翻译（必填项）</div>
          </div>
        </Col>

        <Col xs={24} md={12}>
          <div className="form-item">
            <div className="form-item-label">
              <ExperimentOutlined /> AI模型设置 <span style={{ color: '#ff4d4f' }}>*</span>
            </div>
            <Row gutter={[16, 8]}>
              <Col span={24}>
                <Select
                  style={{ width: '90%' }}
                  value={aiModel}
                  onChange={setAiModel}
                  options={AI_MODEL_OPTIONS}
                  status={!aiModel || aiModel.trim() === '' ? 'error' : ''}
                />
              </Col>
              <Col span={24}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    width: '90%',
                  }}
                >
                  <span style={{ marginRight: 8, minWidth: 80 }}>温度系数:</span>
                  <Slider min={0} max={1} step={0.1} value={temperature} onChange={setTemperature} style={{ flex: 1 }} />
                  <span style={{ marginLeft: 8, width: 40 }}>{temperature}</span>
                </div>
              </Col>
            </Row>
            <div className="form-item-hint">温度越低翻译越稳定，温度越高结果越多样</div>
          </div>
        </Col>

        <Col xs={24} md={12}>
          <div className="form-item">
            <div className="form-item-label">
              <GlobalOutlined /> 目标语言
            </div>
            <Select
              mode="multiple"
              style={{ width: '90%' }}
              placeholder="请选择目标语言"
              value={languages}
              onChange={setLanguages}
              options={languageOptions}
              maxTagCount={6}
              disabled={true}
            />
            <div className="form-item-hint">
              <Tag color="orange">注意</Tag> 目前只支持默认的6种语言，未来将开放更多语种选择
            </div>
          </div>
        </Col>

        {/* 根据翻译模式显示不同的文件上传组件 */}
        <Col xs={24} md={12}>
          {translationMode === TRANSLATION_MODE.JSON && (
            <div className="form-item">
              <div className="form-item-label">选择已有的翻译文件</div>
              <Row gutter={[16, 16]}>
                <Col xs={24}>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px',
                      }}
                    >
                      <Upload name="file" accept=".xlsx,.xls" showUploadList={false} beforeUpload={beforeUploadExcel}>
                        <Button
                          icon={<UploadOutlined />}
                          type="default"
                          style={{ outline: 'none' }}
                          className="upload-button-no-focus"
                          onMouseUp={e => e.currentTarget.blur()}
                        >
                          选择翻译文件
                        </Button>
                      </Upload>
                      <span
                        style={{
                          fontSize: '12px',
                          color: 'rgba(0, 0, 0, 0.45)',
                        }}
                      >
                        1. 只翻译新增和修改内容
                        <br />
                        2. 自动补全翻译不完整的条目
                        <br />
                        3. 文件为空时翻译所有内容
                      </span>
                    </div>

                    {existingTranslation && (
                      <div>
                        <Tag color="blue" closable onClose={removeTranslationFile} style={{ padding: '4px 8px' }}>
                          <CheckCircleOutlined style={{ marginRight: '4px' }} />
                          已选择: {existingTranslation.name}
                        </Tag>
                      </div>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          )}

          {translationMode === TRANSLATION_MODE.EXCEL_HEADER && (
            <div className="form-item">
              <div className="form-item-label">
                选择待翻译的Excel文件 <span style={{ color: '#ff4d4f' }}>*</span>
              </div>
              <Row gutter={[16, 16]}>
                <Col xs={24}>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '8px',
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '10px',
                      }}
                    >
                      <Upload name="file" accept=".xlsx,.xls" showUploadList={false} beforeUpload={beforeUploadExcel}>
                        <Button
                          icon={<FileExcelOutlined />}
                          type="default"
                          style={{ outline: 'none' }}
                          className="upload-button-no-focus"
                          onMouseUp={e => e.currentTarget.blur()}
                        >
                          选择Excel文件
                        </Button>
                      </Upload>
                      <span
                        style={{
                          fontSize: '12px',
                          color: 'rgba(0, 0, 0, 0.45)',
                        }}
                      >
                        1. 仅翻译首行表头内容
                        <br />
                        2. 生成包含多语言表头的Excel
                        <br />
                        3. 原数据保持不变
                      </span>
                    </div>

                    {excelFile && (
                      <div>
                        <Tag color="green" closable onClose={removeExcelFile} style={{ padding: '4px 8px' }}>
                          <FileExcelOutlined style={{ marginRight: '4px' }} />
                          已选择: {excelFile.name}
                        </Tag>
                      </div>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          )}
        </Col>
      </Row>
    </CollapsibleCard>
  );
};

export default ConfigCard;
