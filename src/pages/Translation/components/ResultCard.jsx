import React from "react";
import { 
  Row, 
  Col, 
  Alert, 
  Progress, 
  Divider, 
  Button, 
  Collapse, 
  Space, 
  Tag, 
  Typography,
  Steps
} from "antd";
import {
  LoadingOutlined,
  DownloadOutlined,
  CodeOutlined,
  FileExcelOutlined,
} from "@ant-design/icons";
import CollapsibleCard from "@/components/CollapsibleCard";
import { TRANSLATION_STEPS, TRANSLATION_MODE } from "../constants";

const { Text } = Typography;
const { Panel } = Collapse;

const ResultCard = ({
  translating,
  translationResult,
  currentStep,
  progress,
  processingMessage,
  excelBlob,
  excelFiles,
  zipBlob,
  downloadExcel,
  resultCardRef,
  resultCardCollapsed,
  toggleResultCardCollapse,
  translationMode,
}) => {
  if (!translating && !translationResult) return null;
  
  return (
    <CollapsibleCard
      title="翻译处理和结果"
      collapsed={resultCardCollapsed}
      onCollapse={toggleResultCardCollapse}
      className="result-card"
      cardRef={resultCardRef}
    >
      {/* 处理进度 */}
      {translating && (
        <div style={{ marginBottom: 24 }}>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Steps
                size="small"
                current={currentStep}
                items={TRANSLATION_STEPS}
              />
            </Col>
            <Col span={24}>
              <Alert
                message={processingMessage}
                type="info"
                showIcon
                icon={<LoadingOutlined />}
                style={{ marginBottom: 16 }}
              />
              <Progress percent={progress} status="active" />
            </Col>
          </Row>
        </div>
      )}
      
      {/* 处理结果 - JSON翻译模式 */}
      {translationResult && !translating && translationMode === TRANSLATION_MODE.JSON && (
        <div>
          <Alert
            message="翻译完成"
            description={
              <div>
                <p>共处理了 {translationResult.totalItems} 个翻译项，生成了 {translationResult.excelRows} 行Excel数据</p>
                {translationResult.existingItems > 0 && (
                  <p>
                    {translationResult.overwritten > 0 
                      ? `已覆盖 ${translationResult.overwritten} 条已有翻译`
                      : `已保留 ${translationResult.preserved} 条已有翻译`}
                  </p>
                )}
              </div>
            }
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Divider>翻译结果预览（前3条）</Divider>
          
          <Collapse ghost style={{ marginBottom: 16 }}>
            {translationResult.results.map((item, index) => (
              <Panel 
                header={
                  <Space>
                    <CodeOutlined />
                    <Text strong>{item.key}</Text>
                    <Tag color="blue">{item.resourceType}</Tag>
                  </Space>
                } 
                key={index}
              >
                <div style={{ marginLeft: 24 }}>
                  {Object.entries(item.translations).map(([lang, text]) => (
                    <div key={lang} style={{ marginBottom: 8 }}>
                      <Tag color={lang === 'zh-CN' ? 'green' : 'geekblue'}>{lang}</Tag>
                      <Text>{text}</Text>
                    </div>
                  ))}
                </div>
              </Panel>
            ))}
          </Collapse>
          
          {excelBlob && (
            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                size="large"
                onClick={downloadExcel}
              >
                下载Excel文件
              </Button>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  Excel文件共包含 {translationResult.excelRows} 行数据，覆盖 {translationResult.languages.length} 种语言
                </Text>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* 处理结果 - Excel表头翻译模式 */}
      {translationResult && !translating && translationMode === TRANSLATION_MODE.EXCEL_HEADER && (
        <div>
          <Alert
            message="表头翻译完成"
            description={
              <div>
                <p>已处理 {translationResult.totalSheets} 个工作表，共翻译 {translationResult.totalHeaders} 个表头</p>
                <p>已为每种语言生成单独的Excel文件，文件名格式为"{translationResult.originalFileName.replace(/\.(xlsx|xls)$/i, '')}_语言代码.xlsx"</p>
              </div>
            }
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Divider>工作表处理结果</Divider>
          
          <Collapse ghost style={{ marginBottom: 16 }}>
            {translationResult.processedSheets && translationResult.processedSheets.map((sheet, index) => (
              <Panel 
                header={
                  <Space>
                    <FileExcelOutlined />
                    <Text strong>{sheet.name}</Text>
                    <Tag color="green">已翻译 {sheet.headers} 个表头</Tag>
                  </Space>
                } 
                key={index}
              >
                <div style={{ marginLeft: 24 }}>
                  <p>生成了以下语言版本的工作表:</p>
                  <div>
                    {sheet.languages.map(lang => (
                      <Tag key={lang} color={lang === 'zh-CN' ? 'green' : 'geekblue'}>
                        {lang === 'zh-CN' ? `${lang} (原文)` : `${lang}`}
                      </Tag>
                    ))}
                  </div>
                </div>
              </Panel>
            ))}
          </Collapse>
          
          {(zipBlob || excelFiles.length > 0) && (
            <div style={{ textAlign: 'center', marginTop: 24 }}>
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                size="large"
                onClick={downloadExcel}
              >
                {zipBlob ? "下载所有翻译文件(ZIP压缩包)" : "下载多语言Excel文件"}
              </Button>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  共生成 {excelFiles.length} 个Excel文件，每个文件表头已翻译为对应语言
                </Text>
              </div>
            </div>
          )}
        </div>
      )}
    </CollapsibleCard>
  );
};

export default ResultCard; 