import React from 'react';
import BaseJsonInputCard from '@/components/BaseJsonInputCard';
import { CONFIG_TYPES } from '@/constants/feishuConfig';

// 本地存储键名
const STORAGE_KEY = 'translation_json_input';

const JsonInputCard = ({
  jsonInput,
  jsonData,
  jsonError,
  handleJsonChange,
  loadExampleJson,
  triggerFileUpload,
  fileInputRef,
  inputCardCollapsed,
  toggleInputCardCollapse,
}) => {
  return (
    <BaseJsonInputCard
      title="翻译数据输入"
      storageKey={STORAGE_KEY}
      jsonInput={jsonInput}
      jsonData={jsonData}
      jsonError={jsonError}
      handleJsonChange={handleJsonChange}
      loadExampleJson={loadExampleJson}
      triggerFileUpload={triggerFileUpload}
      fileInputRef={fileInputRef}
      inputCardCollapsed={inputCardCollapsed}
      toggleInputCardCollapse={toggleInputCardCollapse}
      downloadFileName={timestamp => `translation_keys_${timestamp}.json`}
      tipMessage="编辑器会自动保存和填充上次编辑的数据 resourceType 值类型说明：MENU 为菜单，UNIFIED_FRONT_END 为前端资源"
      enableHistory={true}
      configType={CONFIG_TYPES.TRANSLATION_CONFIG}
      successMessage={data => `JSON格式正确，共发现 ${data ? Object.keys(data).length : 0} 个翻译项`}
      placeholder="请输入JSON格式的翻译数据，或使用上方的按钮加载示例/上传JSON文件"
    />
  );
};

export default JsonInputCard;
