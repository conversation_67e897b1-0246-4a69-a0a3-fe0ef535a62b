import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>ontainer, FooterToolbar } from '@ant-design/pro-components';
import { Layout, Button, message } from 'antd';
import { TranslationOutlined } from '@ant-design/icons';
import './style.css';
import '../../styles/common.css';

// 导入常量
import {
  DEFAULT_API_KEY,
  DEFAULT_LANGUAGES,
  EXAMPLE_JSON_FILE_NAME,
  DEFAULT_AI_MODEL_VALUE,
  RESOURCE_TYPE_ENUM,
  TRANSLATION_MODE,
  DEFAULT_TRANSLATION_MODE,
} from './constants';

// 导入组件
import ConfigCard from './components/ConfigCard';
import JsonInputCard from './components/JsonInputCard';
import ResultCard from './components/ResultCard';

// 导入服务
import { translateBatch, prepareTranslationItems, filterItemsForTranslation } from '../../services/translationService';
import { readExistingTranslation, generateExcelData, createExcelBlob, downloadExcel } from '../../services/excelService';
import {
  readExcelHeaders,
  prepareHeadersForTranslation,
  translateHeaders,
  generateMultilingualHeadersExcel,
  createHeaderExcelZip,
} from '../../services/excelHeaderService';
import { MESSAGE_TYPES } from '../../constants';
import { CONFIG_TYPES } from '../../constants/feishuConfig';

// 导入工具
import { parseJsonInput, readFileAsText, loadJsonFile, delay } from '../../utils/translate';

const { Content } = Layout;

const TranslationPage = () => {
  // 状态定义
  const [messageApi, contextHolder] = message.useMessage();

  // 云端上传辅助函数（异步非阻塞）
  const uploadTranslationSnapshot = (originalConfigData, mode, context = '') => {
    // 异步发送消息，不阻塞主流程
    setTimeout(() => {
      console.log('正在发送翻译配置快照到后台上传...', mode);

      chrome.runtime.sendMessage(
        {
          type: MESSAGE_TYPES.UPLOAD_CONFIG_SNAPSHOT,
          configData: originalConfigData,
          configType: CONFIG_TYPES.TRANSLATION_CONFIG,
          context: context,
          description: `${mode}翻译完成，${context}`,
        },
        response => {
          if (response && response.success) {
            console.log('翻译配置快照上传成功:', response.recordId);
            messageApi.success('翻译配置已自动备份到云端', 3);
          } else {
            console.error('翻译配置快照上传失败:', response?.error || '上传失败');
            messageApi.info('云端备份失败，但不影响翻译功能', 2);
          }
        }
      );
    }, 100); // 延迟100ms，确保主流程完成
  };

  // 翻译模式状态
  const [translationMode, setTranslationMode] = useState(DEFAULT_TRANSLATION_MODE);

  // 配置状态
  const [apiKey, setApiKey] = useState(DEFAULT_API_KEY);
  const [languages, setLanguages] = useState(DEFAULT_LANGUAGES);
  const [aiModel, setAiModel] = useState(DEFAULT_AI_MODEL_VALUE);
  const [temperature, setTemperature] = useState(0.3);
  const [overwriteTranslations, setOverwriteTranslations] = useState(false);
  const [existingTranslation, setExistingTranslation] = useState(null);

  // Excel表头翻译状态
  const [excelFile, setExcelFile] = useState(null);

  // JSON数据状态
  const [jsonInput, setJsonInput] = useState('');
  const [jsonData, setJsonData] = useState(null);
  const [jsonError, setJsonError] = useState('');

  // 处理状态
  const [translating, setTranslating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [processingMessage, setProcessingMessage] = useState('');

  // 结果状态
  const [translationResult, setTranslationResult] = useState(null);
  const [excelBlob, setExcelBlob] = useState(null);
  // 新增：Excel文件数组状态
  const [excelFiles, setExcelFiles] = useState([]);
  const [zipBlob, setZipBlob] = useState(null);

  // UI状态
  const [configCardCollapsed, setConfigCardCollapsed] = useState(false);
  const [inputCardCollapsed, setInputCardCollapsed] = useState(false);
  const [resultCardCollapsed, setResultCardCollapsed] = useState(false);

  const resultCardRef = useRef(null);
  const fileInputRef = useRef(null);

  // 处理JSON输入变化
  const handleJsonChange = value => {
    setJsonInput(value);
    const { data, error } = parseJsonInput(value);
    setJsonData(data);
    setJsonError(error);
  };

  // 加载示例JSON
  const loadExampleJson = async () => {
    try {
      // 使用正确的相对路径，不需要区分环境
      const examplePath = EXAMPLE_JSON_FILE_NAME;
      const exampleData = await loadJsonFile(examplePath);
      const jsonString = JSON.stringify(exampleData, null, 2);
      setJsonInput(jsonString);
      handleJsonChange(jsonString);
      messageApi.success('已加载示例数据');
    } catch (error) {
      console.error('加载示例数据失败:', error);
      messageApi.error(`加载示例数据失败: ${error.message}`);
    }
  };

  // 处理JSON文件上传
  const handleFileUpload = async file => {
    if (!file) return;

    try {
      const content = await readFileAsText(file);
      // 尝试解析JSON
      JSON.parse(content); // 验证JSON格式
      setJsonInput(content);
      handleJsonChange(content);
      messageApi.success(`成功加载文件: ${file.name}`);
    } catch (error) {
      messageApi.error(`文件不是有效的JSON格式: ${error.message}`);
    }
  };

  // 触发文件选择
  const triggerFileUpload = file => {
    if (file) {
      handleFileUpload(file);
    } else {
      fileInputRef.current.click();
    }
  };

  // 处理Excel文件上传
  const handleExcelFileUpload = async event => {
    const file = event.target.files[0];
    if (!file) return;

    // 检查文件类型
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      messageApi.error('请上传Excel格式(.xlsx或.xls)的文件');
      return;
    }

    setExcelFile(file);
    messageApi.success(`已选择Excel文件: ${file.name}`);
  };

  // 移除已上传的Excel文件
  const removeExcelFile = () => {
    setExcelFile(null);
  };

  // 开始翻译处理
  const startTranslation = async () => {
    // 检查 API 密钥是否已填写
    if (!apiKey || apiKey.trim() === '') {
      messageApi.error('请输入火山引擎 API 密钥');
      return;
    }

    // 检查 AI 模型是否已选择
    if (!aiModel || aiModel.trim() === '') {
      messageApi.error('请选择 AI 模型');
      return;
    }

    // 根据翻译模式执行不同的处理逻辑
    if (translationMode === TRANSLATION_MODE.JSON) {
      await processJsonTranslation();
    } else if (translationMode === TRANSLATION_MODE.EXCEL_HEADER) {
      await processExcelHeaderTranslation();
    }
  };

  // Excel表头翻译处理
  const processExcelHeaderTranslation = async () => {
    // 检查是否选择了Excel文件
    if (!excelFile) {
      messageApi.error('请选择待翻译的Excel文件');
      return;
    }

    // 检查目标语言
    if (languages.length === 0) {
      messageApi.error('请至少选择一种目标语言');
      return;
    }

    setTranslating(true);
    setProgress(0);
    setCurrentStep(0);
    setProcessingMessage('准备翻译Excel表头...');
    setTranslationResult(null);
    setExcelBlob(null);
    setExcelFiles([]);
    setZipBlob(null);

    try {
      // 自动折叠配置卡片
      setConfigCardCollapsed(true);
      setResultCardCollapsed(false);

      // 读取Excel文件
      setProcessingMessage('正在读取Excel文件和提取表头...');
      setProgress(10);

      const excelHeaderData = await readExcelHeaders(excelFile);

      // 准备表头翻译项
      setProcessingMessage('正在分析表头数据...');
      setProgress(20);

      const headerItems = prepareHeadersForTranslation(excelHeaderData);

      if (headerItems.length === 0) {
        messageApi.error('未找到可翻译的表头');
        setTranslating(false);
        return;
      }

      setProcessingMessage(`已准备 ${headerItems.length} 个表头项目进行翻译...`);
      setProgress(30);
      setCurrentStep(1);

      // 定义进度回调
      const translationProgressCallback = (current, total, batchInfo) => {
        // 将30%-70%的进度分配给翻译过程
        const baseProgress = 30;
        const progressRange = 40; // 70 - 30
        const progressPercentage = Math.floor(baseProgress + progressRange * (current / total));

        // 更新进度和消息
        setProgress(progressPercentage);

        // 如果批次刚开始处理
        if (!batchInfo.completed) {
          setProcessingMessage(
            `正在翻译批次 ${batchInfo.currentBatch}/${batchInfo.totalBatches}，` + `共${batchInfo.batchSize}个表头项目...（${current}/${total}）`
          );
        } else {
          // 批次已完成
          setProcessingMessage(`已完成批次 ${batchInfo.currentBatch}/${batchInfo.totalBatches}，` + `共处理 ${current}/${total} 个表头项目`);
        }
      };

      // 翻译表头（使用进度回调）
      const translatedHeaders = await translateHeaders(headerItems, languages, { apiKey, aiModel, temperature }, translationProgressCallback);

      setProcessingMessage(`已完成 ${translatedHeaders.length} 个表头的翻译...`);
      setProgress(70);
      setCurrentStep(2);

      // 生成多语言表头Excel（每种语言一个文件）
      setProcessingMessage('正在生成多语言表头Excel文件...');
      setProgress(80);

      const { files, stats } = generateMultilingualHeadersExcel(excelHeaderData, translatedHeaders, languages);

      // 设置Excel文件列表
      setExcelFiles(files);

      // 创建ZIP文件
      setProcessingMessage('正在创建ZIP压缩包...');
      setProgress(90);

      const zipFileName = `${excelFile.name.replace(/\.(xlsx|xls)$/i, '')}_translations.zip`;
      const zip = await createHeaderExcelZip(files);
      setZipBlob(zip);

      // 构建结果对象
      const resultStats = {
        ...stats,
        originalFileName: excelFile.name,
        languages: languages,
        mode: TRANSLATION_MODE.EXCEL_HEADER,
        zipFileName: zipFileName,
      };

      setTranslationResult(resultStats);
      setProcessingMessage('Excel表头翻译完成！');
      setProgress(100);

      messageApi.success(`翻译完成! 共生成 ${files.length} 个Excel文件，每个文件包含翻译后的表头`);

      // 滚动到结果区域
      setTimeout(() => {
        if (resultCardRef.current) {
          resultCardRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } catch (error) {
      console.error('表头翻译处理失败:', error);
      messageApi.error(`表头翻译处理失败: ${error.message}`);
    } finally {
      setTranslating(false);
    }
  };

  // JSON翻译处理
  const processJsonTranslation = async () => {
    // 准备翻译数据
    const items = prepareTranslationItems(jsonData);
    if (!items || items.length === 0) {
      messageApi.error('没有找到有效的翻译项');
      return;
    }

    // 检查资源类型
    const { validItems, invalidItems, resourceTypes } = validateResourceTypes(items);

    /* 注释掉原有的资源类型验证错误提示
    if (invalidItems.length > 0) {
      // 构建错误消息
      let errorMessage = `检测到 ${invalidItems.length} 个项目缺少有效的资源类型。`;
      errorMessage += '有效的资源类型值为 "UNIFIED_FRONT_END" 或 "MENU"。';

      // 显示详细错误信息
      messageApi.error({
        content: errorMessage,
        duration: 8,
      });

      // 日志记录无效项目
      console.warn('以下项目缺少有效的资源类型:', invalidItems);
      return;
    }
    */

    // 显示检测到的资源类型
    if (resourceTypes.length > 0) {
      messageApi.success({
        content: `检测到资源类型: ${resourceTypes.join(', ')}`,
        duration: 3,
      });
    }

    // 使用验证后的有效项目进行后续处理
    if (validItems.length === 0) {
      messageApi.error('没有找到有效的翻译项');
      return;
    }

    if (languages.length === 0) {
      messageApi.error('请至少选择一种目标语言');
      return;
    }

    setTranslating(true);
    setProgress(0);
    setCurrentStep(0);
    setProcessingMessage('准备翻译...');
    setTranslationResult(null);
    setExcelBlob(null);
    setExcelFiles([]);
    setZipBlob(null);

    try {
      // 自动折叠配置和输入卡片
      setConfigCardCollapsed(true);
      setInputCardCollapsed(true);
      setResultCardCollapsed(false);

      // 读取已有翻译文件（如果有）
      let existingExcelData = [];
      if (existingTranslation) {
        setProcessingMessage('正在读取已有翻译文件...');
        setProgress(5);

        try {
          existingExcelData = await readExistingTranslation(existingTranslation);

          // 检查是否为空文件
          if (existingExcelData.length === 0) {
            setProcessingMessage('已上传的翻译文件为空，将跳过对比逻辑，进行全量翻译');
            messageApi.info('检测到上传的翻译文件为空，将跳过对比逻辑，翻译所有内容');
          } else {
            setProcessingMessage(`成功读取已有翻译文件，共${existingExcelData.length}条记录`);
          }
        } catch (error) {
          console.error('读取已有翻译文件失败:', error);
          messageApi.error(`读取已有翻译文件失败: ${error.message}`);
          existingExcelData = [];
        }
      }

      // 过滤需要翻译的项目，避免重复翻译
      // 使用验证后的有效项目
      const { itemsNeedingTranslation, existingResults } = filterItemsForTranslation(validItems, existingExcelData, languages, overwriteTranslations);

      // 准备翻译项
      setProcessingMessage(`准备翻译 ${itemsNeedingTranslation.length} 个项目，已有 ${existingResults.length} 个完整翻译`);
      setProgress(10);
      setCurrentStep(0);

      // 如果所有项目都已翻译且不需要覆盖，直接返回结果
      if (itemsNeedingTranslation.length === 0 && existingResults.length > 0) {
        setProcessingMessage(`所有 ${existingResults.length} 个项目已有翻译，无需调用API`);
        setProgress(90);

        // 直接生成Excel数据
        const { excelData, statsInfo } = generateExcelData(
          existingResults,
          languages,
          [], // 已经在existingResults中包含了所有已有翻译，不需要重复使用
          false // 不需要覆盖
        );

        // 创建Excel Blob
        const blob = createExcelBlob(excelData);
        setExcelBlob(blob);

        // 完成处理
        setCurrentStep(2);
        setProcessingMessage('翻译完成！(使用已有翻译)');
        setProgress(100);

        // 设置完整的统计信息
        const fullStatsInfo = {
          ...statsInfo,
          totalItems: validItems.length,
          languages: languages,
          usingExisting: true,
          mode: TRANSLATION_MODE.JSON,
        };

        setTranslationResult(fullStatsInfo);

        // 自动将生成的Excel文件设置为"已有翻译文件"
        const fileName = existingTranslation ? existingTranslation.name : `translations_${Date.now()}.xlsx`;
        const generatedFile = new File([blob], fileName, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        setExistingTranslation(generatedFile);
        messageApi.info('已自动将新生成的翻译文件设置为"已有翻译文件"');

        messageApi.success(`翻译完成! 使用了 ${existingResults.length} 个已有翻译项目，无需调用API`);

        // 滚动到结果区域
        setTimeout(() => {
          if (resultCardRef.current) {
            resultCardRef.current.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);

        setTranslating(false);
        return;
      }

      // 延迟一点，让UI有时间更新
      await delay(100);

      // 分批处理翻译
      const batchSize = 10; // 每批最多10个项目
      const translationResults = [];
      let processedCount = 0;

      for (let i = 0; i < itemsNeedingTranslation.length; i += batchSize) {
        const batch = itemsNeedingTranslation.slice(i, i + batchSize);
        setProcessingMessage(
          `正在翻译批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(itemsNeedingTranslation.length / batchSize)}，共${batch.length}个项目...`
        );

        // 调用翻译API
        try {
          const batchResult = await translateBatch({
            texts: batch.map(item => item.text),
            targetLanguages: languages.filter(lang => lang !== 'zh-CN'), // 确保不包含中文
            apiKey,
            aiModel,
            temperature,
          });

          // 处理翻译结果
          if (batchResult.items && Array.isArray(batchResult.items)) {
            batch.forEach((item, index) => {
              const translated = batchResult.items.find(r => r.index === index) || batchResult.items[index] || { translations: {} };

              // 不需要手动添加中文翻译，已在 translateBatch 中处理
              const translations = translated.translations;

              translationResults.push({
                key: item.key,
                resourceType: item.resourceType,
                translations,
              });
            });
          } else {
            throw new Error('翻译结果格式不符合预期');
          }
        } catch (error) {
          console.error('批次翻译失败:', error);
          messageApi.error(`批次翻译失败: ${error.message}，使用占位符继续`);

          // 使用占位符
          batch.forEach(item => {
            const translations = { 'zh-CN': item.text }; // 添加中文原文
            for (const lang of languages) {
              if (lang !== 'zh-CN') {
                // 只为非中文语言添加占位符
                translations[lang] = `[${lang}] ${item.text}`;
              }
            }

            translationResults.unshift({
              key: item.key,
              resourceType: item.resourceType,
              translations,
            });
          });
        }

        // 更新进度
        processedCount += batch.length;
        const newProgress = Math.floor((processedCount / itemsNeedingTranslation.length) * 70) + 10;
        setProgress(newProgress);

        // 批次间延迟，避免API限制
        if (i + batchSize < itemsNeedingTranslation.length) {
          await delay(1000);
        }
      }

      // 合并新翻译结果和已有结果
      const allResults = [...translationResults, ...existingResults];

      // 生成Excel格式
      setCurrentStep(1);
      setProcessingMessage('正在生成Excel格式...');
      setProgress(80);

      // 生成Excel数据
      const { excelData, statsInfo } = generateExcelData(
        allResults,
        languages,
        [], // 已经在allResults中包含了所有需要的翻译，不需要再传入existingExcelData
        false // 已经在过滤阶段处理了是否覆盖的逻辑
      );

      // 创建Excel Blob
      const blob = createExcelBlob(excelData);
      setExcelBlob(blob);

      // 完成处理
      setCurrentStep(2);
      setProcessingMessage('翻译完成！');
      setProgress(100);

      // 设置完整的统计信息
      const fullStatsInfo = {
        ...statsInfo,
        totalItems: validItems.length,
        newTranslations: translationResults.length,
        existingTranslations: existingResults.length,
        languages: languages,
        mode: TRANSLATION_MODE.JSON,
      };

      setTranslationResult(fullStatsInfo);

      // 云端上传翻译配置快照（上传原始JSON配置数据）
      const contextInfo = `共处理${allResults.length}个项目，新翻译${translationResults.length}项，复用${existingResults.length}项`;
      uploadTranslationSnapshot(jsonData, 'JSON翻译', contextInfo);

      // 自动将生成的Excel文件设置为"已有翻译文件"
      const fileName = existingTranslation ? existingTranslation.name : `translations_${Date.now()}.xlsx`;
      const generatedFile = new File([blob], fileName, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      setExistingTranslation(generatedFile);
      messageApi.info('已自动将新生成的翻译文件设置为"已有翻译文件"');

      let successMessage = `翻译完成! 共处理 ${allResults.length} 个项目，生成 ${excelData.length} 行Excel数据`;
      if (existingResults.length > 0) {
        successMessage += `，使用了 ${existingResults.length} 个已有翻译`;
      }

      messageApi.success(successMessage);

      // 滚动到结果区域
      setTimeout(() => {
        if (resultCardRef.current) {
          resultCardRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } catch (error) {
      console.error('翻译处理失败:', error);
      messageApi.error(`翻译处理失败: ${error.message}`);
    } finally {
      setTranslating(false);
    }
  };

  // 下载Excel文件
  const handleDownloadExcel = async () => {
    try {
      if (translationMode === TRANSLATION_MODE.JSON) {
        // JSON翻译模式 - 下载单个Excel文件
        const fileName = existingTranslation ? existingTranslation.name : `translations_${Date.now()}.xlsx`;

        downloadExcel(excelBlob, fileName);
        messageApi.success('Excel文件下载已开始');
      } else {
        // Excel表头翻译模式 - 下载ZIP压缩包
        if (zipBlob) {
          // 下载ZIP压缩包
          const zipFileName = translationResult?.zipFileName || `excel_headers_${Date.now()}.zip`;
          const url = URL.createObjectURL(zipBlob);
          const a = document.createElement('a');
          a.href = url;
          a.download = zipFileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          messageApi.success(`已开始下载包含${excelFiles.length}个Excel文件的ZIP压缩包`);
        } else {
          // 如果没有ZIP文件，单独下载每个Excel文件
          for (const file of excelFiles) {
            downloadExcel(file.blob, file.fileName);
            // 添加小延迟避免浏览器限制
            await delay(100);
          }

          messageApi.success(`已开始下载${excelFiles.length}个Excel文件`);
        }
      }
    } catch (error) {
      messageApi.error(`下载失败: ${error.message}`);
    }
  };

  // 处理配置卡片的折叠/展开
  const toggleConfigCardCollapse = collapsed => {
    setConfigCardCollapsed(collapsed);
  };

  // 处理输入卡片的折叠/展开
  const toggleInputCardCollapse = collapsed => {
    setInputCardCollapsed(collapsed);
  };

  // 处理结果卡片的折叠/展开
  const toggleResultCardCollapse = collapsed => {
    setResultCardCollapsed(collapsed);
  };

  // 处理已有翻译文件上传
  const handleTranslationFileUpload = event => {
    const file = event.target.files[0];
    if (!file) return;

    // 检查文件类型
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      messageApi.error('请上传Excel格式(.xlsx或.xls)的翻译文件');
      return;
    }

    setExistingTranslation(file);
    messageApi.success(`已选择翻译文件: ${file.name}`);
  };

  // 移除已上传的翻译文件
  const removeTranslationFile = () => {
    setExistingTranslation(null);
    setOverwriteTranslations(false);
  };

  // 添加资源类型验证函数
  const validateResourceTypes = items => {
    // 注释掉原有的验证逻辑，让所有项目都被视为有效项目
    /*
    const validItems = [];
    const invalidItems = [];
    const resourceTypesSet = new Set();

    // 定义有效的资源类型（从常量中获取）
    const validResourceTypes = Object.values(RESOURCE_TYPE_ENUM);

    items.forEach(item => {
      if (!item.resourceType) {
        // 如果没有设置资源类型，则认为是无效的
        invalidItems.push(item);
      } else {
        // 检查是否是有效的资源类型（不区分大小写）
        const normalizedType = typeof item.resourceType === 'string' ? item.resourceType.toUpperCase() : item.resourceType;

        const isValid = validResourceTypes.some(type => typeof type === 'string' && type.toUpperCase() === normalizedType);

        if (isValid) {
          validItems.push(item);
          // 保存检测到的资源类型
          resourceTypesSet.add(item.resourceType);
        } else {
          invalidItems.push(item);
        }
      }
    });
    */

    // 新逻辑：所有项目都被视为有效项目
    const validItems = items;
    const invalidItems = [];
    const resourceTypesSet = new Set();

    // 收集所有不为空的资源类型
    items.forEach(item => {
      if (item.resourceType) {
        resourceTypesSet.add(item.resourceType);
      }
    });

    return {
      validItems,
      invalidItems,
      resourceTypes: Array.from(resourceTypesSet),
    };
  };

  // 翻译模式改变时的处理
  const handleTranslationModeChange = mode => {
    setTranslationMode(mode);
    // 切换模式时重置相关状态
    setTranslationResult(null);
    setExcelBlob(null);
    setExcelFiles([]);
    setZipBlob(null);
    setProcessingMessage('');
    setProgress(0);
    setCurrentStep(0);
  };

  return (
    <Layout className="translation-pro-layout">
      {contextHolder}
      <Content className="translation-pro-content">
        <div className="content-wrapper">
          <PageContainer
            header={{
              title: '多语言翻译工具',
              subTitle: translationMode === TRANSLATION_MODE.JSON ? '将JSON格式的翻译数据转换为多语言Excel' : '将Excel表头转换为多语言格式',
              ghost: false,
            }}
            childrenContentStyle={{ padding: 16, paddingBottom: 50 }}
            tabProps={{
              type: 'card',
            }}
            affixProps={{ offsetTop: 0 }}
          >
            {/* 配置卡片 */}
            <ConfigCard
              apiKey={apiKey}
              setApiKey={setApiKey}
              aiModel={aiModel}
              setAiModel={setAiModel}
              temperature={temperature}
              setTemperature={setTemperature}
              languages={languages}
              setLanguages={setLanguages}
              existingTranslation={existingTranslation}
              overwriteTranslations={overwriteTranslations}
              setOverwriteTranslations={setOverwriteTranslations}
              handleTranslationFileUpload={handleTranslationFileUpload}
              removeTranslationFile={removeTranslationFile}
              configCardCollapsed={configCardCollapsed}
              toggleConfigCardCollapse={toggleConfigCardCollapse}
              translationMode={translationMode}
              setTranslationMode={handleTranslationModeChange}
              excelFile={excelFile}
              handleExcelFileUpload={handleExcelFileUpload}
              removeExcelFile={removeExcelFile}
            />

            {/* JSON输入卡片 - 仅在JSON翻译模式下显示 */}
            {translationMode === TRANSLATION_MODE.JSON && (
              <JsonInputCard
                jsonInput={jsonInput}
                jsonData={jsonData}
                jsonError={jsonError}
                handleJsonChange={handleJsonChange}
                loadExampleJson={loadExampleJson}
                triggerFileUpload={triggerFileUpload}
                fileInputRef={fileInputRef}
                inputCardCollapsed={inputCardCollapsed}
                toggleInputCardCollapse={toggleInputCardCollapse}
              />
            )}

            {/* 结果卡片 */}
            <ResultCard
              translating={translating}
              translationResult={translationResult}
              currentStep={currentStep}
              progress={progress}
              processingMessage={processingMessage}
              excelBlob={excelBlob}
              excelFiles={excelFiles}
              zipBlob={zipBlob}
              downloadExcel={handleDownloadExcel}
              resultCardRef={resultCardRef}
              resultCardCollapsed={resultCardCollapsed}
              toggleResultCardCollapse={toggleResultCardCollapse}
              translationMode={translationMode}
            />

            <FooterToolbar>
              <Button
                type="default"
                size="large"
                icon={<TranslationOutlined />}
                onClick={startTranslation}
                loading={translating}
                className="upload-button-no-focus"
                disabled={
                  (translationMode === TRANSLATION_MODE.JSON && !jsonData) ||
                  (translationMode === TRANSLATION_MODE.EXCEL_HEADER && !excelFile) ||
                  translating
                }
              >
                开始翻译
              </Button>
            </FooterToolbar>
          </PageContainer>
        </div>
      </Content>
    </Layout>
  );
};

export default TranslationPage;
