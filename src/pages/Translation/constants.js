// 翻译页面常量定义

// 火山引擎 API 配置
export const AI_MODEL_API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

export const DEFAULT_API_KEY = '70d2a441-2b18-425f-8c44-fe072511af7e';

// 默认支持的语言列表
export const DEFAULT_LANGUAGES = ['zh-CN', 'en-US', 'es-ES', 'pt-PT', 'ar-AR', 'th-TH'];

// 语言名称映射
export const LANGUAGE_NAMES = {
  'zh-CN': '中文',
  'en-US': '英语',
  'es-ES': '西班牙语',
  'pt-PT': '葡萄牙语',
  'ar-AR': '阿拉伯语',
  'th-TH': '泰语',
  'fr-FR': '法语',
  'de-DE': '德语',
  'it-IT': '意大利语',
  'ja-JP': '日语',
  'ko-KR': '韩语',
  'ru-RU': '俄语',
  'vi-VN': '越南语',
  'id-ID': '印尼语',
  'ms-MY': '马来语',
  'tr-TR': '土耳其语',
};

// 接入的 AI 模型选项
export const AI_MODEL_OPTIONS = [
  { label: 'Doubao-1.5-pro-32k', value: 'doubao-1.5-pro-32k-250115' },
  { label: 'Doubao-1.5-lite-32k', value: 'doubao-1.5-lite-32k-250115' },
  { label: 'Doubao-lite-32k', value: 'doubao-lite-32k-240828' },
  { label: 'Doubao-lite-4k', value: 'doubao-lite-4k-character-240828' },
  { label: 'DeepSeek-R1', value: 'deepseek-r1-250120' },
  { label: 'DeepSeek-V3', value: 'deepseek-v3-250324' },
];

export const DEFAULT_AI_MODEL_VALUE = AI_MODEL_OPTIONS[0].value;

export const RESOURCE_TYPE_ENUM = {
  UNIFIED_FRONT_END: 'UNIFIED_FRONT_END',
  MENU: 'MENU',
};

// 翻译处理步骤
export const TRANSLATION_STEPS = [
  {
    title: '准备阶段',
    description: '准备翻译数据',
  },
  {
    title: '翻译阶段',
    description: '调用翻译API处理',
  },
  {
    title: '生成Excel',
    description: '生成Excel格式',
  },
];

// 翻译模式枚举
export const TRANSLATION_MODE = {
  JSON: 'JSON', // JSON翻译模式
  EXCEL_HEADER: 'EXCEL_HEADER', // Excel表头翻译模式
};

// 翻译模式选项
export const TRANSLATION_MODE_OPTIONS = [
  { label: 'JSON翻译', value: TRANSLATION_MODE.JSON },
  { label: 'Excel表头翻译', value: TRANSLATION_MODE.EXCEL_HEADER },
];

// 默认翻译模式
export const DEFAULT_TRANSLATION_MODE = TRANSLATION_MODE.JSON;

/**
 * 示例JSON文件名
 * 在Chrome扩展环境中，路径是相对于扩展根目录的
 */
export const EXAMPLE_JSON_FILE_NAME = 'local_translations.json';
