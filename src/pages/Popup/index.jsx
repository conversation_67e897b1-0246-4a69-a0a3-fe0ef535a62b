/* global chrome */
import React from 'react';
import { Button, Card, Space, Typography, Divider } from 'antd';
import { SettingOutlined, TranslationOutlined, MenuOutlined } from '@ant-design/icons';
import './style.css';

const { Title, Text } = Typography;

const PopupPage = () => {
  // 打开选项页面
  const openOptionsPage = () => {
    if (chrome?.runtime?.openOptionsPage) {
      chrome.runtime.openOptionsPage();
    }
  };

  // 打开翻译页面
  const openTranslationPage = () => {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({ type: 'OPEN_TRANSLATION_PAGE' });
    }
  };

  // 打开批量新增菜单页面
  const openBatchAddMenuPage = () => {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({ type: 'OPEN_BATCH_MENU_PAGE' });
    }
  };

  return (
    <div className="popup-container">
      <Card className="popup-card">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <Title level={4} className="popup-title">
            广汽国际平台插件集合
          </Title>

          <Text>快速访问平台工具集合，提高工作效率</Text>

          <Button type="primary" icon={<SettingOutlined />} onClick={openOptionsPage} block>
            OBS文件上传服务
          </Button>

          <Button type="primary" icon={<TranslationOutlined />} onClick={openTranslationPage} block>
            多语言翻译工具
          </Button>

          <Button type="primary" icon={<MenuOutlined />} onClick={openBatchAddMenuPage} block>
            批量新增菜单&权限分配
          </Button>

          <Divider style={{ margin: '12px 0' }} />

          <div className="popup-footer">
            <Text type="secondary">选择功能按钮进入对应页面</Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default PopupPage;
