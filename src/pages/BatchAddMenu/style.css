.batch-menu-pro-layout {
  min-height: 100vh;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
}


.batch-menu-pro-content {
  padding: 24px;
  width: 100%;
  max-width: 1200px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

/* 卡片容器样式 */
.batch-menu-pro-content .ant-pro-page-container {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
}

.batch-menu-pro-content .ant-pro-page-container-children-content {
  padding: 24px;
}

/* 站点配置卡片样式 */
.site-config-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-card {
  margin-top: 16px;
  margin-bottom: 16px;
}