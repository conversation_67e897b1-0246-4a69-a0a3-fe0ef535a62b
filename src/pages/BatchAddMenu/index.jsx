/* global chrome */
import React, { useState, useEffect, useRef } from 'react';
import { PageContainer, ProForm, FooterToolbar } from '@ant-design/pro-components';
import { Layout, Button, message, Progress, Alert, Space, notification, Steps } from 'antd';
import { SendOutlined, CheckCircleOutlined, ClearOutlined, LoadingOutlined, MenuOutlined, CloudUploadOutlined } from '@ant-design/icons';
import SiteConfigTable from '@/components/SiteConfigTable';
import ResultDisplay from '@/components/ResultDisplay';
import CollapsibleCard from '@/components/CollapsibleCard';
import MenuJsonInputCard from './components/MenuJsonInputCard';
import { updateAllSites } from '@/components/SiteConfigTable/utils';
import { PAGE_TYPES, MESSAGE_TYPES } from '@/constants';
import { CONFIG_TYPES } from '../../constants/feishuConfig';
import './style.css';
import '../../styles/common.css';

const { Content } = Layout;

const BatchAddMenuPage = () => {
  const [siteConfigs, setSiteConfigs] = useState([]);
  const [jsonInput, setJsonInput] = useState('');
  const [menuJsonData, setMenuJsonData] = useState(null);
  const menuJsonDataRef = useRef(null);
  const [jsonError, setJsonError] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('');
  const [processResult, setProcessResult] = useState(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [formRef] = ProForm.useForm();
  const fileInputRef = useRef();
  const [siteCardCollapsed, setSiteCardCollapsed] = useState(false);
  const [menuInputCardCollapsed, setMenuInputCardCollapsed] = useState(false);

  // 云端上传辅助函数（异步非阻塞）
  const uploadMenuSnapshot = (originalMenuData, context = '') => {
    console.log('uploadMenuSnapshot 被调用:', { originalMenuData, context });

    // 异步发送消息，不阻塞主流程
    setTimeout(() => {
      console.log('正在发送菜单配置快照到后台上传...', context);

      chrome.runtime.sendMessage(
        {
          type: MESSAGE_TYPES.UPLOAD_CONFIG_SNAPSHOT,
          configData: originalMenuData,
          configType: CONFIG_TYPES.MENU_CONFIG,
          context: context,
          description: `菜单权限分配完成，${context}`,
        },
        response => {
          if (response && response.success) {
            console.log('菜单配置快照上传成功:', response.recordId);
            messageApi.success('菜单配置已自动备份到云端', 3);
          } else {
            console.error('菜单配置快照上传失败:', response?.error || '上传失败');
            messageApi.info('云端备份失败，但不影响菜单功能', 2);
          }
        }
      );
    }, 100); // 延迟100ms，确保主流程完成
  };

  // 其他状态定义
  const [currentStep, setCurrentStep] = useState(0);
  const resultCardRef = useRef(null);
  const [progressCardCollapsed, setProgressCardCollapsed] = useState(false);

  const steps = [
    {
      title: '准备阶段',
      description: '准备菜单数据',
      icon: <MenuOutlined />,
    },
    {
      title: '添加阶段',
      description: '将菜单添加至目标站点',
      icon: <CloudUploadOutlined />,
    },
    {
      title: '完成处理',
      description: '处理完成',
      icon: <CheckCircleOutlined />,
    },
  ];

  // 加载已保存的站点配置
  const loadSiteConfigs = () => {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({ type: 'GET_SITE_CONFIGS' }, response => {
        if (response && response.siteConfigs) {
          setSiteConfigs(response.siteConfigs);

          // 同步更新表单
          updateAllSites(formRef, response.siteConfigs);
        } else {
          setSiteConfigs([]);

          // 同步更新表单，设置一个默认值
          updateAllSites(formRef, [{ enabled: true }]);
        }
      });
    }
  };

  // 初始加载
  useEffect(() => {
    loadSiteConfigs();

    // 添加消息监听器处理后台返回的处理结果
    const handleMessages = message => {
      if (message.type === 'BROWSER_AUTOMATION_COMPLETED') {
        console.log('收到批量菜单处理完成消息:', message);

        // 判断页面类型，只处理与当前页面类型匹配的消息
        if (message.pageType && message.pageType !== PAGE_TYPES.BATCH_ADD_MENU) {
          console.log('消息页面类型不匹配，忽略此消息:', message.pageType);
          return;
        }

        // 处理结果
        setProcessing(false);

        // 转换数据格式以符合ResultDisplay组件期望的结构
        const formattedResult = {
          success: message.success,
          sites: message.results.map(item => ({
            domain: item.site || item.domain,
            status: item.success ? 'success' : 'error',
            message: item.error || item.message,
            menus: item.menus || [],
          })),
        };

        console.log('转换后的结果数据:', formattedResult);
        setProcessResult(formattedResult);

        // 更新进度
        handleProgressUpdate('completed', '处理完成', 100);

        // 显示通知
        if (message.success) {
          messageApi.success('处理成功');
          notification.success({
            message: '处理成功',
            description: '所有菜单添加完成！',
            duration: 4,
          });

          // 云端上传菜单配置快照（上传原始菜单配置数据）
          const currentMenuData = menuJsonDataRef.current;
          console.log('BatchAddMenu: 检查是否需要上传菜单配置快照...', {
            hasMenuJsonData: !!currentMenuData,
            menuJsonData: currentMenuData,
            messageResults: message.results,
          });

          if (currentMenuData) {
            const successSites = message.results.filter(item => item.success);
            const contextInfo = `成功处理${successSites.length}个站点，共${message.results.length}个站点`;
            console.log('BatchAddMenu: 开始上传菜单配置快照...', contextInfo);
            uploadMenuSnapshot(currentMenuData, contextInfo);
          } else {
            console.log('BatchAddMenu: menuJsonData 为空，跳过云端上传');
          }
        } else {
          messageApi.error('处理失败: ' + (message.error || message.results?.[0]?.error || '未知错误'));
          notification.error({
            message: '处理失败',
            description: message.error || message.results?.[0]?.error || '处理过程中出现错误',
            duration: 6,
          });
        }

        // 自动滚动到结果区域
        setTimeout(() => {
          if (resultCardRef.current && resultCardRef.current.scrollIntoView) {
            resultCardRef.current.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
            });
          }
        }, 100);
      }
    };

    // 注册消息监听
    if (chrome?.runtime?.onMessage) {
      chrome.runtime.onMessage.addListener(handleMessages);
    }

    // 组件卸载时移除监听器
    return () => {
      if (chrome?.runtime?.onMessage) {
        chrome.runtime.onMessage.removeListener(handleMessages);
      }
    };
  }, []);

  // 清除缓存的站点配置
  const clearSiteConfigs = () => {
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage(
        {
          type: 'CLEAR_SITE_CONFIGS',
        },
        () => {
          setSiteConfigs([]);

          // 同步更新表单，设置一个默认值
          updateAllSites(formRef, [{ enabled: true }]);

          messageApi.success('站点配置已清除');
        }
      );
    }
  };

  // 处理站点配置变化
  const handleSiteConfigChange = configs => {
    // 不过滤字段，保留所有字段
    const processedConfigs = configs.map(config => {
      return config || {};
    });

    setSiteConfigs(processedConfigs);

    // 同步更新表单
    updateAllSites(formRef, processedConfigs);

    // 保存到Chrome存储
    if (chrome?.runtime?.sendMessage) {
      chrome.runtime.sendMessage({
        type: 'SAVE_SITE_CONFIGS',
        siteConfigs: processedConfigs,
      });
    }
  };

  // 处理JSON输入变化
  const handleJsonChange = value => {
    setJsonInput(value);

    try {
      if (!value.trim()) {
        setMenuJsonData(null);
        menuJsonDataRef.current = null;
        setJsonError(null);
        return;
      }

      const parsed = JSON.parse(value);
      setMenuJsonData(parsed);
      menuJsonDataRef.current = parsed;
      setJsonError(null);
    } catch (error) {
      setMenuJsonData(null);
      menuJsonDataRef.current = null;
      setJsonError(error.message);
    }
  };

  // 加载示例JSON
  const loadExampleJson = () => {
    return new Promise(resolve => {
      const exampleJson = {
        menus: [
          {
            name: '系统管理菜单',
            parentName: '参数配置',
            parentPerms: 'param:config', // 添加父菜单权限编码
            type: 1,
            component: 'layouts/default/index',
            componentName: 'SystemManage',
            url: '/system/manage',
            icon: 'ant-design:setting-outlined',
            hide: 1,
            orderNum: 0,
            perms: 'system:manage', // 页面菜单的权限编码
            children: [
              {
                name: '系统管理-查询',
                type: 2,
                perms: 'system:manage:query', // 按钮菜单必须设置perms
                orderNum: 0,
              },
              {
                name: '系统管理-新增',
                type: 2,
                perms: 'system:manage:add', // 按钮菜单必须设置perms
                orderNum: 1,
              },
            ],
          },
          {
            name: '用户管理菜单',
            parentName: '参数配置',
            parentPerms: 'param:config', // 添加父菜单权限编码，与上面相同的父菜单
            type: 1,
            component: 'layouts/default/index',
            componentName: 'UserManage',
            url: '/user/manage',
            icon: 'ant-design:user-outlined',
            hide: 1,
            orderNum: 1,
            perms: 'user:manage', // 页面菜单的权限编码
            children: [
              {
                name: '用户管理-查询',
                type: 2,
                perms: 'user:manage:query', // 按钮菜单必须设置perms
                orderNum: 0,
              },
              {
                name: '用户管理-修改',
                type: 2,
                perms: 'user:manage:update', // 按钮菜单必须设置perms
                orderNum: 1,
              },
            ],
          },
          // 添加一个特别说明父子关系的例子
          {
            name: '第三方集成',
            parentName: '系统设置',
            parentPerms: 'system:settings', // 使用权限编码精确定位父菜单
            type: 1,
            component: 'layouts/default/index',
            componentName: 'ThirdPartyIntegration',
            url: '/system/integration',
            icon: 'ant-design:api-outlined',
            hide: 1,
            orderNum: 2,
            perms: 'system:integration', // 当前菜单的权限编码
            children: [
              {
                name: '添加集成', // 这些子菜单会自动继承父菜单关系
                type: 2,
                perms: 'system:integration:add',
                orderNum: 0,
              },
            ],
          },
        ],
      };

      const jsonString = JSON.stringify(exampleJson, null, 2);
      setJsonInput(jsonString);
      setMenuJsonData(exampleJson);
      menuJsonDataRef.current = exampleJson;
      setJsonError(null);

      resolve();
    });
  };

  // 触发文件上传
  const triggerFileUpload = file => {
    if (file) {
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target.result;
        try {
          // 尝试解析JSON
          const data = JSON.parse(content);
          setJsonInput(JSON.stringify(data, null, 2));
          setMenuJsonData(data);
          menuJsonDataRef.current = data;
          setJsonError(null);
          messageApi.success('JSON文件加载成功');
        } catch (error) {
          messageApi.error(`JSON解析失败: ${error.message}`);
          setJsonError(error.message);
        }
      };
      reader.onerror = () => {
        messageApi.error('文件读取失败');
      };
      reader.readAsText(file);
    }
  };

  // 处理进度更新
  const handleProgressUpdate = (status, message, progressValue) => {
    setStatusMessage(message);
    setProgress(progressValue);

    // 根据状态更新当前步骤，使流程更加可视化
    if (status === 'preparing' || status === 'prepare') {
      setCurrentStep(0);
    } else if (status === 'adding' || status === 'add_menu') {
      setCurrentStep(1);
    } else if (status === 'completed' || status === 'complete') {
      setCurrentStep(2);
    }
  };

  // 执行批量添加菜单
  const handleExecute = async () => {
    try {
      // 验证表单
      try {
        await formRef.validateFields();
      } catch (error) {
        console.error('表单验证失败:', error);
        messageApi.error('表单验证失败，请检查所有必填项');
        return;
      }

      // 使用状态中的站点配置
      const allSiteConfigs = siteConfigs || [];

      // 过滤掉禁用的站点配置
      const currentSiteConfigs = allSiteConfigs.filter(config => config && config.enabled !== false);

      if (currentSiteConfigs.length === 0) {
        messageApi.error('请至少启用一个站点配置');
        return;
      }

      if (!menuJsonData) {
        messageApi.error('请输入有效的菜单JSON数据');
        return;
      }

      console.log('BatchAddMenu: 开始处理时的 menuJsonData:', menuJsonData);

      if (!menuJsonData.menus || !Array.isArray(menuJsonData.menus) || menuJsonData.menus.length === 0) {
        messageApi.error('菜单数据格式不正确或为空，请检查JSON结构');
        return;
      }

      // 自动折叠站点配置和菜单输入区域
      setSiteCardCollapsed(true);
      setMenuInputCardCollapsed(true);

      // 重置状态
      setProcessing(true);
      setProgress(0);
      setCurrentStep(0);
      setStatusMessage('准备处理...');
      setProcessResult(null);
      // 确保进度卡片展开
      setProgressCardCollapsed(false);

      notification.open({
        message: '开始处理',
        description: '正在准备批量添加菜单，请耐心等待...',
        duration: 3,
        icon: <LoadingOutlined style={{ color: '#1677ff' }} />,
      });

      // 为更好的可视化，先显示准备阶段进度
      handleProgressUpdate('preparing', '准备菜单数据...', 10);

      // 准备站点配置
      const processedSiteConfigs = currentSiteConfigs.map(config => config || {});

      // 延迟一点时间后发送请求
      setTimeout(() => {
        handleProgressUpdate('preparing', '验证菜单数据结构...', 30);

        // 发送到后台脚本进行处理
        if (chrome?.runtime?.sendMessage) {
          chrome.runtime.sendMessage(
            {
              type: 'BATCH_ADD_MENU',
              siteConfigs: processedSiteConfigs,
              menuData: menuJsonData,
            },
            response => {
              if (response && response.success) {
                handleProgressUpdate('adding', '处理中...', 50);

                notification.info({
                  message: '请求已发送',
                  description: '批量添加菜单请求已发送，等待处理结果...',
                  duration: 3,
                });
              } else {
                // 请求发送失败
                setProcessing(false);
                messageApi.error(`请求发送失败: ${response?.error || '未知错误'}`);
                notification.error({
                  message: '请求发送失败',
                  description: response?.error || '未知错误',
                  duration: 6,
                });
              }
            }
          );
        } else {
          throw new Error('Chrome 扩展环境不可用');
        }
      }, 800);
    } catch (error) {
      console.log('error', error);
      // 设置错误状态
      handleProgressUpdate('error', `处理出错: ${error.message}`, 100);

      // 延迟显示错误信息，确保用户能看到进度变化
      setTimeout(() => {
        messageApi.error(`处理出错: ${error.message}`);
        notification.error({
          message: '处理出错',
          description: error.message,
          duration: 6,
        });

        // 转换错误情况的数据格式
        const formattedResult = {
          success: false,
          sites: [
            {
              domain: '系统错误',
              status: 'error',
              message: error.message,
              menus: [],
            },
          ],
        };

        setProcessResult(formattedResult);
        setProcessing(false);
      }, 1000);
    }
  };

  // 处理站点配置卡片折叠切换
  const handleSiteCardCollapse = collapsed => {
    setSiteCardCollapsed(collapsed);
  };

  // 处理进度卡片折叠切换
  const handleProgressCardCollapse = collapsed => {
    setProgressCardCollapsed(collapsed);
  };

  // 处理菜单输入卡片折叠切换
  const handleMenuInputCardCollapse = collapsed => {
    setMenuInputCardCollapsed(collapsed);
  };

  // 生成站点配置表单项
  const renderSiteConfigsForm = () => {
    return (
      <CollapsibleCard
        title="站点配置"
        collapsed={siteCardCollapsed}
        onCollapse={handleSiteCardCollapse}
        className="site-config-card"
        extra={
          <Space>
            {siteConfigs.length > 0 && (
              <Button icon={<ClearOutlined />} onClick={clearSiteConfigs} danger>
                清除配置缓存
              </Button>
            )}
          </Space>
        }
      >
        <SiteConfigTable value={siteConfigs} onChange={handleSiteConfigChange} formRef={formRef} />
      </CollapsibleCard>
    );
  };

  // 渲染进度卡片
  const renderProgressCard = () => {
    if (!processing) return null;

    return (
      <CollapsibleCard
        title="处理进度"
        className="progress-card"
        collapsed={progressCardCollapsed}
        onCollapse={handleProgressCardCollapse}
        headerBordered
        bordered
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />
          <Alert message={`正在处理: ${statusMessage}`} type="info" showIcon style={{ marginBottom: 12 }} />
          <Progress
            percent={progress}
            status="active"
            strokeColor={{
              from: '#108ee9',
              to: '#87d068',
            }}
            strokeWidth={10}
          />
        </Space>
      </CollapsibleCard>
    );
  };

  return (
    <Layout className="batch-menu-pro-layout">
      {contextHolder}
      <Content className="batch-menu-pro-content">
        <PageContainer
          header={{
            title: '批量新增菜单&权限分配',
            subTitle: '配置站点信息并批量添加菜单数据，并进行权限分配',
            ghost: false,
          }}
          childrenContentStyle={{ padding: 24, paddingBottom: 50 }}
          tabProps={{
            type: 'card',
          }}
          affixProps={{ offsetTop: 0 }}
        >
          <ProForm
            form={formRef}
            submitter={false}
            layout="vertical"
            onValuesChange={(_, allValues) => {
              // 处理站点配置变更
              if (allValues.sites && JSON.stringify(allValues.sites) !== JSON.stringify(siteConfigs)) {
                handleSiteConfigChange(allValues.sites);
              }
            }}
          >
            {renderSiteConfigsForm()}

            <MenuJsonInputCard
              jsonInput={jsonInput}
              jsonData={menuJsonData}
              jsonError={jsonError}
              handleJsonChange={handleJsonChange}
              loadExampleJson={loadExampleJson}
              triggerFileUpload={triggerFileUpload}
              fileInputRef={fileInputRef}
              inputCardCollapsed={menuInputCardCollapsed}
              toggleInputCardCollapse={handleMenuInputCardCollapse}
            />

            {renderProgressCard()}
          </ProForm>

          <FooterToolbar>
            <Button
              type="primary"
              size="large"
              icon={<SendOutlined />}
              onClick={handleExecute}
              loading={processing}
              style={{ outline: 'none' }}
              className="upload-button-no-focus"
              disabled={siteConfigs.length === 0 || !menuJsonData || jsonError || processing}
            >
              执行
            </Button>
          </FooterToolbar>

          {/* 处理结果展示 */}
          {(processResult || processing) && <ResultDisplay result={processResult} loading={processing} ref={resultCardRef} />}
        </PageContainer>
      </Content>
    </Layout>
  );
};

export default BatchAddMenuPage;
