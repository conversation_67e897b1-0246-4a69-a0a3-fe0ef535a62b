import React, { useState } from 'react';
import BaseJsonInputCard from '@/components/BaseJsonInputCard';
import { Typography, Space, Button, Modal, Tag, Alert } from 'antd';
import { QuestionCircleOutlined, MenuOutlined, FormOutlined } from '@ant-design/icons';
import { CONFIG_TYPES } from '@/constants/feishuConfig';

const { Text, Paragraph } = Typography;

// 本地存储键名
const STORAGE_KEY = 'batch_menu_json_input';

// 简化的菜单数据验证，支持 children 结构
const validateMenuData = data => {
  if (!data || typeof data !== 'object') {
    return '菜单数据必须是有效的JSON对象';
  }

  if (!data.menus || !Array.isArray(data.menus)) {
    return '菜单数据必须包含menus数组字段';
  }

  if (data.menus.length === 0) {
    return '菜单数组不能为空';
  }

  // 递归检查子菜单
  const checkChildren = menuArray => {
    for (const menu of menuArray) {
      // 检查菜单名称
      if (!menu.name) {
        return `菜单缺少必要的name字段`;
      }

      // 对于按钮菜单(type=2)，必须有perms字段
      if ((menu.type === 2 || menu.type === '2') && (!menu.perms || menu.perms.trim() === '')) {
        return `按钮菜单"${menu.name}"缺少必要的perms字段`;
      }

      // 检查子菜单
      if (menu.children) {
        if (!Array.isArray(menu.children)) {
          return `菜单"${menu.name}"的children字段必须是数组`;
        }

        if (menu.children.length > 0) {
          const childrenError = checkChildren(menu.children);
          if (childrenError) {
            return childrenError;
          }
        }
      }
    }
    return null;
  };

  // 检查所有菜单，包括子菜单
  return checkChildren(data.menus);
};

// 菜单格式说明，使用JSX替代纯文本，以提供更好的格式化
const MenuFormatTip = () => (
  <div style={{ padding: '8px 0' }}>
    <Paragraph strong>支持使用 children 数组构建层级菜单关系：</Paragraph>

    <Space direction="vertical" style={{ width: '100%' }}>
      <Alert
        message={
          <Space>
            <MenuOutlined />
            <Text strong>菜单层级结构 (使用 children)</Text>
          </Space>
        }
        description="对于依赖父菜单的子菜单项，必须严格放在父菜单的children数组中。系统支持多层嵌套的children结构，可构建复杂的菜单层级。"
        type="success"
        showIcon
      />

      <pre
        style={{
          background: '#f6f8fa',
          padding: '12px',
          borderRadius: '6px',
          fontSize: '13px',
          lineHeight: '1.5',
        }}
      >
        {`{
  "menus": [
    {
      "name": "测试页面菜单",
      "type": 1,
      "parentName": "参数配置",
      "parentPerms": "param:config", // 父菜单权限编码
      "component": "layouts/default/index",
      "componentName": "TestPage",
      "url": "/test",
      "icon": "ant-design:appstore-outlined",
      "hide": 1,  // 控制菜单显示状态: 1=展示, 2=隐藏
      "perms": "test:page", // 当前菜单权限编码
      "children": [
        {
          "name": "测试按钮",
          "type": 2,
          "perms": "test:button",
          "children": [
            {
              "name": "三级子菜单",
              "type": 2,
              "perms": "test:third:level"
            }
          ]
        }
      ]
    }
  ]
}`}
      </pre>

      <Paragraph>
        <Text strong>关键字段说明：</Text>
        <ul style={{ fontSize: '14px' }}>
          <li>
            <Text strong code>name</Text>: 菜单名称，必填，与perms组合用于唯一标识菜单
          </li>
          <li>
            <Text strong code>type</Text>: 菜单类型，1=页面菜单，2=按钮菜单，必填
          </li>
          <li>
            <Text strong code>perms</Text>: 权限编码，用于控制访问权限，对按钮菜单(type=2)是<Text mark>必填项</Text>
          </li>
          <li>
            <Text strong code>parentName</Text>/<Text strong code>parentPerms</Text>: 父菜单名称/权限编码，建议<Text mark>同时提供</Text>以精确定位父菜单
          </li>
          <li>
            <Text strong code>children</Text>: <Text mark>依赖父菜单的子菜单必须放在此数组中</Text>，支持多层嵌套
          </li>
          <li>
            <Text strong code>hide</Text>: 控制菜单显示状态 - 1=显示菜单，2=隐藏菜单
          </li>
          <li>
            <Text strong code>orderNum</Text>: 菜单排序号，决定同级菜单的显示顺序
          </li>
        </ul>
      </Paragraph>
    </Space>
  </div>
);

const MenuJsonInputCard = ({
  jsonInput,
  jsonData,
  jsonError,
  handleJsonChange,
  loadExampleJson,
  triggerFileUpload,
  fileInputRef,
  inputCardCollapsed,
  toggleInputCardCollapse,
}) => {
  // 控制格式说明弹窗的显示/隐藏
  const [formatModalVisible, setFormatModalVisible] = useState(false);

  const showFormatModal = () => {
    setFormatModalVisible(true);
  };

  const handleModalClose = () => {
    setFormatModalVisible(false);
  };

  return (
    <>
      <BaseJsonInputCard
        title="菜单数据输入"
        storageKey={STORAGE_KEY}
        jsonInput={jsonInput}
        jsonData={jsonData}
        jsonError={jsonError}
        handleJsonChange={handleJsonChange}
        loadExampleJson={loadExampleJson}
        triggerFileUpload={triggerFileUpload}
        fileInputRef={fileInputRef}
        inputCardCollapsed={inputCardCollapsed}
        toggleInputCardCollapse={toggleInputCardCollapse}
        downloadFileName={timestamp => `menu_data_${timestamp}.json`}
        tipMessage={
          <Space direction="vertical" style={{ width: '100%', marginTop: 10 }}>
            <Text>编辑器会自动保存和填充上次编辑的数据。</Text>
            <Space size="small" wrap>
              <Text>支持的菜单类型:</Text>
              <Tag color="blue" icon={<MenuOutlined />}>
                页面菜单 (type=1)
              </Tag>
              <Tag color="green" icon={<FormOutlined />}>
                按钮菜单 (type=2)
              </Tag>
              <Button type="link" size="small" icon={<QuestionCircleOutlined />} onClick={showFormatModal}>
                查看详细格式说明
              </Button>
            </Space>
          </Space>
        }
        successMessage={data => {
          // 递归计算所有菜单，包括子菜单
          const countMenusByType = menuArray => {
            let result = { total: 0, page: 0, button: 0, other: 0 };

            if (!menuArray || !Array.isArray(menuArray)) return result;

            for (const menu of menuArray) {
              result.total++;

              // 计算菜单类型
              if (menu.type === 1 || menu.type === '1' || menu.type === 'MENU') {
                result.page++;
              } else if (menu.type === 2 || menu.type === '2' || menu.type === 'BUTTON') {
                result.button++;
              } else {
                result.other++;
              }

              // 递归处理子菜单
              if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
                const childCounts = countMenusByType(menu.children);
                result.total += childCounts.total;
                result.page += childCounts.page;
                result.button += childCounts.button;
                result.other += childCounts.other;
              }
            }

            return result;
          };

          const counts = countMenusByType(data.menus);

          return `菜单数据格式正确，共 ${counts.total} 个菜单项（页面菜单：${counts.page}，按钮菜单：${counts.button}${
            counts.other > 0 ? `，其他类型：${counts.other}` : ''
          }）`;
        }}
        validateData={validateMenuData}
        placeholder="请输入JSON格式的菜单数据，或使用上方的按钮加载示例/上传JSON文件"
        enableHistory={true}
        configType={CONFIG_TYPES.MENU_CONFIG}
      />

      {/* 菜单格式说明弹窗 */}
      <Modal
        title="菜单数据格式说明"
        open={formatModalVisible}
        onCancel={handleModalClose}
        footer={[
          <Button key="close" type="primary" onClick={handleModalClose}>
            知道了
          </Button>,
        ]}
        width={700}
        styles={{ body: { maxHeight: '70vh', overflow: 'auto' } }}
      >
        <MenuFormatTip />
      </Modal>
    </>
  );
};

export default MenuJsonInputCard;
