/* global chrome */
import { MESSAGE_TYPES } from './constants';
import { stateService } from './services/stateService';
import { messageService } from './services/messageService';
import { processingService } from './services/processingService';
import { validateMenuData } from './services/menuService';
import { configSnapshotService } from './services/configSnapshotService';

// Chrome插件的后台脚本
// 处理扩展的生命周期和事件

// 扩展安装或更新时触发
chrome.runtime.onInstalled.addListener(details => {
  if (details.reason === 'install') {
    console.log('插件已安装');

    // 初始化存储
    chrome.storage.local.set({
      siteConfigs: [],
      lastProcessed: null,
    });

    // 安装后打开选项页
    chrome.tabs.create({
      url: 'options.html',
    });
  } else if (details.reason === 'update') {
    console.log('插件已更新');
  }
});

// 监听来自内容脚本或弹出窗口的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === MESSAGE_TYPES.GET_SITE_CONFIGS) {
    // 获取站点配置
    messageService.getSiteConfigs().then(siteConfigs => {
      sendResponse({ siteConfigs });
    });
    return true; // 保持消息通道打开，以便异步回复
  }

  if (message.type === MESSAGE_TYPES.SAVE_SITE_CONFIGS) {
    // 保存站点配置
    messageService.saveSiteConfigs(message.siteConfigs).then(result => {
      sendResponse(result);
    });
    return true;
  }

  if (message.type === MESSAGE_TYPES.CLEAR_SITE_CONFIGS) {
    // 清除站点配置
    messageService.clearSiteConfigs().then(result => {
      sendResponse(result);
    });
    return true;
  }

  if (message.type === MESSAGE_TYPES.OPEN_TRANSLATION_PAGE) {
    // 打开翻译页面
    messageService.openNewTab('translation.html').then(result => {
      sendResponse(result);
    });
    return true;
  }

  if (message.type === MESSAGE_TYPES.OPEN_BATCH_MENU_PAGE) {
    // 打开批量新增菜单页面
    messageService.openNewTab('batchMenu.html').then(result => {
      sendResponse(result);
    });
    return true;
  }

  if (message.type === MESSAGE_TYPES.PROCESS_COMPLETED) {
    // 处理完成通知
    messageService.saveLastProcessedTime();
    sendResponse({ success: true });
    return true;
  }

  if (message.type === MESSAGE_TYPES.IMPORT_MULTI_LANGUAGE) {
    // 处理导入多语言请求
    const { siteConfigs, files } = message;

    console.log('接收到导入多语言请求:', {
      siteCount: siteConfigs?.length || 0,
      filesCount: files?.length || 0,
      files: files?.map(f => f.name) || [],
    });

    // 验证请求数据
    if (!siteConfigs || siteConfigs.length === 0) {
      sendResponse({
        success: false,
        error: '未提供有效的站点配置',
      });
      return true;
    }

    if (!files || files.length === 0) {
      sendResponse({
        success: false,
        error: '未提供有效的文件信息',
      });
      return true;
    }

    // 初始化处理状态
    stateService.initLanguageImportState(siteConfigs, files);

    // 发送初始响应
    sendResponse({
      success: true,
      message: '已开始处理多语言导入',
    });

    // 开始处理第一个站点
    processingService.startSiteProcessing();

    return true;
  }

  if (message.type === MESSAGE_TYPES.START_BROWSER_AUTOMATION) {
    // 开始文件上传处理
    const { siteConfigs, files } = message;
    console.log(
      '文件数据结构:',
      files.map(f => ({
        filePath: f.filePath,
        fileType: f.file?.type || '未知',
        hasFile: !!f.file,
        fileStructure: Object.keys(f),
      }))
    );

    try {
      // 初始化处理状态
      const initResult = stateService.initUploadState(siteConfigs, files);

      if (initResult.success) {
        // 立即发送确认消息
        sendResponse({ success: true, message: '已开始处理' });

        // 开始处理第一个站点
        processingService.startSiteProcessing();
      } else {
        sendResponse({
          success: false,
          error: initResult.error || '初始化处理状态失败',
        });
      }
    } catch (error) {
      console.error('处理文件时出错:', error);
      sendResponse({
        success: false,
        error: error.message,
        message: '处理文件时出错',
      });
    }

    return true;
  }

  if (message.type === MESSAGE_TYPES.GET_FILE_CONTENT) {
    // 获取文件内容
    const { fileId } = message;
    const fileInfo = stateService.getFileContent(fileId);

    if (fileInfo) {
      console.log(`获取文件信息: ${fileInfo.filePath}, ID: ${fileId}`);

      // 找到原始文件对象
      const originalFile = stateService.getFiles().find(f => f.id === fileId);

      if (originalFile) {
        sendResponse({
          success: true,
          // 不再传递文件内容，只传递文件信息
          filePath: fileInfo.filePath,
          contentType: fileInfo.contentType,
          id: fileId,
        });
      } else {
        console.error(`未找到原始文件: ${fileId}`);
        sendResponse({
          success: false,
          error: '未找到原始文件',
        });
      }
    } else {
      sendResponse({
        success: false,
        error: '未找到文件内容',
      });
    }
    return true;
  }

  if (message.type === MESSAGE_TYPES.BATCH_ADD_MENU) {
    // 处理批量添加菜单的请求
    const { siteConfigs, menuData } = message;
    console.log('接收到批量添加菜单请求:', {
      siteCount: siteConfigs?.length || 0,
      menuCount: menuData?.menus?.length || 0,
      menuTypes: menuData?.menus?.map(m => m.type) || [],
    });

    // 首先验证菜单数据
    const validationError = validateMenuData(menuData);

    if (validationError) {
      sendResponse({
        success: false,
        error: `菜单数据验证失败: ${validationError}`,
        results: [],
      });
      return true;
    }

    // 初始化即刻响应
    sendResponse({
      success: true,
      message: '已开始处理批量添加菜单',
    });

    try {
      // 初始化处理状态
      stateService.initMenuState(siteConfigs, menuData);

      // 开始处理第一个站点
      processingService.startSiteProcessing();
    } catch (error) {
      console.error('批量添加菜单处理出错:', error);

      // 处理初始化错误，通过消息传递给前端
      messageService.sendProcessingCompleted(false, [
        {
          site: siteConfigs[0]?.domain || '未知站点',
          success: false,
          error: `处理初始化错误: ${error.message || '未知错误'}`,
        },
      ]);
    }

    return true; // 保持消息通道打开，以便异步回复
  }

  if (message.type === MESSAGE_TYPES.UPLOAD_CONFIG_SNAPSHOT) {
    // 处理云端配置快照上传
    handleConfigSnapshotUpload(message)
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        sendResponse({
          success: false,
          error: error.message,
        });
      });
    return true;
  }

  if (message.type === MESSAGE_TYPES.GET_LATEST_SITE_CONFIGS) {
    // 处理获取最新站点配置
    handleGetLatestSiteConfigs()
      .then(result => {
        sendResponse(result);
      })
      .catch(error => {
        sendResponse({
          success: false,
          error: error.message,
        });
      });
    return true;
  }
});

/**
 * 处理配置快照上传
 * @param {Object} message - 消息对象
 * @returns {Promise<Object>} 上传结果
 */
async function handleConfigSnapshotUpload(message) {
  try {
    const { configData, configType, context, description } = message;

    console.log('Background: 处理配置快照上传请求...');

    const result = await configSnapshotService.uploadSnapshot({
      configData,
      configType,
      context,
      description,
    });

    console.log('Background: 配置快照上传完成:', result.success ? '成功' : '失败');
    return result;
  } catch (error) {
    console.error('Background: 处理配置快照上传失败:', error);
    throw error;
  }
}

/**
 * 处理获取最新站点配置
 * @returns {Promise<Object>} 获取结果
 */
async function handleGetLatestSiteConfigs() {
  try {
    console.log('Background: 处理获取最新站点配置请求...');

    const result = await configSnapshotService.getLatestSiteConfigs();

    console.log('Background: 获取最新站点配置完成:', {
      success: result.success,
      configsCount: result.configs?.length || 0,
      message: result.message,
      snapshotInfo: result.snapshot ? {
        name: result.snapshot.name,
        createdAt: result.snapshot.createdAt,
        createdBy: result.snapshot.createdBy
      } : null
    });

    return result;
  } catch (error) {
    console.error('Background: 获取最新站点配置失败:', error);
    console.error('Background: 错误堆栈:', error.stack);
    throw error;
  }
}
