import React from 'react';
import ReactDOM from 'react-dom/client';
import TranslationPage from './pages/Translation';
import './index.css';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

// 引入Antd CSS
import 'antd/dist/reset.css';

// 创建React根节点
const root = ReactDOM.createRoot(document.getElementById('root'));

// 渲染应用
root.render(
  <React.StrictMode>
    <ConfigProvider locale={zhCN}>
      <TranslationPage />
    </ConfigProvider>
  </React.StrictMode>
); 