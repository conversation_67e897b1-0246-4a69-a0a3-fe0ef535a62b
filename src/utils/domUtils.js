/**
 * DOM操作相关工具函数
 */

/**
 * 滚动到指定选择器的元素
 * @param {Object} options - 滚动选项
 * @param {string} options.containerSelector - 容器选择器，默认'.site-config-card'
 * @param {string} options.itemSelector - 项目选择器，默认'.ant-pro-card'
 * @param {string} options.target - 目标位置，可选值：'first'|'last'|'index'，默认'last'
 * @param {number} options.index - 当target为'index'时的索引值
 * @param {number} options.delay - 延迟执行的时间（毫秒），默认100ms
 * @param {string} options.behavior - 滚动行为，'smooth'或'auto'，默认'smooth'
 * @param {string} options.block - 垂直对齐方式，默认'center'
 * @param {string} options.inline - 水平对齐方式，默认'nearest'
 * @returns {Promise<boolean>} - 是否成功滚动
 */
export const scrollToSiteItem = ({
  containerSelector = '.site-config-card',
  itemSelector = '.ant-pro-card',
  target = 'last',
  index = -1,
  delay = 100,
  behavior = 'smooth',
  block = 'center',
  inline = 'nearest'
} = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      try {
        // 1. 找到容器
        const container = document.querySelector(containerSelector);
        if (!container) {
          console.warn(`未找到容器：${containerSelector}`);
          resolve(false);
          return;
        }

        // 2. 在容器内查找所有项目
        const items = container.querySelectorAll(itemSelector);
        if (!items || items.length === 0) {
          console.warn(`在容器 ${containerSelector} 中未找到项目：${itemSelector}`);
          resolve(false);
          return;
        }

        // 3. 确定目标项目
        let targetItem;
        if (target === 'first') {
          targetItem = items[0];
        } else if (target === 'index' && index >= 0 && index < items.length) {
          targetItem = items[index];
        } else {
          // 默认滚动到最后一项
          targetItem = items[items.length - 1];
        }

        // 4. 执行滚动
        if (targetItem && targetItem.scrollIntoView) {
          targetItem.scrollIntoView({
            behavior,
            block,
            inline
          });
          resolve(true);
        } else {
          console.warn('无法滚动到目标元素');
          resolve(false);
        }
      } catch (error) {
        console.error('滚动到元素时出错:', error);
        resolve(false);
      }
    }, delay);
  });
}; 