/**
 * Excel处理工具模块
 * 提供通用的Excel文件读取、处理和生成功能
 */
import * as XLSX from 'xlsx';
import { readFileAsArrayBuffer } from './fileUtils';

/**
 * 读取Excel文件
 * @param {File} file - Excel文件对象
 * @param {Object} options - 读取选项
 * @param {boolean} [options.header=false] - 是否使用header:1模式读取
 * @param {boolean} [options.extractHeaders=false] - 是否提取表头
 * @returns {Promise<Object>} 包含工作簿和工作表数据的对象
 */
export const readExcelFile = async (file, options = {}) => {
  if (!file) {
    throw new Error("未提供Excel文件");
  }
  
  try {
    const fileData = await readFileAsArrayBuffer(file);
    const workbook = XLSX.read(fileData, { type: 'array' });
    
    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new Error("Excel文件不包含任何工作表");
    }
    
    // 默认读取模式
    if (!options.extractHeaders) {
      // 读取第一个工作表数据
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      return {
        fileName: file.name,
        data: jsonData,
        workbook
      };
    } 
    // 提取表头和数据模式
    else {
      const sheets = {};
      
      // 处理每个工作表
      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        
        // 将工作表数据转换为JSON数组
        const sheetData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (sheetData.length === 0) {
          // 工作表为空，跳过处理
          continue;
        }
        
        // 第一行为表头
        const headers = sheetData[0];
        
        // 保存工作表信息
        sheets[sheetName] = {
          headers,
          data: sheetData.slice(1), // 数据行（排除表头）
          originalWorksheet: worksheet
        };
      }
      
      return {
        fileName: file.name,
        sheets,
        workbook
      };
    }
  } catch (error) {
    console.error("读取Excel文件失败:", error);
    throw new Error(`读取Excel文件失败: ${error.message}`);
  }
};

/**
 * 创建Excel工作簿并添加工作表
 * @param {Array} data - 数据数组
 * @param {Object} options - 选项
 * @param {boolean} [options.isJsonData=true] - 数据是否为JSON对象数组，否则为二维数组
 * @param {string} [options.sheetName="Sheet1"] - 工作表名称
 * @returns {Object} XLSX工作簿对象
 */
export const createExcelWorkbook = (data, options = { isJsonData: true, sheetName: "Sheet1" }) => {
  const workbook = XLSX.utils.book_new();
  
  // 根据数据类型转换为工作表
  let worksheet;
  if (options.isJsonData) {
    worksheet = XLSX.utils.json_to_sheet(data);
  } else {
    worksheet = XLSX.utils.aoa_to_sheet(data);
  }
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, options.sheetName);
  
  return workbook;
};

/**
 * 将工作簿转换为Blob对象
 * @param {Object} workbook - XLSX工作簿对象
 * @returns {Blob} Excel文件的Blob对象
 */
export const workbookToBlob = (workbook) => {
  const excelBlobData = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  return new Blob([excelBlobData], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
};

/**
 * 生成Excel文件Blob
 * @param {Array} data - 数据数组
 * @param {Object} options - 选项
 * @param {boolean} [options.isJsonData=true] - 数据是否为JSON对象数组
 * @param {string} [options.sheetName="Sheet1"] - 工作表名称
 * @returns {Blob} Excel文件Blob对象
 */
export const generateExcelBlob = (data, options = {}) => {
  const workbook = createExcelWorkbook(data, options);
  return workbookToBlob(workbook);
}; 