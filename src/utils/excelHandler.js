/* global chrome */
import { staticDomainOptions } from '../components/constants';
import { MESSAGE_TYPES } from '../constants';

/**
 * 使用站点配置处理Excel数据
 * @param {Object} excelData - Excel数据对象
 * @param {Array} siteConfigs - 站点配置数组
 * @returns {Object} - 处理结果
 */
export const processSiteData = (excelData, siteConfigs) => {
  const results = {};

  // 遍历所有站点配置
  for (const site of siteConfigs) {
    const domain = site.domain;
    results[domain] = {
      config: site,
      processedData: [],
    };

    // 遍历Excel文件数据
    Object.entries(excelData).forEach(([filePath, fileInfo]) => {
      if (fileInfo.data && Array.isArray(fileInfo.data)) {
        // 假设我们需要处理的Excel数据，这里仅做示例
        // 实际处理逻辑需要根据具体业务需求来调整
        const processedItems = fileInfo.data.map(item => ({
          ...item,
          processed: true,
          filePath,
        }));

        results[domain].processedData.push(...processedItems);
      }
    });
  }

  return results;
};

/**
 * 登录站点并上传文件 - 浏览器自动化方式
 * @param {Object} siteConfig - 站点配置
 * @param {Array<{file: File, filePath: string}>} files - 要上传的文件数组
 * @param {Function} onProgress - 进度回调函数 (status, message, progress) => void
 * @returns {Promise<Object>} - 处理结果
 */
export const loginAndUploadFiles = async (siteConfig, files, onProgress) => {
  return new Promise(resolve => {
    const domainLabel = staticDomainOptions.find(option => option.value === siteConfig.domain)?.label;
    const domain = siteConfig.domain;
    onProgress?.('login', `正在使用浏览器自动化方式登录站点: ${domainLabel}: ${domain}...`, 0);

    // 使用background.js中的浏览器自动化处理
    const messageHandler = response => {
      // 移除消息监听
      chrome.runtime.onMessage.removeListener(messageHandler);
      console.log('response', response);
      if (response && response.type === MESSAGE_TYPES.BROWSER_AUTOMATION_COMPLETED) {
        // 找到当前站点的处理结果
        const siteResult = response.results.find(result => result.domain === siteConfig.domain);

        if (siteResult) {
          onProgress?.('complete', `站点 ${domainLabel}: ${domain} 处理完成`, 100);
          resolve(siteResult);
        } else {
          onProgress?.('error', `站点 ${domainLabel}: ${domain} 处理失败：未找到处理结果`, 0);
          resolve({
            site: siteConfig.domain,
            success: false,
            error: '未找到处理结果',
          });
        }
      }
    };

    // 监听自动化完成消息
    chrome.runtime.onMessage.addListener(messageHandler);

    // 设置超时处理，避免无限等待
    const timeoutId = setTimeout(() => {
      // 移除消息监听
      chrome.runtime.onMessage.removeListener(messageHandler);
      onProgress?.('error', `站点 ${domainLabel}: ${domain} 处理超时`, 0);
      resolve({
        site: siteConfig.domain,
        success: false,
        error: '处理超时，可能是连接问题导致',
      });
    }, 120000); // 设置2分钟超时

    // 发送开始浏览器自动化的消息
    try {
      chrome.runtime.sendMessage(
        {
          type: 'START_BROWSER_AUTOMATION',
          siteConfigs: [siteConfig], // 只处理当前站点
          files: files, // 传递文件数组
        },
        response => {
          // 检查是否有错误
          if (chrome.runtime.lastError) {
            console.error('发送自动化消息时出错:', chrome.runtime.lastError);
            clearTimeout(timeoutId);
            chrome.runtime.onMessage.removeListener(messageHandler);
            resolve({
              site: siteConfig.domain,
              success: false,
              error: `消息发送失败: ${chrome.runtime.lastError.message}`,
            });
          }
          // 如果有响应但没有错误，记录日志
          else if (response) {
            console.log('收到自动化启动确认:', response);
          }
        }
      );
    } catch (error) {
      console.error('发送自动化消息时发生异常:', error);
      clearTimeout(timeoutId);
      chrome.runtime.onMessage.removeListener(messageHandler);
      resolve({
        site: siteConfig.domain,
        success: false,
        error: `发送消息异常: ${error.message}`,
      });
    }
  });
};

/**
 * 执行整个处理流程
 * @param {Array} siteConfigs - 站点配置数组
 * @param {Array<{file: File, filePath: string}>} excelFiles - 带路径的Excel文件数组
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Object>} - 处理结果
 */
export const executeProcess = async (siteConfigs, excelFiles, onProgress) => {
  try {
    const results = [];
    const totalSites = siteConfigs.length;

    // 对每个站点配置进行处理
    for (let i = 0; i < totalSites; i++) {
      const siteConfig = siteConfigs[i];

      onProgress?.('site', `处理站点 ${i + 1}/${totalSites}: ${siteConfig.domain}`, Math.round((i / totalSites) * 100));

      // 处理当前站点的所有文件
      const siteResult = await loginAndUploadFiles(siteConfig, excelFiles, (status, message, progress) => {
        // 将站点进度转换为总体进度
        const totalProgress = Math.round(((i + progress / 100) / totalSites) * 100);
        onProgress?.(status, message, totalProgress);
      });

      results.push(siteResult);
    }

    onProgress?.('complete', '所有站点处理完成', 100);

    return {
      success: true,
      results,
    };
  } catch (error) {
    onProgress?.('error', `处理出错: ${error.message}`, 0);
    return {
      success: false,
      error: error.message,
    };
  }
};
