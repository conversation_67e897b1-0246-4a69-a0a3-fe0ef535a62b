/**
 * 解析JSON输入
 * @param {string} jsonString - JSON字符串
 * @returns {Object} 解析结果，包含data和error
 */
export const parseJsonInput = (jsonString) => {
  if (!jsonString.trim()) {
    return { data: null, error: "" };
  }
  
  try {
    const data = JSON.parse(jsonString);
    return { data, error: "" };
  } catch (error) {
    return { 
      data: null, 
      error: `JSON解析错误: ${error.message}` 
    };
  }
};

/**
 * 异步加载JSON文件
 * @param {string} filePath - 文件路径
 * @returns {Promise<Object>} JSON数据
 */
export const loadJsonFile = async (filePath) => {
  try {
    const response = await fetch(filePath);
    if (!response.ok) {
      throw new Error(`加载失败: ${response.status} ${response.statusText}`);
    }
    return await response.json();
  } catch (error) {
    console.error("加载JSON数据失败:", error);
    throw error;
  }
};

/**
 * 读取上传的文件为文本
 * @param {File} file - 文件对象
 * @returns {Promise<string>} 文件内容
 */
export const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error("没有提供文件"));
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = () => reject(new Error("读取文件失败"));
    reader.readAsText(file);
  });
};

/**
 * 延迟函数
 * @param {number} ms - 毫秒数
 * @returns {Promise} 延迟Promise
 */
export const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms)); 