/**
 * 日期时间工具函数
 * 统一处理项目中的日期时间格式化需求
 */

/**
 * 格式化日期时间为 YYYY-MM-DD HH:mm:ss
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @returns {string} 格式化后的日期时间字符串
 */
export const formatDateTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 格式化日期时间为简短格式 YYYY-MM-DD HH:mm
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @returns {string} 格式化后的简短日期时间字符串
 */
export const formatShortDateTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

/**
 * 格式化日期为 YYYY-MM-DD
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @returns {string} 格式化后的日期字符串
 */
export const formatDate = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * 格式化时间为 HH:mm:ss
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
};

/**
 * 获取相对时间描述
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @returns {string} 相对时间描述
 */
export const getRelativeTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const now = new Date();
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const diffMs = now.getTime() - dateObj.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours}小时前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatShortDateTime(date);
  }
};

/**
 * 检查日期是否在指定范围内
 * @param {Date|string|number} date - 要检查的日期
 * @param {Date|string|number} startDate - 开始日期
 * @param {Date|string|number} endDate - 结束日期
 * @returns {boolean} 是否在范围内
 */
export const isDateInRange = (date, startDate, endDate) => {
  if (!date || !startDate || !endDate) return false;
  
  const dateObj = new Date(date);
  const startObj = new Date(startDate);
  const endObj = new Date(endDate);
  
  // 检查日期是否有效
  if (isNaN(dateObj.getTime()) || isNaN(startObj.getTime()) || isNaN(endObj.getTime())) {
    return false;
  }
  
  return dateObj >= startObj && dateObj <= endObj;
};

/**
 * 获取N天前的日期
 * @param {number} days - 天数
 * @returns {Date} N天前的日期对象
 */
export const getDaysAgo = (days) => {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date;
};

/**
 * 获取今天的开始时间 (00:00:00)
 * @returns {Date} 今天开始时间
 */
export const getStartOfToday = () => {
  const date = new Date();
  date.setHours(0, 0, 0, 0);
  return date;
};

/**
 * 获取今天的结束时间 (23:59:59)
 * @returns {Date} 今天结束时间
 */
export const getEndOfToday = () => {
  const date = new Date();
  date.setHours(23, 59, 59, 999);
  return date;
};

/**
 * 解析时间戳（支持秒和毫秒）
 * @param {number} timestamp - 时间戳
 * @returns {Date} 日期对象
 */
export const parseTimestamp = (timestamp) => {
  if (!timestamp) return null;
  
  // 如果时间戳长度为10位，认为是秒级时间戳，需要转换为毫秒
  if (timestamp.toString().length === 10) {
    return new Date(timestamp * 1000);
  }
  
  // 否则认为是毫秒级时间戳
  return new Date(timestamp);
};
