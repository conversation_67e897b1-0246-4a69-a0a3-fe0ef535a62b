/**
 * 文件处理工具模块
 * 提供通用的文件读取和下载功能
 */

import { FileExcelOutlined, FileImageOutlined, FilePdfOutlined, FileWordOutlined, FileTextOutlined, FileUnknownOutlined } from '@ant-design/icons';
import React from 'react';

/**
 * 将文件读取为ArrayBuffer
 * @param {File} file - 文件对象
 * @returns {Promise<ArrayBuffer>} 文件内容的ArrayBuffer
 */
export const readFileAsArrayBuffer = async file => {
  if (!file) {
    throw new Error('未提供文件');
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => resolve(e.target.result);
    reader.onerror = () => reject(new Error(`读取文件失败: ${file.name}`));
    reader.readAsArrayBuffer(file);
  });
};

/**
 * 将文件读取为文本
 * @param {File} file - 文件对象
 * @returns {Promise<string>} 文件内容的文本
 */
export const readFileAsText = async file => {
  if (!file) {
    throw new Error('未提供文件');
  }

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => resolve(e.target.result);
    reader.onerror = () => reject(new Error(`读取文件失败: ${file.name}`));
    reader.readAsText(file);
  });
};

/**
 * 下载Blob对象为文件
 * @param {Blob} blob - 文件内容Blob对象
 * @param {string} fileName - 下载的文件名
 */
export const downloadBlobAsFile = (blob, fileName) => {
  if (!blob) {
    throw new Error('没有可下载的内容');
  }

  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * 根据文件扩展名获取对应的图标组件
 * @param {string} fileName 文件名
 * @param {object} style 图标样式（可选）
 * @returns {React.ReactNode} 对应的图标组件
 */
export const getFileIcon = (fileName, style = {}) => {
  if (!fileName) return <FileUnknownOutlined style={style} className="file-icon" />;

  const extension = fileName.toLowerCase().split('.').pop();

  switch (extension) {
    case 'xlsx':
    case 'xls':
    case 'csv':
      return <FileExcelOutlined style={style} className="file-icon excel" />;

    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'webp':
      return <FileImageOutlined style={style} className="file-icon image" />;

    case 'pdf':
      return <FilePdfOutlined style={style} className="file-icon pdf" />;

    case 'doc':
    case 'docx':
      return <FileWordOutlined style={style} className="file-icon word" />;

    case 'txt':
    case 'md':
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
    case 'json':
    case 'html':
    case 'css':
      return <FileTextOutlined style={style} className="file-icon text" />;

    default:
      return <FileUnknownOutlined style={style} className="file-icon unknown" />;
  }
};

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = size => {
  if (!size && size !== 0) return '';

  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB';
  }
};
