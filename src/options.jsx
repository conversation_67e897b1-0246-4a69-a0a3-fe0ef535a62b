import React from 'react';
import ReactDOM from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import OptionsPage from './pages/Options';
import './index.css';

// 引入Antd CSS
import 'antd/dist/reset.css';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ConfigProvider locale={zhCN}>
      <OptionsPage />
    </ConfigProvider>
  </React.StrictMode>,
); 