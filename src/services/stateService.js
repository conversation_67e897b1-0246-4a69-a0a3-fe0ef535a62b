import { OPERATION_TYPES } from '../constants';

/**
 * 处理状态管理服务
 * 负责管理扩展的处理状态
 */
class StateService {
  constructor() {
    this.resetState();
  }

  /**
   * 重置处理状态
   */
  resetState() {
    this.state = {
      siteConfigs: [],
      currentSiteIndex: -1,
      files: [],
      fileContents: {}, // 文件ID -> 文件内容映射
      results: [],
      loginToken: null, // 用于存储登录token
      loginTimeoutId: null,
      operationType: null, // 操作类型：'upload' 或 'menu' 或 'import_language'
      menuData: null, // 添加菜单数据字段
      menuSiteMap: new Map(), // 站点的菜单映射：domain -> { menuName -> { id, name } }
      importFiles: [], // 导入多语言的文件列表
      importResults: [], // 导入多语言的结果
      importProgress: 0, // 导入进度
      importStatus: '', // 导入状态
    };
  }

  /**
   * 获取完整状态
   * @returns {Object} - 当前处理状态
   */
  getState() {
    return this.state;
  }

  /**
   * 初始化文件上传状态
   * @param {Array} siteConfigs - 站点配置数组
   * @param {Array} files - 文件数组
   */
  initUploadState(siteConfigs, files) {
    this.resetState();
    this.state.siteConfigs = siteConfigs;
    this.state.currentSiteIndex = 0;
    this.state.operationType = OPERATION_TYPES.UPLOAD;

    // 处理并安全地存储文件信息
    files.forEach(file => {
      // 创建新的文件对象，确保文件结构一致
      const fileId = `file_${Date.now()}_${Math.floor(Math.random() * 10000)}`;

      // 只存储 ID 和路径
      this.state.files.push({ id: fileId, filePath: file.filePath });

      // 存储元数据和 Base64 内容
      this.state.fileContents[fileId] = {
        filePath: file.filePath,
        contentType: file.contentType,
        base64Content: file.base64Content,
      };
    });

    return {
      success: true,
      message: '已初始化文件上传状态',
    };
  }

  /**
   * 初始化菜单添加状态
   * @param {Array} siteConfigs - 站点配置数组
   * @param {Object} menuData - 菜单数据
   */
  initMenuState(siteConfigs, menuData) {
    this.resetState();
    this.state.siteConfigs = siteConfigs;
    this.state.currentSiteIndex = 0;
    this.state.operationType = OPERATION_TYPES.MENU;
    this.state.menuData = menuData;

    return {
      success: true,
      message: '已初始化菜单添加状态',
    };
  }

  /**
   * 初始化多语言导入状态
   * @param {Array} siteConfigs - 站点配置数组
   * @param {Array} files - 导入文件数组
   */
  initLanguageImportState(siteConfigs, files) {
    this.resetState();
    this.state.siteConfigs = siteConfigs;
    this.state.currentSiteIndex = 0;
    this.state.operationType = OPERATION_TYPES.IMPORT_LANGUAGE;
    this.state.importFiles = files;
    this.state.importProgress = 0;
    this.state.importStatus = '准备处理...';

    return {
      success: true,
      message: '已初始化多语言导入状态',
    };
  }

  /**
   * 保存站点处理结果
   * @param {Object} result - 站点处理结果
   */
  saveSiteResult(result) {
    this.state.results.push(result);
  }

  /**
   * 处理下一个站点
   * @returns {Object} - 下一步操作信息
   */
  processNextSite() {
    // 如果处理状态已重置，则不再继续
    if (this.state.currentSiteIndex === -1) {
      return {
        success: false,
        message: '处理状态已重置，不再处理下一站点',
        isCompleted: true,
      };
    }

    // 增加站点索引
    this.state.currentSiteIndex++;

    // 检查是否已超出站点配置范围
    if (this.state.currentSiteIndex >= this.state.siteConfigs.length) {
      return {
        success: true,
        message: '已处理所有站点，结束流程',
        isCompleted: true,
      };
    }

    return {
      success: true,
      message: '准备处理下一站点',
      isCompleted: false,
      currentSiteIndex: this.state.currentSiteIndex,
    };
  }

  /**
   * 获取当前站点
   * @returns {Object|null} - 当前站点配置
   */
  getCurrentSite() {
    if (this.state.currentSiteIndex >= 0 && this.state.currentSiteIndex < this.state.siteConfigs.length) {
      return this.state.siteConfigs[this.state.currentSiteIndex];
    }
    return null;
  }

  /**
   * 获取当前操作类型
   * @returns {string|null} - 操作类型
   */
  getOperationType() {
    return this.state.operationType;
  }

  /**
   * 设置登录token
   * @param {string} token - 用户登录token
   */
  setLoginToken(token) {
    this.state.loginToken = token;
  }

  /**
   * 获取登录token
   * @returns {string|null} - 登录token
   */
  getLoginToken() {
    return this.state.loginToken;
  }

  /**
   * 设置登录超时定时器ID
   * @param {number} timeoutId - 超时定时器ID
   */
  setLoginTimeoutId(timeoutId) {
    this.state.loginTimeoutId = timeoutId;
  }

  /**
   * 获取登录超时定时器ID
   * @returns {number|null} - 超时定时器ID
   */
  getLoginTimeoutId() {
    return this.state.loginTimeoutId;
  }

  /**
   * 清除登录超时定时器
   */
  clearLoginTimeout() {
    if (this.state.loginTimeoutId) {
      clearTimeout(this.state.loginTimeoutId);
      this.state.loginTimeoutId = null;
    }
  }

  /**
   * 更新多语言导入进度
   * @param {string} status - 当前状态描述
   * @param {number} progress - 当前进度(0-100)
   */
  updateImportProgress(status, progress) {
    this.state.importStatus = status;
    this.state.importProgress = progress;
    return {
      status,
      progress,
    };
  }

  /**
   * 获取处理结果
   * @returns {Array} - 所有处理结果
   */
  getResults() {
    return this.state.results;
  }

  /**
   * 获取站点的菜单映射
   * @param {string} domain - 站点域名
   * @returns {Map} - 菜单映射
   */
  getSiteMenuMap(domain) {
    if (!this.state.menuSiteMap.has(domain)) {
      this.state.menuSiteMap.set(domain, new Map());
    }
    return this.state.menuSiteMap.get(domain);
  }

  /**
   * 获取文件内容
   * @param {string} fileId - 文件ID
   * @returns {Object|null} - 文件内容对象
   */
  getFileContent(fileId) {
    return this.state.fileContents[fileId] || null;
  }

  /**
   * 获取所有文件
   * @returns {Array} - 文件数组
   */
  getFiles() {
    return this.state.files;
  }

  /**
   * 获取所有导入文件
   * @returns {Array} - 导入文件数组
   */
  getImportFiles() {
    return this.state.importFiles;
  }

  /**
   * 获取菜单数据
   * @returns {Object|null} - 菜单数据对象
   */
  getMenuData() {
    return this.state.menuData;
  }
}

// 创建单例实例
export const stateService = new StateService();
