import { AI_MODEL_API_URL, LANGUAGE_NAMES } from '../pages/Translation/constants';

/**
 * 从字符串中提取JSON
 * @param {string} str - 包含JSON的字符串
 * @returns {string|null} 提取的JSON字符串或null
 */
export const extractJsonFromString = str => {
  if (!str) return null;

  const jsonStart = str.indexOf('{');
  const jsonEnd = str.lastIndexOf('}');

  if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
    return str.substring(jsonStart, jsonEnd + 1);
  }

  return null;
};

/**
 * 翻译批次
 * @param {Object} params - 翻译参数
 * @param {Array} params.texts - 待翻译文本数组
 * @param {Array} params.targetLanguages - 目标语言数组
 * @param {string} params.apiKey - 豆包API密钥
 * @param {string} params.aiModel - 使用的AI模型
 * @param {number} params.temperature - 温度系数
 * @returns {Promise<Object>} 翻译结果
 */
export const translateBatch = async ({ texts, targetLanguages, apiKey, aiModel, temperature }) => {
  if (!texts || texts.length === 0) {
    return { items: [] };
  }

  // 确保targetLanguages不包含中文
  const nonChineseLanguages = targetLanguages.filter(lang => lang !== 'zh-CN');

  // 如果没有需要翻译的语言，返回空结果
  if (nonChineseLanguages.length === 0) {
    return {
      items: texts.map((text, index) => ({
        index,
        translations: { 'zh-CN': text },
      })),
    };
  }

  // 构建语言列表字符串
  const languageList = nonChineseLanguages.map(lang => `${LANGUAGE_NAMES[lang] || lang} (${lang})`).join(', ');

  // 构建提示词
  const textsFormatted = texts.map((text, index) => `项目${index + 1}: "${text}"`).join('\n');
  const expectedResponseFormat = `{
  "items": [
    {
      "index": 0,
      "translations": {
        ${nonChineseLanguages.map(lang => `"${lang}": "${lang}翻译1"`).join(',\n        ')}
      }
    },
    {
      "index": 1,
      "translations": { ... }
    },
    ...
  ]
}`;

  const prompt = `请将以下多个中文文本翻译成多种语言，并以JSON格式返回。
需要翻译的语言有: ${languageList}

返回格式要求:
${expectedResponseFormat}

请只返回JSON格式的结果，不要有任何解释或额外内容。

要翻译的文本有:
${textsFormatted}`;

  try {
    // 调用火山引擎 AI 模型的 API
    const response = await fetch(AI_MODEL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: aiModel,
        messages: [
          { role: 'system', content: '你是专业的多语言翻译专家。请提供准确的翻译，并以指定的JSON格式返回结果。' },
          { role: 'user', content: prompt },
        ],
        temperature: temperature,
      }),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const responseData = await response.json();

    if (responseData?.choices?.[0]?.message?.content) {
      const content = responseData.choices[0].message.content.trim();

      // 提取JSON内容
      const jsonStr = extractJsonFromString(content);
      if (!jsonStr) {
        throw new Error('翻译结果中没有找到有效的JSON格式');
      }

      // 解析JSON
      const result = JSON.parse(jsonStr);

      // 验证格式
      if (!result.items || !Array.isArray(result.items)) {
        throw new Error('翻译结果格式不符合预期，缺少items数组');
      }

      // 为每个翻译项目添加中文原文
      result.items.forEach((item, index) => {
        if (index < texts.length) {
          item.translations = {
            ...item.translations,
            'zh-CN': texts[index], // 直接添加中文原文
          };
        }
      });

      return result;
    }

    throw new Error('翻译API返回格式异常');
  } catch (error) {
    console.error('豆包API调用失败:', error);
    throw new Error(`豆包API调用失败: ${error.message}`);
  }
};

/**
 * 准备翻译数据
 * @param {Object|Array} jsonData - 需要翻译的JSON数据
 * @returns {Array} 格式化后的翻译项数组
 */
export const prepareTranslationItems = jsonData => {
  if (!jsonData) {
    return null;
  }

  // 格式化翻译项
  let translationItems = [];

  if (Array.isArray(jsonData)) {
    // 如果是数组，直接使用
    translationItems = jsonData;
  } else if (typeof jsonData === 'object' && jsonData !== null) {
    // 如果是对象，转换为数组
    translationItems = Object.entries(jsonData)
      .map(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          // 对象格式: key: { text: "文本", resourceType: "类型" }
          return {
            key,
            text: value.text || value.value || '',
            resourceType: value.resourceType,
          };
        }
        return null;
      })
      .filter(item => item !== null && item.text);
  }

  return translationItems;
};

/**
 * 过滤需要翻译的项目，避免重复翻译已有内容
 * @param {Array} items - 需要翻译的项目数组
 * @param {Array} existingExcelData - 已有的Excel数据
 * @param {Array} languages - 目标语言数组
 * @returns {Object} 过滤结果，包含需要翻译的项目和已有翻译结果
 */
export const filterItemsForTranslation = (items, existingExcelData, languages) => {
  if (!existingExcelData || existingExcelData.length === 0) {
    return {
      itemsNeedingTranslation: items, // 所有项目都需要翻译
      existingResults: [], // 没有已有结果
    };
  }

  const itemsNeedingTranslation = [];
  const existingResults = [];

  // 检查每个项目是否已有完整的翻译
  items.forEach(item => {
    // 检查条目是否已存在（无论是否完全翻译）
    const keyExists = existingExcelData.some(row => row.resourceType === item.resourceType && row.code === item.key);

    // 检查是否每种语言都有翻译
    const existingTranslations = {};
    let isFullyTranslated = true;

    // 找到中文源文本（如果存在）
    const existingChineseRow = existingExcelData.find(
      row => row.resourceType === item.resourceType && row.code === item.key && row.language === 'zh-CN'
    );

    // 中文文本是否发生变更
    const chineseTextChanged = existingChineseRow && existingChineseRow.value !== item.text;

    // 检查所有目标语言的翻译情况
    for (const lang of languages) {
      const existingRow = existingExcelData.find(row => row.resourceType === item.resourceType && row.code === item.key && row.language === lang);

      if (existingRow) {
        existingTranslations[lang] = existingRow.value;
      } else {
        // 如果任一语言没有翻译，则标记为未完全翻译
        isFullyTranslated = false;
      }
    }

    // 处理逻辑：
    // 1. 如果key不存在，需要翻译（新增）
    // 2. 如果key存在但未完全翻译，需要翻译（补全）
    // 3. 如果key存在且中文有变更，需要翻译（更新）
    // 4. 如果key存在且完全翻译且中文无变更，使用已有翻译
    if (!keyExists || !isFullyTranslated || chineseTextChanged) {
      // 需要翻译，但保留已有的中文翻译（如果有且未变更）
      if (existingChineseRow && !chineseTextChanged) {
        item.text = existingChineseRow.value;
      }
      itemsNeedingTranslation.push(item);
    } else {
      // 已有完整翻译且中文未变更，直接使用
      existingResults.push({
        key: item.key,
        resourceType: item.resourceType,
        translations: existingTranslations,
      });
    }
  });

  return { itemsNeedingTranslation, existingResults };
};
