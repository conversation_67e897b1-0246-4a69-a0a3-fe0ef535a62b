/* global chrome */
import { MESSAGE_TYPES, BADGE_COLORS } from '../constants';

/**
 * 消息处理服务
 * 负责处理扩展内部通信和与页面的通信
 */
export const messageService = {
  /**
   * 发送状态更新消息
   * @param {Object} params - 消息参数
   */
  sendStatusUpdate(params) {
    const { type, status, progress, completed, success, results, error } = params;

    chrome.runtime.sendMessage({
      type,
      status,
      progress,
      completed,
      success,
      results,
      error,
    });
  },


  /**
   * 发送处理完成消息
   * @param {boolean} success - 是否成功完成
   * @param {Array} results - 处理结果数组
   * @param {string} operationType - 操作类型
   * @param {string} pageType - 页面类型，用于区分不同页面的处理结果
   */
  sendProcessingCompleted(success, results, operationType, pageType) {
    chrome.runtime.sendMessage({
      type: MESSAGE_TYPES.BROWSER_AUTOMATION_COMPLETED,
      success,
      results,
      operationType,
      pageType,
    });

    // 添加徽章通知
    this.showBadge(success);
  },

  /**
   * 显示状态徽章
   * @param {boolean} success - 操作是否成功
   * @param {string} text - 徽章文本，默认根据成功状态设置
   */
  showBadge(success, text) {
    chrome.action.setBadgeText({
      text: text || (success ? '✓' : '!'),
    });

    chrome.action.setBadgeBackgroundColor({
      color: success ? BADGE_COLORS.SUCCESS : BADGE_COLORS.ERROR,
    });

    // 5秒后清除徽章
    setTimeout(() => {
      chrome.action.setBadgeText({ text: '' });
    }, 5000);
  },

  /**
   * 保存最后处理时间
   */
  saveLastProcessedTime() {
    chrome.storage.local.set(
      {
        lastProcessed: new Date().toISOString(),
      },
      () => {
        this.showBadge(true);
      }
    );
  },

  /**
   * 打开新标签页
   * @param {string} url - 要打开的页面URL
   * @returns {Promise} - 打开结果的Promise
   */
  openNewTab(url) {
    return new Promise(resolve => {
      chrome.tabs.create({ url }, tab => {
        console.log(`页面已打开: ${url}`, tab.id);
        resolve({ success: true, tab });
      });
    });
  },

  /**
   * 获取站点配置
   * @returns {Promise<Array>} - 站点配置数组
   */
  getSiteConfigs() {
    return new Promise(resolve => {
      chrome.storage.local.get('siteConfigs', result => {
        resolve(result.siteConfigs || []);
      });
    });
  },

  /**
   * 保存站点配置
   * @param {Array} siteConfigs - 站点配置数组
   * @returns {Promise<Object>} - 保存结果
   */
  saveSiteConfigs(siteConfigs) {
    return new Promise(resolve => {
      chrome.storage.local.set({ siteConfigs }, () => {
        resolve({ success: true });
      });
    });
  },

  /**
   * 清除站点配置
   * @returns {Promise<Object>} - 清除结果
   */
  clearSiteConfigs() {
    return new Promise(resolve => {
      chrome.storage.local.set({ siteConfigs: [] }, () => {
        resolve({ success: true });
      });
    });
  },
};
