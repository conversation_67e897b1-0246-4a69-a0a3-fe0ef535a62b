/* global chrome */
import { TIMEOUTS, OPERATION_TYPES, PAGE_TYPES } from '../constants';
import { authService } from './authService';
import { fileUploadService } from './fileUploadService';
import { languageService } from './languageService';
import { stateService } from './stateService';
import { messageService } from './messageService';
import { addMenusToSite } from './menuService';

/**
 * 处理服务
 * 负责协调和执行不同类型的处理流程
 */
export const processingService = {
  /**
   * 开始站点处理流程 - 通用函数，支持文件上传、菜单添加和多语言导入
   */
  async startSiteProcessing() {
    // 清除可能存在的超时定时器
    stateService.clearLoginTimeout();

    const currentSiteIndex = stateService.getState().currentSiteIndex;

    // 检查站点索引是否有效
    if (currentSiteIndex < 0) {
      console.error('无效的站点索引: ' + currentSiteIndex);
      return;
    }

    // 检查是否已处理完所有站点
    if (currentSiteIndex >= stateService.getState().siteConfigs.length) {
      // 所有站点处理完成
      this.finishAllProcessing();
      return;
    }

    const currentSite = stateService.getCurrentSite();

    const operationType = stateService.getOperationType();

    try {
      const domain = currentSite.domain;

      console.log(`开始处理站点[${operationType}]: ${domain}`);

      // 设置登录超时
      const loginTimeoutId = setTimeout(() => {
        console.log('登录超时');
        this.handleLoginTimeout();
      }, TIMEOUTS.LOGIN);

      stateService.setLoginTimeoutId(loginTimeoutId);

      // 获取验证码，优先使用配置中的，如果没有则使用默认值
      const code = currentSite.captchaCode;

      // 准备登录数据
      const loginData = {
        domain: domain,
        account: currentSite.username,
        password: currentSite.password,
        code,
        enablePasswordEncryption: currentSite.enablePasswordEncryption !== false, // 默认启用加密
      };

      console.log('登录数据准备完毕:', Object.assign({}, loginData, { password: '***' }));

      // 使用API进行登录
      const loginResult = await authService.login(loginData);

      // 清除登录超时
      stateService.clearLoginTimeout();

      if (loginResult.success) {
        console.log(loginResult.fromCache ? '使用缓存token成功' : '登录成功，已获取token');

        // 保存token供后续使用
        stateService.setLoginToken(loginResult.token);

        // 根据操作类型执行不同的处理逻辑
        await this.executeOperation(currentSite, operationType);
      } else {
        console.error('登录失败:', loginResult.error);

        // 记录登录失败结果并继续处理下一个站点
        this.handleLoginFailure(currentSite, loginResult.error);
      }
    } catch (error) {
      console.error(`处理站点 ${currentSite.domain} 失败:`, error);
      this.handleSiteProcessingError(currentSite, error);
    }
  },

  /**
   * 执行特定类型的操作
   * @param {Object} currentSite - 当前站点信息
   * @param {string} operationType - 操作类型
   */
  async executeOperation(currentSite, operationType) {
    switch (operationType) {
      case OPERATION_TYPES.UPLOAD:
        await this.executeFileUpload(currentSite);
        break;
      case OPERATION_TYPES.MENU:
        await this.executeMenuAddition(currentSite);
        break;
      case OPERATION_TYPES.IMPORT_LANGUAGE:
        await this.executeLanguageImport(currentSite);
        break;
      default:
        console.error(`未知的操作类型: ${operationType}`);
        stateService.saveSiteResult({
          domain: currentSite.domain,
          success: false,
          error: `未知的操作类型: ${operationType}`,
        });
        this.processNextSite();
    }
  },

  /**
   * 执行文件上传操作
   * @param {Object} currentSite - 当前站点信息
   */
  async executeFileUpload(currentSite) {
    try {
      // 验证token是否可用
      const token = stateService.getLoginToken();
      if (!token) {
        throw new Error('未获取到有效的登录token，无法上传文件');
      }

      console.log('开始执行文件上传...');

      // 调用上传服务
      const uploadResult = await fileUploadService.uploadFiles(
        {
          domain: currentSite.domain,
          files: stateService.getFiles(),
          token: token,
        },
        stateService.getState().fileContents
      );

      if (uploadResult.success) {
        console.log('上传成功:', uploadResult);

        // 记录上传成功结果
        stateService.saveSiteResult({
          domain: currentSite.domain,
          success: true,
          uploadResults: uploadResult.results || [],
        });
      } else {
        console.error('上传失败:', uploadResult.error || '未知错误');

        // 记录上传失败结果
        stateService.saveSiteResult({
          domain: currentSite.domain,
          success: false,
          error: uploadResult.error || '上传失败',
          results: uploadResult.results || [],
        });
      }

      // 处理下一个站点
      this.processNextSite();
    } catch (error) {
      console.error(`上传过程中出错:`, error);

      // 记录错误并继续下一个站点
      stateService.saveSiteResult({
        domain: currentSite.domain,
        success: false,
        error: `上传操作失败: ${error.message}`,
      });

      this.processNextSite();
    }
  },

  /**
   * 执行菜单添加操作
   * @param {Object} currentSite - 当前站点信息
   */
  async executeMenuAddition(currentSite) {
    try {
      // 获取菜单数据和token
      const menuData = stateService.getMenuData();
      const token = stateService.getLoginToken();

      if (!menuData) {
        const result = {
          site: currentSite.domain,
          success: false,
          error: '菜单数据未提供',
          menus: [],
        };
        stateService.saveSiteResult(result);
        this.processNextSite();
        return;
      }

      // 获取当前站点的菜单映射
      const currentSiteMenuMap = stateService.getSiteMenuMap(currentSite.domain);

      console.log(`准备添加菜单到站点: ${currentSite.domain}, 当前菜单映射大小: ${currentSiteMenuMap.size}`);

      // 添加菜单到当前站点
      const siteResult = await addMenusToSite(token, currentSite, menuData, currentSiteMenuMap);

      // 保存结果
      stateService.saveSiteResult(siteResult);

      // 处理下一个站点
      this.processNextSite();
    } catch (error) {
      console.error(`菜单添加失败:`, error);

      // 记录错误并继续下一个站点
      const result = {
        site: currentSite.domain,
        success: false,
        error: `菜单添加失败: ${error.message}`,
        menus: [],
      };
      stateService.saveSiteResult(result);

      // 处理下一个站点
      this.processNextSite();
    }
  },

  /**
   * 执行多语言导入操作
   * @param {Object} currentSite - 当前站点信息
   */
  async executeLanguageImport(currentSite) {
    try {
      const domain = currentSite.domain;

      const importFiles = stateService.getImportFiles();

      if (!importFiles || importFiles.length === 0) {
        const result = {
          domain: currentSite.domain,
          success: false,
          error: '未提供要导入的文件',
        };
        stateService.saveSiteResult(result);
        this.processNextSite();
        return;
      }

      // 调用多语言服务
      const importResult = await languageService.importLanguageFiles({
        domain,
        files: importFiles,
      });

      // 保存导入结果
      stateService.saveSiteResult({
        domain: currentSite.domain,
        success: importResult.success,
        files: importResult.files,
        error: importResult.error,
      });

      // 处理下一个站点
      this.processNextSite();
    } catch (error) {
      console.error(`多语言导入过程中出错:`, error);

      // 记录错误并继续下一个站点
      stateService.saveSiteResult({
        domain: currentSite.domain,
        success: false,
        error: `多语言导入失败: ${error.message}`,
        files: [],
      });

      this.processNextSite();
    }
  },

  /**
   * 处理下一个站点
   */
  processNextSite() {
    const nextSiteResult = stateService.processNextSite();

    if (nextSiteResult.isCompleted) {
      if (nextSiteResult.success) {
        // 处理完成
        this.finishAllProcessing();
        return;
      } else {
        // 处理状态已重置或其他错误
        console.log(nextSiteResult.message);
        return;
      }
    }

    // 稍等一会儿再开始下一个站点，避免过快处理
    setTimeout(() => {
      // 再次检查状态是否已被重置
      if (stateService.getState().currentSiteIndex === -1) {
        console.log('延迟处理期间状态已重置，不再继续处理');
        return;
      }
      this.startSiteProcessing();
    }, 1000);
  },

  /**
   * 处理登录超时
   */
  handleLoginTimeout() {
    const currentSite = stateService.getCurrentSite();

    if (!currentSite) {
      console.error('无效的站点，无法处理登录超时');
      return;
    }

    console.log('登录超时，处理失败, 当前站点[%s]', currentSite.domain);

    // 记录登录失败结果
    stateService.saveSiteResult({
      domain: currentSite.domain,
      success: false,
      error: `登录超时，请检查网络连接或服务器状态`,
    });

    // 检查是否是最后一个站点
    const isLastSite = stateService.getState().currentSiteIndex === stateService.getState().siteConfigs.length - 1;

    if (isLastSite) {
      // 如果是最后一个站点，直接结束处理
      this.finishAllProcessing();
      return;
    }

    // 继续处理下一个站点
    this.processNextSite();
  },

  /**
   * 处理登录失败
   * @param {Object} currentSite - 当前站点信息
   * @param {string} errorMessage - 错误消息
   */
  handleLoginFailure(currentSite, errorMessage) {
    // 记录登录失败结果
    stateService.saveSiteResult({
      domain: currentSite.domain,
      success: false,
      error: `登录失败: ${errorMessage}`,
    });

    // 继续处理下一个站点
    this.processNextSite();
  },

  /**
   * 处理站点处理错误
   * @param {Object} currentSite - 当前站点信息
   * @param {Error} error - 错误对象
   */
  handleSiteProcessingError(currentSite, error) {
    // 如果状态已重置，则不再继续
    if (stateService.getState().currentSiteIndex === -1) {
      console.log('处理状态已重置，不再处理错误');
      return;
    }

    // 记录错误结果
    stateService.saveSiteResult({
      domain: currentSite.domain,
      success: false,
      error: `站点处理失败: ${error.message}`,
    });

    // 判断是否处理完所有站点
    if (stateService.getState().currentSiteIndex === stateService.getState().siteConfigs.length - 1) {
      // 如果是最后一个站点，直接结束处理
      this.finishAllProcessing();
      return;
    }

    // 继续处理下一个站点
    this.processNextSite();
  },

  /**
   * 完成所有处理
   */
  finishAllProcessing() {
    const results = stateService.getResults();
    const operationType = stateService.getOperationType();
    const allSuccess = results.every(r => r.success);

    // 获取当前操作的页面类型
    let pageType = '';

    // 根据操作类型推断页面类型
    if (operationType === OPERATION_TYPES.MENU) {
      pageType = PAGE_TYPES.BATCH_ADD_MENU;
    } else if (operationType === OPERATION_TYPES.IMPORT_LANGUAGE) {
      pageType = PAGE_TYPES.TRANSLATION;
    } else {
      pageType = PAGE_TYPES.OPTIONS;
    }

    if (operationType === OPERATION_TYPES.MENU) {
      // 对于菜单添加操作，计算最终成功状态
      const hasSuccessfulSite = results.some(result => result.success);

      // 检查是否有严重错误（排除"已存在"的情况，因为这不算失败）
      const hasCriticalErrors = results.some(
        site => site.menus && site.menus.some(menu => !menu.success && !menu.message?.includes('已存在') && menu.message?.includes('父菜单'))
      );

      // 计算整体成功状态：有成功的站点且没有严重错误
      const overallSuccess = hasSuccessfulSite && !hasCriticalErrors;

      console.log('菜单处理完成状态计算:', {
        hasSuccessfulSite,
        hasCriticalErrors,
        overallSuccess,
        resultsCount: results.length,
      });

      // 统一使用消息服务发送处理结果
      messageService.sendProcessingCompleted(overallSuccess, results, operationType, pageType);
    } else if (operationType === OPERATION_TYPES.IMPORT_LANGUAGE) {
      // 多语言导入操作完成
      messageService.sendProcessingCompleted(allSuccess, results, operationType, pageType);
    } else {
      // 对于文件上传操作
      messageService.sendProcessingCompleted(allSuccess, results, operationType, pageType);
    }

    // 重置处理状态
    stateService.resetState();
  },
};
