/**
 * 云端配置同步服务
 * 负责与飞书多维表格的数据交互
 */

import { FEISHU_API_BASE_URL, FEISHU_API_ENDPOINTS, CONFIG_SNAPSHOT_FIELDS, API_LIMITS } from '../constants/feishuConfig.js';
import { getFeishuCredentials } from '../config/feishuCredentials.js';

class CloudSyncService {
  constructor() {
    this.tokenCache = null;
    this.tokenExpireTime = null;
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.lastRequestTime = 0;
    this.credentials = null;

    // 初始化凭证
    this.initializeCredentials();
  }

  /**
   * 初始化凭证信息
   */
  initializeCredentials() {
    try {
      this.credentials = getFeishuCredentials();

      if (!this.credentials.validation.isValid) {
        console.error('飞书凭证配置不完整:', this.credentials.validation.message);
        throw new Error(`飞书凭证配置错误: ${this.credentials.validation.message}`);
      }

      console.log('飞书凭证初始化成功');
    } catch (error) {
      console.error('初始化飞书凭证失败:', error);
      throw error;
    }
  }

  /**
   * 获取飞书应用凭证
   * @returns {Promise<Object>} 应用凭证信息
   */
  async getAppCredentials() {
    if (!this.credentials) {
      this.initializeCredentials();
    }

    return {
      appId: this.credentials.APP_ID,
      appSecret: this.credentials.APP_SECRET,
      appToken: this.credentials.APP_TOKEN,
      tableId: this.credentials.TABLE_ID,
    };
  }

  /**
   * 设置飞书应用凭证
   * @param {Object} credentials - 凭证信息
   */
  async setAppCredentials(credentials) {
    // 更新内存中的凭证
    this.credentials = {
      ...this.credentials,
      ...credentials,
    };

    // 清除缓存的token，强制重新获取
    this.tokenCache = null;
    this.tokenExpireTime = null;

    console.log('飞书凭证已更新');
  }

  /**
   * 获取tenant_access_token
   * @returns {Promise<string>} access token
   */
  async getTenantAccessToken() {
    // 检查缓存的token是否有效
    if (this.tokenCache && this.tokenExpireTime && Date.now() < this.tokenExpireTime) {
      return this.tokenCache;
    }

    const credentials = await this.getAppCredentials();
    if (!credentials.appId || !credentials.appSecret) {
      throw new Error('飞书应用凭证未配置，请先在配置文件中配置APP_ID和APP_SECRET');
    }

    try {
      const response = await fetch(`${FEISHU_API_BASE_URL}${FEISHU_API_ENDPOINTS.TENANT_ACCESS_TOKEN}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          app_id: credentials.appId,
          app_secret: credentials.appSecret,
        }),
      });

      const data = await response.json();

      if (data.code !== 0) {
        throw new Error(`获取token失败: ${data.msg || '未知错误'}`);
      }

      // 缓存token，提前5分钟过期
      this.tokenCache = data.tenant_access_token;
      this.tokenExpireTime = Date.now() + (data.expire - 300) * 1000;

      return this.tokenCache;
    } catch (error) {
      console.error('获取飞书token失败:', error);
      throw new Error(`获取飞书访问令牌失败: ${error.message}`);
    }
  }

  /**
   * 发送API请求（带限流控制）
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async makeApiRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ url, options, resolve, reject });
      this.processRequestQueue();
    });
  }

  /**
   * 处理请求队列（实现QPS限制）
   */
  async processRequestQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.requestQueue.length > 0) {
      const now = Date.now();
      const timeSinceLastRequest = now - this.lastRequestTime;
      const minInterval = 1000 / API_LIMITS.QPS_LIMIT;

      if (timeSinceLastRequest < minInterval) {
        await new Promise(resolve => setTimeout(resolve, minInterval - timeSinceLastRequest));
      }

      const { url, options, resolve, reject } = this.requestQueue.shift();
      this.lastRequestTime = Date.now();

      try {
        const token = await this.getTenantAccessToken();

        // 创建AbortController来处理超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), API_LIMITS.REQUEST_TIMEOUT);

        const response = await fetch(url, {
          ...options,
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
            ...options.headers,
          },
          body: options.body ? JSON.stringify(options.body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        const data = await response.json();

        if (data.code !== 0) {
          throw new Error(`API请求失败: ${data.msg || '未知错误'} (code: ${data.code})`);
        }

        resolve(data);
      } catch (error) {
        reject(error);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * 上传配置快照
   * @param {Object} snapshot - 配置快照数据
   * @returns {Promise<Object>} 上传结果
   */
  async uploadSnapshot(snapshot) {
    const credentials = await this.getAppCredentials();
    if (!credentials.appToken || !credentials.tableId) {
      throw new Error('飞书多维表格配置未完成，请先配置APP_TOKEN和TABLE_ID');
    }

    // 验证必要字段
    if (!snapshot.snapshot_name || !snapshot.config_json) {
      throw new Error('快照名称和配置数据不能为空');
    }

    const recordData = {
      fields: {
        [CONFIG_SNAPSHOT_FIELDS.SNAPSHOT_NAME]: snapshot.snapshot_name,
        [CONFIG_SNAPSHOT_FIELDS.CONFIG_JSON]: snapshot.config_json,
        [CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE]: snapshot.config_type,
        [CONFIG_SNAPSHOT_FIELDS.CREATED_BY]: snapshot.created_by || '系统用户',
        [CONFIG_SNAPSHOT_FIELDS.CREATED_AT]: snapshot.created_at || Date.now(),
        [CONFIG_SNAPSHOT_FIELDS.DESCRIPTION]: snapshot.description || '',
      },
    };

    const url = `${FEISHU_API_BASE_URL}${FEISHU_API_ENDPOINTS.BITABLE.CREATE_RECORD}`
      .replace('{app_token}', credentials.appToken)
      .replace('{table_id}', credentials.tableId);

    try {
      const response = await this.makeApiRequest(url, {
        method: 'POST',
        body: recordData,
      });

      return {
        success: true,
        recordId: response.data.record.record_id,
        message: '配置快照上传成功',
      };
    } catch (error) {
      console.error('上传配置快照失败:', error);
      throw new Error(`上传配置快照失败: ${error.message}`);
    }
  }

  /**
   * 查询配置快照
   * @param {Object} filters - 查询过滤条件
   * @returns {Promise<Array>} 快照列表
   */
  async querySnapshots(filters = {}) {
    const credentials = await this.getAppCredentials();
    if (!credentials.appToken || !credentials.tableId) {
      throw new Error('飞书多维表格配置未完成');
    }

    const url = `${FEISHU_API_BASE_URL}${FEISHU_API_ENDPOINTS.BITABLE.LIST_RECORDS}`
      .replace('{app_token}', credentials.appToken)
      .replace('{table_id}', credentials.tableId);

    const params = new URLSearchParams();

    const body = {};

    // 设置分页参数
    if (filters.pageSize) {
      params.append('page_size', filters.pageSize);
    }

    if (filters.pageToken) {
      params.append('page_token', filters.pageToken);
    }

    // 固定排序条件：按创建时间倒序
    const sortValue = [{ field_name: CONFIG_SNAPSHOT_FIELDS.CREATED_AT, desc: true }];
    if (filters.sort) {
      body.sort = filters.sort;
    } else {
      body.sort = sortValue;
    }

    // 直接使用传入的过滤条件
    if (filters.filter) {
      body.filter = filters.filter;
      console.log('CloudSyncService: 查询过滤条件:', JSON.stringify(filters.filter, null, 2));
    }

    try {
      const requestUrl = `${url}?${params.toString()}`;
      console.log('CloudSyncService: 发送查询请求:', {
        url: requestUrl,
        body: JSON.stringify(body, null, 2),
      });

      const response = await this.makeApiRequest(requestUrl, {
        method: 'POST',
        body,
      });

      console.log('CloudSyncService: 查询响应:', {
        success: !!response.data,
        itemCount: response.data?.items?.length || 0,
        hasMore: !!response.data?.has_more,
        pageToken: response.data?.page_token,
      });

      // 调试：查看第一条记录的时间字段格式
      if (response.data?.items?.length > 0) {
        const firstItem = response.data.items[0];
        const createdAtField = firstItem.fields?.[CONFIG_SNAPSHOT_FIELDS.CREATED_AT];
        console.log('CloudSyncService: 第一条记录的时间字段格式:', {
          raw: createdAtField,
          type: typeof createdAtField,
          isArray: Array.isArray(createdAtField),
        });
      }

      // 转换数据格式 - 处理飞书富文本格式
      const snapshots =
        response.data?.items?.map(item => {
          const fields = item.fields || {};

          // 提取富文本字段的文本内容
          const extractText = richTextField => {
            if (!richTextField) return '';

            // 如果是字符串，直接返回
            if (typeof richTextField === 'string') return richTextField;

            // 如果是数组（富文本格式）
            if (Array.isArray(richTextField)) {
              return richTextField
                .map(item => {
                  if (typeof item === 'string') return item;
                  if (item && typeof item === 'object') {
                    return item.text || item.link || '';
                  }
                  return '';
                })
                .join('');
            }

            // 如果是对象，尝试提取text属性
            if (typeof richTextField === 'object' && richTextField.text) {
              return richTextField.text;
            }

            // 其他情况转为字符串
            return richTextField.toString();
          };

          const snapshot = {
            recordId: item.record_id,
            snapshotName: extractText(fields[CONFIG_SNAPSHOT_FIELDS.SNAPSHOT_NAME]),
            configJson: extractText(fields[CONFIG_SNAPSHOT_FIELDS.CONFIG_JSON]),
            configType: extractText(fields[CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE]) || fields[CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE],
            createdBy: extractText(fields[CONFIG_SNAPSHOT_FIELDS.CREATED_BY]),
            createdAt: fields[CONFIG_SNAPSHOT_FIELDS.CREATED_AT], // 时间戳保持原样
            description: extractText(fields[CONFIG_SNAPSHOT_FIELDS.DESCRIPTION]),
          };

          console.log('snapshot', snapshot);

          return snapshot;
        }) || [];

      return {
        success: true,
        data: snapshots,
        hasMore: response.data?.has_more || false,
        pageToken: response.data?.page_token,
      };
    } catch (error) {
      console.error('查询配置快照失败:', error);
      throw new Error(`查询配置快照失败: ${error.message}`);
    }
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误信息
   */
  handleApiError(error) {
    if (error.message.includes('token')) {
      return '访问令牌无效或已过期，请检查应用配置';
    }
    if (error.message.includes('permission')) {
      return '权限不足，请检查应用权限配置';
    }
    if (error.message.includes('rate limit')) {
      return '请求过于频繁，请稍后再试';
    }
    return error.message || '未知错误';
  }

  /**
   * 重试请求
   * @param {Function} requestFn - 请求函数
   * @param {number} maxRetries - 最大重试次数
   * @returns {Promise<any>} 请求结果
   */
  async retryRequest(requestFn, maxRetries = API_LIMITS.MAX_RETRIES) {
    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;

        // 检查是否为可重试的错误
        if (i < maxRetries && this.isRetryableError(error)) {
          const delay = API_LIMITS.RETRY_DELAY * Math.pow(2, i); // 指数退避
          console.log(`请求失败，${delay}ms后进行第${i + 1}次重试:`, error.message);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else if (i < maxRetries) {
          // 非可重试错误，直接抛出
          throw error;
        }
      }
    }

    throw lastError;
  }

  /**
   * 判断错误是否可重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否可重试
   */
  isRetryableError(error) {
    const retryableErrors = ['network', 'timeout', 'rate limit', 'server error', '429', '500', '502', '503', '504'];

    const errorMessage = error.message.toLowerCase();
    return retryableErrors.some(keyword => errorMessage.includes(keyword));
  }

  /**
   * 验证配置完整性
   * @param {Object} config - 配置对象
   * @returns {Object} 验证结果
   */
  validateConfigIntegrity(config) {
    const requiredFields = CONFIG_SNAPSHOT_FIELDS;
    const missingFields = [];

    for (const field of requiredFields) {
      if (!config.hasOwnProperty(field) || config[field] === null || config[field] === undefined) {
        missingFields.push(field);
      }
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      message: missingFields.length > 0 ? `配置缺少必要字段: ${missingFields.join(', ')}` : '配置完整性验证通过',
    };
  }

  /**
   * 验证云端配置
   * @returns {Promise<Object>} 验证结果
   */
  async validateCloudConfig() {
    try {
      const credentials = await this.getAppCredentials();

      // 验证凭证完整性
      const requiredCredentials = ['appId', 'appSecret', 'appToken', 'tableId'];
      const missingCredentials = requiredCredentials.filter(key => !credentials[key]);

      if (missingCredentials.length > 0) {
        return {
          isValid: false,
          message: `缺少必要的凭证信息: ${missingCredentials.join(', ')}`,
        };
      }

      // 测试API连接
      try {
        await this.getTenantAccessToken();
        return {
          isValid: true,
          message: '云端配置验证通过',
        };
      } catch (error) {
        return {
          isValid: false,
          message: `API连接测试失败: ${error.message}`,
        };
      }
    } catch (error) {
      return {
        isValid: false,
        message: `配置验证失败: ${error.message}`,
      };
    }
  }

  /**
   * 验证配置完整性
   * @returns {Promise<Object>} 验证结果
   */
  async validateConfiguration() {
    try {
      const credentials = await this.getAppCredentials();

      const issues = [];

      if (!credentials.appId) issues.push('缺少APP_ID配置');
      if (!credentials.appSecret) issues.push('缺少APP_SECRET配置');
      if (!credentials.appToken) issues.push('缺少APP_TOKEN配置');
      if (!credentials.tableId) issues.push('缺少TABLE_ID配置');

      if (issues.length > 0) {
        return {
          valid: false,
          issues,
        };
      }

      // 尝试获取token验证凭证有效性
      await this.getTenantAccessToken();

      return {
        valid: true,
        message: '配置验证成功',
      };
    } catch (error) {
      return {
        valid: false,
        issues: [this.handleApiError(error)],
      };
    }
  }
}

// 创建单例实例
export const cloudSyncService = new CloudSyncService();
export default cloudSyncService;
