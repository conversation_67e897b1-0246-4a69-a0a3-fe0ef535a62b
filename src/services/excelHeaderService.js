import * as XLSX from 'xlsx';
import { translateBatch } from './translationService';
import JSZ<PERSON> from 'jszip';
import { readExcelFile, workbookToBlob } from '../utils/excelUtils';
import { downloadBlobAsFile } from '../utils/fileUtils';

/**
 * 读取Excel文件并提取表头
 * @param {File} file - Excel文件对象
 * @returns {Promise<Object>} 包含表头和工作表数据的对象
 */
export const readExcelHeaders = async file => {
  if (!file) {
    throw new Error('未提供Excel文件');
  }

  try {
    // 使用通用Excel读取工具，指定提取表头模式
    return await readExcelFile(file, { extractHeaders: true });
  } catch (error) {
    console.error('读取Excel表头失败:', error);
    throw new Error(`读取Excel表头失败: ${error.message}`);
  }
};

/**
 * 准备表头翻译项
 * @param {Object} excelData - 从readExcelHeaders获取的Excel数据
 * @returns {Array} 准备翻译的表头项数组
 */
export const prepareHeadersForTranslation = excelData => {
  if (!excelData || !excelData.sheets) {
    console.log('[表头准备] 未提供有效的Excel数据或无工作表');
    return [];
  }

  const headerItems = [];

  console.log(`[表头准备] Excel文件名: ${excelData.fileName}`);
  console.log(`[表头准备] 工作表数量: ${Object.keys(excelData.sheets).length}`);

  // 遍历所有工作表收集表头
  Object.entries(excelData.sheets).forEach(([sheetName, sheetInfo]) => {
    console.log(`[表头准备] 处理工作表: ${sheetName}, 表头行长度: ${sheetInfo.headers?.length || 0}`);

    if (sheetInfo.headers && sheetInfo.headers.length > 0) {
      // 提取表头并添加到翻译列表
      const validHeaders = [];

      sheetInfo.headers.forEach((header, index) => {
        if (header && typeof header === 'string' && header.trim() !== '') {
          headerItems.push({
            key: `${sheetName}_header_${index}`,
            text: header,
            sheetName,
            columnIndex: index,
            resourceType: 'EXCEL_HEADER',
          });
          validHeaders.push(header);
        } else {
          console.log(`[表头准备] 工作表 ${sheetName} 中跳过无效表头，列索引: ${index}, 值: ${header}`);
        }
      });

      console.log(`[表头准备] 工作表 ${sheetName} 找到 ${validHeaders.length} 个有效表头: ${validHeaders.join(', ')}`);
    } else {
      console.log(`[表头准备] 工作表 ${sheetName} 没有有效表头`);
    }
  });

  console.log(`[表头准备] 总共准备了 ${headerItems.length} 个表头项目用于翻译`);
  return headerItems;
};

/**
 * 翻译Excel表头
 * @param {Array} headerItems - 表头项数组
 * @param {Array} languages - 目标语言数组
 * @param {Object} options - 翻译选项（apiKey, aiModel, temperature）
 * @param {Function} [progressCallback] - 可选的进度回调函数，接收参数：当前进度、总数、当前批次
 * @returns {Promise<Array>} 翻译后的表头项数组
 */
export const translateHeaders = async (headerItems, languages, { apiKey, aiModel, temperature }, progressCallback) => {
  if (!headerItems || headerItems.length === 0) {
    return [];
  }

  try {
    // 分批处理翻译
    const batchSize = 10; // 每批最多10个表头
    const translationResults = [];
    const totalBatches = Math.ceil(headerItems.length / batchSize);

    // 添加调试日志
    console.log(`[表头翻译] 开始处理 ${headerItems.length} 个表头项目，分为 ${totalBatches} 个批次，每批最多 ${batchSize} 个项目`);
    console.log(`[表头翻译] 目标语言: ${languages.join(', ')}`);
    console.log(`[表头翻译] 表头项目详情:`, headerItems);

    // 优化批次处理 - 避免创建不必要的小批次
    // 如果总数小于batchSize，直接作为一个批次处理
    if (headerItems.length <= batchSize) {
      console.log(`[表头翻译] 表头项目数(${headerItems.length})小于等于批次大小(${batchSize})，将作为单个批次处理`);

      // 通过回调函数报告进度
      if (typeof progressCallback === 'function') {
        progressCallback(0, headerItems.length, {
          currentBatch: 1,
          totalBatches: 1,
          batchSize: headerItems.length,
        });
      }

      // 调用翻译API - 所有项目一次性处理
      const batchResult = await translateBatch({
        texts: headerItems.map(item => item.text),
        targetLanguages: languages.filter(lang => lang !== 'zh-CN'), // 排除中文
        apiKey,
        aiModel,
        temperature,
      });

      console.log(`[表头翻译] 单批次翻译完成，API返回结果:`, batchResult);

      // 处理翻译结果
      if (batchResult.items && Array.isArray(batchResult.items)) {
        headerItems.forEach((item, index) => {
          const translated = batchResult.items.find(r => r.index === index) || batchResult.items[index] || { translations: {} };

          translationResults.push({
            ...item,
            translations: translated.translations || { 'zh-CN': item.text },
          });
        });
      }

      // 通知完成
      if (typeof progressCallback === 'function') {
        progressCallback(headerItems.length, headerItems.length, {
          currentBatch: 1,
          totalBatches: 1,
          batchSize: headerItems.length,
          completed: true,
        });
      }
    } else {
      // 多批次处理
      for (let i = 0; i < headerItems.length; i += batchSize) {
        const batch = headerItems.slice(i, i + batchSize);
        const currentBatch = Math.floor(i / batchSize) + 1;

        console.log(`[表头翻译] 处理批次 ${currentBatch}/${totalBatches}，包含 ${batch.length} 个项目`);

        // 通过回调函数报告进度
        if (typeof progressCallback === 'function') {
          progressCallback(i, headerItems.length, {
            currentBatch,
            totalBatches,
            batchSize: batch.length,
          });
        }

        // 调用翻译API
        const batchResult = await translateBatch({
          texts: batch.map(item => item.text),
          targetLanguages: languages.filter(lang => lang !== 'zh-CN'), // 排除中文
          apiKey,
          aiModel,
          temperature,
        });

        console.log(`[表头翻译] 批次 ${currentBatch} 翻译完成，API返回结果:`, batchResult);

        // 处理翻译结果
        if (batchResult.items && Array.isArray(batchResult.items)) {
          batch.forEach((item, index) => {
            const translated = batchResult.items.find(r => r.index === index) || batchResult.items[index] || { translations: {} };

            translationResults.push({
              ...item,
              translations: translated.translations || { 'zh-CN': item.text },
            });
          });
        }

        // 通知批次完成
        if (typeof progressCallback === 'function') {
          progressCallback(Math.min(i + batch.length, headerItems.length), headerItems.length, {
            currentBatch,
            totalBatches,
            batchSize: batch.length,
            completed: true,
          });
        }

        // 批次间延迟，避免API限制
        if (i + batchSize < headerItems.length) {
          console.log(`[表头翻译] 批次 ${currentBatch} 延迟1秒后继续下一批次`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    console.log(`[表头翻译] 全部批次处理完成，总共翻译了 ${translationResults.length} 个表头项目`);
    return translationResults;
  } catch (error) {
    console.error('翻译表头失败:', error);
    throw new Error(`翻译表头失败: ${error.message}`);
  }
};

/**
 * 生成多语言表头Excel（每种语言一个文件）
 * @param {Object} originalExcelData - 原始Excel数据
 * @param {Array} translatedHeaders - 翻译后的表头数组
 * @param {Array} languages - 语言列表
 * @returns {Object} 包含Excel文件列表和统计信息
 */
export const generateMultilingualHeadersExcel = (originalExcelData, translatedHeaders, languages) => {
  if (!originalExcelData || !translatedHeaders || !languages || languages.length === 0) {
    throw new Error('缺少生成多语言表头所需的数据');
  }

  // 源文件名（去除扩展名）
  const originalFileName = originalExcelData.fileName.replace(/\.(xlsx|xls)$/i, '');

  // 统计信息
  const stats = {
    totalSheets: 0,
    totalHeaders: translatedHeaders.length,
    files: [], // 新增：生成的文件列表
    processedSheets: [],
  };

  // 按工作表分组翻译结果
  const translationsBySheet = {};
  translatedHeaders.forEach(header => {
    if (!translationsBySheet[header.sheetName]) {
      translationsBySheet[header.sheetName] = [];
    }
    translationsBySheet[header.sheetName].push(header);
  });

  // 为每种语言创建单独的Excel文件
  const excelFiles = [];

  // 处理每种语言
  languages.forEach(language => {
    // 创建新工作簿
    const workbook = XLSX.utils.book_new();

    // 语言文件名,把-替换为_为了和业务代码里保持一致
    const languageFileName = `${originalFileName}_${language.replace('-', '_')}.xlsx`;

    // 工作表计数
    let sheetCount = 0;

    // 处理每个工作表
    Object.entries(originalExcelData.sheets).forEach(([sheetName, sheetInfo]) => {
      stats.totalSheets++;

      // 获取当前工作表的翻译结果
      const sheetTranslations = translationsBySheet[sheetName] || [];

      // 创建新的表头行
      const localizedHeaders = [...sheetInfo.headers]; // 复制原表头

      // 替换为翻译后的表头
      sheetTranslations.forEach(header => {
        if (header.translations && header.translations[language]) {
          localizedHeaders[header.columnIndex] = header.translations[language];
        }
      });

      // 创建工作表
      const ws = XLSX.utils.aoa_to_sheet([localizedHeaders, ...sheetInfo.data]);
      XLSX.utils.book_append_sheet(workbook, ws, sheetName);
      sheetCount++;

      // 更新统计信息（首次遍历时）
      if (language === languages[0]) {
        stats.processedSheets.push({
          name: sheetName,
          headers: sheetTranslations.length,
          languages: languages,
        });
      }
    });

    // 生成Excel二进制数据
    const blob = workbookToBlob(workbook);

    // 将文件添加到列表
    excelFiles.push({
      fileName: languageFileName,
      language: language,
      blob: blob,
      sheetCount: sheetCount,
    });

    // 更新统计信息
    stats.files.push({
      fileName: languageFileName,
      language: language,
      sheetCount: sheetCount,
    });
  });

  return {
    files: excelFiles,
    stats: stats,
  };
};

/**
 * 将多个Excel文件打包为一个ZIP文件
 * @param {Array} excelFiles - Excel文件对象数组
 * @param {string} [zipFileName] - ZIP文件名(可选，未使用)
 * @returns {Promise<Blob>} ZIP文件Blob对象
 */
export const createHeaderExcelZip = async excelFiles => {
  try {
    const zip = new JSZip();

    // 添加每个Excel文件到ZIP
    excelFiles.forEach(file => {
      zip.file(file.fileName, file.blob);
    });

    // 生成ZIP文件
    const zipBlob = await zip.generateAsync({ type: 'blob' });
    return zipBlob;
  } catch (error) {
    console.error('创建ZIP文件失败:', error);
    throw new Error(`创建ZIP文件失败: ${error.message}`);
  }
};

/**
 * 下载Excel文件或ZIP包
 * @param {Blob} blob - 文件Blob对象
 * @param {string} fileName - 文件名
 */
export const downloadExcelFile = (blob, fileName) => {
  if (!blob) {
    throw new Error('没有可下载的文件');
  }

  downloadBlobAsFile(blob, fileName);
};
