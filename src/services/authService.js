import { loginApi } from '../apis';
import JSEncrypt from 'jsencrypt';

/**
 * Token缓存管理服务
 * 负责存储和管理各站点的认证token
 */
class TokenCacheService {
  constructor() {
    this.tokens = {};
  }

  /**
   * 获取指定域名的缓存token
   * @param {string} domain - 域名
   * @returns {string|null} - 返回有效的token或null
   */
  getToken(domain) {
    const formattedDomain = this.formatDomain(domain);
    const tokenData = this.tokens[formattedDomain];

    // 检查token是否存在且未过期
    if (tokenData && tokenData.expiresAt > Date.now()) {
      console.log(`使用缓存的token: ${formattedDomain}`);
      return tokenData.token;
    }

    // 如果token不存在或已过期，删除它
    if (tokenData) {
      console.log(`缓存的token已过期: ${formattedDomain}`);
      delete this.tokens[formattedDomain];
    }

    return null;
  }

  /**
   * 保存token到缓存
   * @param {string} domain - 域名
   * @param {string} token - 认证token
   * @param {number} expiresInMinutes - token过期时间(分钟)
   */
  saveToken(domain, token, expiresInMinutes = 60) {
    const formattedDomain = this.formatDomain(domain);
    const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;

    this.tokens[formattedDomain] = {
      token,
      expiresAt,
    };

    console.log(`token已缓存: ${formattedDomain}, 过期时间: ${new Date(expiresAt).toLocaleString()}`);
  }

  /**
   * 格式化域名，确保包含协议前缀
   * @param {string} domain - 域名
   * @returns {string} - 格式化后的域名
   */
  formatDomain(domain) {
    let formattedDomain = domain;
    if (!formattedDomain.startsWith('http://') && !formattedDomain.startsWith('https://')) {
      formattedDomain = `https://${formattedDomain}`;
    }
    return formattedDomain;
  }
}

// 创建单例实例
export const tokenCache = new TokenCacheService();

/**
 * 认证服务
 * 负责处理用户登录和认证相关操作
 */
export const authService = {
  // RSA公钥
  publicKey:
    'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArzdmP59VZhP0nAD3dZXCG90o9uDOabs0FG4Iyqe/nqBEffE4ycDZV5Sj6L6QsWDIKHvsxVNkTSSmeg2h5vnNrraxb/DmM78XptqhMKzEUujHHPnTwFqyU6dVxlKUZt7tQrbf9/vddtBYl7EMgIlUWJxmdHJG1J/IGT1+WtSwcjI2261uxJ0A12lpW5MMLghlXKjtINjf2YfLYQX+BnLBIUAjxyHnAERbXfTr9FSUJuDi86FOyXRMRE0abOqAadiXauAxmZp9GevXP+l/etum07TkSczsNFyO7ORqZ/bf4unV63OP2Fp+d6ydiJ0u9whjke1z8oH7t34SLK+xd+3YoQIDAQAB',

  // JSEncrypt实例缓存
  _encryptInstance: null,

  /**
   * 获取或创建JSEncrypt实例（懒加载）
   * @returns {JSEncrypt} - JSEncrypt实例
   */
  _getEncryptInstance() {
    if (!this._encryptInstance) {
      this._encryptInstance = new JSEncrypt();
      this._encryptInstance.setPublicKey(this.publicKey);
      console.log('JSEncrypt实例已初始化');
    }
    return this._encryptInstance;
  },

  /**
   * 使用RSA公钥加密密码
   * @param {string} password - 明文密码
   * @returns {string} - 加密后的密码
   */
  encryptPassword(password) {
    try {
      const encrypt = this._getEncryptInstance();
      const encrypted = encrypt.encrypt(password);

      if (!encrypted) {
        throw new Error('密码加密失败');
      }

      console.log('密码加密成功');
      return encrypted;
    } catch (error) {
      console.error('密码加密失败:', error);
      throw new Error('密码加密失败: ' + error.message);
    }
  },

  /**
   * 使用API方式登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.domain - 域名
   * @param {string} loginData.account - 账号
   * @param {string} loginData.password - 密码
   * @param {string} loginData.code - 验证码
   * @param {boolean} loginData.enablePasswordEncryption - 是否启用密码加密，默认为true
   * @returns {Promise<Object>} - 登录结果，包含token
   */
  async login(loginData) {
    const { domain, account, password, code, enablePasswordEncryption = true } = loginData;

    try {
      // 根据配置决定是否加密密码
      let finalPassword = password;
      if (enablePasswordEncryption) {
        console.log('启用密码加密，正在加密密码...');
        finalPassword = this.encryptPassword(password);
      } else {
        console.log('密码加密已禁用，使用明文密码');
      }

      const token = await loginApi({
        domain,
        account,
        password: finalPassword,
        code,
      });

      return {
        success: true,
        token: token,
      };
    } catch (error) {
      console.error('API登录失败:', error);
      return {
        success: false,
        error: error.message || '登录请求失败',
      };
    }
  },
};
