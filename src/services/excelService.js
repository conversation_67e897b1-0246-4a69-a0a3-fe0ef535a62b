import { readExcelFile, generateExcelBlob } from '../utils/excelUtils';
import { downloadBlobAsFile } from '../utils/fileUtils';

/**
 * 读取已有翻译文件
 * @param {File} file - Excel文件对象
 * @returns {Promise<Array>} Excel数据数组
 */
export const readExistingTranslation = async file => {
  if (!file) {
    return [];
  }

  try {
    const result = await readExcelFile(file);
    return result.data;
  } catch (error) {
    console.error('读取已有翻译文件失败:', error);
    throw new Error(`读取已有翻译文件失败: ${error.message}`);
  }
};

/**
 * 根据翻译结果生成Excel数据
 * @param {Array} results - 翻译结果数组
 * @param {Array} languages - 语言列表
 * @param {Array} existingExcelData - 已有的Excel数据
 * @returns {Object} 生成的Excel数据和统计信息
 */
export const generateExcelData = (results, languages, existingExcelData = []) => {
  const excelData = [];

  // 统计变量
  let preservedCount = 0;
  let updatedCount = 0;
  let newlyAddedCount = 0;

  // 处理翻译结果，合并或覆盖已有数据
  results.forEach(item => {
    for (const lang of languages) {
      // 检查是否存在该项的翻译
      const existingIndex = existingExcelData.findIndex(
        row => row.resourceType === item.resourceType && row.code === item.key && row.language === lang
      );

      // 检查是否是中文源文本
      const isChineseSource = lang === 'zh-CN';

      // 检查中文文本是否发生变更
      let chineseTextChanged = false;
      if (!isChineseSource) {
        const chineseRow = existingExcelData.find(row => row.resourceType === item.resourceType && row.code === item.key && row.language === 'zh-CN');
        const currentChineseText = item.translations['zh-CN'];
        chineseTextChanged = chineseRow && chineseRow.value !== currentChineseText;
      }

      if (existingIndex >= 0) {
        // 如果条目已存在
        if (!chineseTextChanged) {
          // 如果中文未变更，保留已有翻译
          excelData.push(existingExcelData[existingIndex]);
          preservedCount++;
        } else {
          // 中文已变更，添加新翻译
          excelData.push({
            resourceType: item.resourceType,
            code: item.key,
            value: item.translations[lang] || `[${lang}] 翻译失败`,
            language: lang,
          });
          updatedCount++;
        }
      } else {
        // 如果条目不存在，添加新翻译
        excelData.push({
          resourceType: item.resourceType,
          code: item.key,
          value: item.translations[lang] || `[${lang}] 翻译失败`,
          language: lang,
        });
        newlyAddedCount++;
      }
    }
  });

  // 统计处理结果
  const statsInfo = {
    processedItems: results.length,
    excelRows: excelData.length,
    existingItems: existingExcelData.length,
    preserved: preservedCount,
    updated: updatedCount,
    newlyAdded: newlyAddedCount,
    results: results.slice(0, 3), // 只保存前3个结果用于预览
  };

  return { excelData, statsInfo };
};

/**
 * 创建Excel文件Blob
 * @param {Array} excelData - Excel数据数组
 * @returns {Blob} Excel文件Blob对象
 */
export const createExcelBlob = excelData => {
  return generateExcelBlob(excelData, {
    isJsonData: true,
    sheetName: 'Translations',
  });
};

/**
 * 创建Excel文件下载链接
 * @param {Blob} blob - Excel文件Blob对象
 * @param {string} fileName - 文件名，默认为translations_当前日期
 */
export const downloadExcel = (blob, fileName) => {
  if (!blob) {
    throw new Error('没有可下载的Excel文件');
  }

  const defaultFileName = `translations_${new Date().toISOString().slice(0, 10)}.xlsx`;
  downloadBlobAsFile(blob, fileName || defaultFileName);
};
