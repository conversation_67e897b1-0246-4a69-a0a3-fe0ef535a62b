import { importLanguage } from '../apis';

/**
 * 多语言服务
 * 负责处理多语言导入相关操作
 */
export const languageService = {
  /**
   * 导入多语言文件
   * @param {Object} params - 导入参数
   * @param {string} params.domain - 站点域名
   * @param {Array} params.files - 要导入的文件列表
   * @returns {Promise<Object>} - 导入结果
   */
  async importLanguageFiles(params) {
    const { domain, files } = params;

    const importResults = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      try {
        // 调用导入API
        console.log('准备导入文件:', file);
        const fileResult = await this.importSingleFile(domain, file);
        console.log('文件导入结果:', fileResult);
        importResults.push(fileResult);
      } catch (error) {
        console.error(`导入文件 ${file.name} 失败:`, error);
        importResults.push({
          file: file.name,
          success: false,
          error: error.message || '导入失败',
          message: error.message || '导入失败',
          languageCount: 0,
        });
      }
    }

    // 计算整体结果
    const allSuccess = importResults.length > 0 && importResults.every(r => r.success);
    const hasAnySuccess = importResults.some(r => r.success);

    // 构建错误信息
    let errorMessage = null;
    if (!allSuccess) {
      const errors = importResults
        .filter(r => !r.success)
        .map(r => `${r.file}: ${r.error || r.message || '未知错误'}`)
        .join('; ');
      errorMessage = errors || '部分文件导入失败';
    }

    return {
      success: allSuccess,
      files: importResults,
      error: errorMessage,
    };
  },

  /**
   * 导入单个多语言文件
   * @param {string} domain - 格式化后的站点域名
   * @param {Object} file - 文件信息
   * @returns {Promise<Object>} - 导入结果
   */
  async importSingleFile(domain, file) {
    try {
      // 调用导入API
      const importResult = await importLanguage({ domain, fileUrl: file.path });

      // 创建基本结果对象
      const result = {
        file: file.name,
        success: false, // 默认设为失败，后续根据条件修改
        message: '',
      };

      // 检查是否有错误信息
      if (importResult.content && importResult.content.errors && importResult.content.errors.length > 0) {
        // 检查是否所有错误都是"language not exits"类型的
        const hasRealErrors = importResult.content.errors.some(err => !(err.errorMsg && err.errorMsg.includes('language not exits')));

        if (hasRealErrors) {
          // 有真正的错误，表示导入失败
          const errorMessages = importResult.content.errors
            .map(err => `${err.rowNumber ? `行 ${err.rowNumber}: ` : ''}${err.errorMsg || '未知错误'}`)
            .join('; ');

          result.message = errorMessages;
          result.error = errorMessages;
          return result;
        }
        // 只有"language not exits"类型的错误，视为成功
        result.success = true;
        result.message = '导入成功';
        return result;
      }

      result.success = true;
      result.message = '导入成功';

      return result;
    } catch (error) {
      console.error('导入文件报错', error);
      return {
        file: file.name,
        success: false,
        message: error.message || '导入失败',
      };
    }
  },
};
