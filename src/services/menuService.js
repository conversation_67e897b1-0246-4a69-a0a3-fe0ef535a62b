import { fetchMenuTree, fetchCurrentUserInfo, fetchUserRoleMenus, fetchRoleMenuTree } from '../apis';
import { API_PATHS } from '../constants';

/**
 * 平铺菜单树结构
 * @param {Array} menuTree - 菜单树结构
 * @returns {Array} - 平铺后的菜单数组
 */
export const flattenMenuTree = menuTree => {
  if (!menuTree || !Array.isArray(menuTree)) {
    return [];
  }

  const flatMenus = [];

  const flatten = (menus, parentId = '0') => {
    if (!menus || !Array.isArray(menus)) return;

    menus.forEach(menu => {
      // 提取菜单的关键字段到平铺数组
      flatMenus.push({
        id: menu.id,
        text: menu.text,
        parentId: parentId,
        perms: menu.perms || '', // 确保保留权限编码字段
      });

      // 如果有子菜单，递归处理
      if (menu.children && Array.isArray(menu.children)) {
        flatten(menu.children, menu.id);
      }
    });
  };

  flatten(menuTree);
  return flatMenus;
};

/**
 * 根据菜单名称和权限编码查找菜单ID
 * @param {Array} flatMenus - 平铺后的菜单数组
 * @param {Object} params - 查询参数对象
 * @param {string} params.parentName - 父菜单名称
 * @param {string} params.parentPerms - 父菜单权限编码
 * @returns {string} - 找到的菜单ID，如果未找到则返回'-1'
 */
export const findMenuId = (flatMenus, params) => {
  if (!flatMenus || !Array.isArray(flatMenus)) {
    return '0';
  }

  const { parentName, parentPerms } = params || {};

  // 按优先级依次尝试匹配
  // 1. 同时匹配名称和权限编码
  if (parentName && parentPerms) {
    const menu = flatMenus.find(
      m => (m.text || '').toLowerCase().trim() === parentName.toLowerCase().trim() && (m.perms || '').trim() === parentPerms.trim()
    );
    if (menu) return menu.id;
  }

  // 2. 只匹配权限编码
  if (parentPerms) {
    const menu = flatMenus.find(m => (m.perms || '').trim() === parentPerms.trim());
    if (menu) return menu.id;
  }

  // 3. 只匹配菜单名称，但只有唯一匹配时才返回
  if (parentName) {
    const normalizedMenuName = parentName.toLowerCase().trim();
    const matchedMenus = flatMenus.filter(m => (m.text || '').toLowerCase().trim() === normalizedMenuName);
    if (matchedMenus.length === 1) return matchedMenus[0].id;
  }

  return '-1';
};

/**
 * 验证菜单数据格式
 * @param {Object} data - 菜单数据对象
 * @returns {string|null} - 错误信息或 null
 */
export const validateMenuData = data => {
  if (!data || typeof data !== 'object') {
    return '菜单数据必须是有效的JSON对象';
  }

  if (!data.menus || !Array.isArray(data.menus)) {
    return '菜单数据必须包含menus数组字段';
  }

  if (data.menus.length === 0) {
    return '菜单数组不能为空';
  }

  // 检查每个菜单项的必填字段
  const validateMenu = menu => {
    // 检查菜单名称
    if (!menu.name || typeof menu.name !== 'string' || menu.name.trim() === '') {
      return `菜单缺少必要的name字段`;
    }

    // 对于按钮类型的菜单，perms是必填的
    if ((menu.type === 2 || menu.type === '2') && (!menu.perms || menu.perms.trim() === '')) {
      return `按钮菜单"${menu.name}"缺少必要的perms字段`;
    }

    // 递归检查子菜单
    if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
      for (const child of menu.children) {
        const childError = validateMenu(child);
        if (childError) return childError;
      }
    }

    return null;
  };

  // 检查所有顶级菜单
  for (const menu of data.menus) {
    const error = validateMenu(menu);
    if (error) return error;
  }

  return null;
};

/**
 * 处理菜单数据，准备用于API调用
 * @param {Object} menuData - 原始菜单数据
 * @param {Array} flatMenus - 平铺后的菜单数组
 * @returns {Object} - 处理后的菜单数组和错误信息
 */
export const processMenuData = (menuData, flatMenus = []) => {
  if (!menuData || !menuData.menus || !Array.isArray(menuData.menus)) {
    return { menus: [], errors: ['菜单数据无效'] };
  }

  if (menuData.menus.length === 0) {
    return { menus: [], errors: ['菜单数据为空'] };
  }

  const errors = [];
  const processedMenus = [];

  // 递归处理菜单及其子菜单
  const processMenu = (menu, parentId = null) => {
    // 获取 parentId，优先使用传入的值，再使用现有值，最后查找
    const parentName = menu.parentName || '';
    const parentPerms = menu.parentPerms || '';
    let menuParentId = parentId || menu.parentId;

    // 如果没有parentId但有parentName或parentPerms，尝试查找
    if (!menuParentId && (parentName || parentPerms)) {
      menuParentId = findMenuId(flatMenus, { parentName, parentPerms });

      // 记录查找失败的情况
      if (menuParentId === '0' && (parentName || parentPerms)) {
        const criteria = [parentName && `名称"${parentName}"`, parentPerms && `权限编码"${parentPerms}"`].filter(Boolean).join('或');

        errors.push(`菜单"${menu.name}"的父菜单(${criteria})不存在`);
      }
    }

    // 构建处理后的菜单对象
    const processedMenu = {
      name: menu.name,
      parentId: menuParentId || '0',
      orderNum: menu.orderNum || 0,
      type: menu.type || 1,
    };

    // 根据菜单类型添加特定字段
    let finalMenu;

    if (menu.type === 1 || menu.type === '1') {
      // 页面菜单
      finalMenu = {
        ...processedMenu,
        component: menu.component,
        componentName: menu.componentName,
        url: menu.url || menu.path || '',
        icon: menu.icon || '',
        hide: menu.hide || 1,
        perms: menu.perms || '',
      };
    } else if (menu.type === 2 || menu.type === '2') {
      // 按钮菜单
      finalMenu = {
        ...processedMenu,
        perms: menu.perms || '',
      };
    } else {
      finalMenu = processedMenu;
    }

    // 添加处理后的菜单到结果数组
    processedMenus.push(finalMenu);

    // 处理子菜单
    if (menu.children && Array.isArray(menu.children) && menu.children.length > 0) {
      menu.children.forEach(childMenu => {
        processMenu(childMenu, finalMenu.parentId);
      });
    }

    return finalMenu;
  };

  // 处理所有顶级菜单
  menuData.menus.forEach(menu => {
    processMenu(menu);
  });

  return { menus: processedMenus, errors };
};

/**
 * 批量添加菜单到指定站点
 * @param {string} loginToken - 登录令牌
 * @param {Object} site - 站点配置
 * @param {Object} menuData - 菜单数据对象，包含menus数组
 * @param {Map} [externalMenuMap] - 外部传入的已添加菜单映射
 * @returns {Promise<Object>} - 添加结果
 */
export const addMenusToSite = async (loginToken, site, menuData, externalMenuMap) => {
  const addedMenuNames = []; // 用于记录成功添加的菜单名称
  const results = []; // 所有菜单的处理结果
  // 如果传入了外部菜单映射，则使用它；否则创建本地映射
  const addedMenuMap = new Map();

  console.log('输入的菜单数据', menuData);
  console.log('当前菜单映射大小', addedMenuMap.size);

  try {
    if (!site || !site.domain) {
      throw new Error('站点配置无效');
    }

    if (!menuData || !menuData.menus || !Array.isArray(menuData.menus)) {
      throw new Error('菜单数据无效');
    }

    if (menuData.menus.length === 0) {
      throw new Error('菜单数据为空');
    }

    const domain = site.domain;

    const b_accesstoken = loginToken;

    // 获取初始菜单树
    const initialMenuTree = await fetchMenuTree(domain);

    let currentFlatMenus = flattenMenuTree(initialMenuTree);

    // 递归处理菜单，支持 children 结构
    const processRecursive = async menuList => {
      if (!menuList || !Array.isArray(menuList) || menuList.length === 0) {
        return;
      }

      // 逐个处理同级菜单
      for (const menu of menuList) {
        // 保存原始的 children
        const children = menu.children ? [...menu.children] : [];

        // 处理当前菜单
        const processResult = processMenuData({ menus: [menu] }, currentFlatMenus);

        if (processResult.errors && processResult.errors.length > 0) {
          console.log(`菜单"${menu.name}"处理错误:`, processResult.errors);
          results.push({
            menuId: menu.id || '',
            name: menu.name,
            type: menu.type,
            success: false,
            message: processResult.errors[0],
            parentName: menu.parentName || '',
          });
          continue;
        }

        const processedMenu = processResult.menus[0];

        // 检查是否已存在同名菜单
        const normalizedMenuName = menu.name.toLowerCase().trim();
        const menuPerms = menu.perms || '';

        // 查找可能存在的同名菜单，现在需要严格匹配名称和perms
        const existingMenu = currentFlatMenus.find(m => {
          const normalizedText = (m.text || '').toLowerCase().trim();

          // 按钮菜单(type=2)必须同时匹配名称和权限编码
          if (menu.type === 2 || menu.type === '2') {
            return normalizedText === normalizedMenuName && m.perms === menuPerms;
          }

          // 页面菜单(type=1)主要匹配名称，如果有perms也要匹配
          return normalizedText === normalizedMenuName && ((!menuPerms && !m.perms) || (menuPerms && m.perms && menuPerms === m.perms));
        });

        // 查找父菜单信息，用于日志输出
        let parentMenuName = '无';
        if (menu.parentId) {
          const parentMenu = currentFlatMenus.find(m => m.id === menu.parentId);
          if (parentMenu) {
            parentMenuName = parentMenu.text || '未知';
          }
        } else if (menu.parentName) {
          parentMenuName = menu.parentName;
        }

        if (existingMenu) {
          console.log(`跳过菜单"${menu.name}"的添加，因为已存在同名菜单:`, {
            existing: existingMenu.text,
            new: menu.name,
            existingId: existingMenu.id,
            perms: existingMenu.perms || '无',
            menuPerms: menuPerms || '无',
            parentName: parentMenuName,
          });

          // 添加到菜单映射，以便子菜单可以引用
          addedMenuMap.set(menu.name, {
            id: existingMenu.id,
            name: menu.name,
          });

          results.push({
            menuId: existingMenu.id || '',
            name: menu.name,
            type: menu.type,
            success: true,
            message: `菜单"${menu.name}"已存在，可直接使用`,
            parentName: parentMenuName,
            perms: existingMenu.perms || '无',
          });

          // 如果当前菜单存在且有子菜单，在处理子菜单之前刷新菜单树
          if (children && children.length > 0) {
            // 刷新菜单树数据，确保能找到最新添加的菜单
            console.log(`菜单"${menu.name}"已存在，刷新菜单树后处理其子菜单...`);
            const updatedMenuTree = await fetchMenuTree(domain);
            const updatedFlatMenus = flattenMenuTree(updatedMenuTree);

            // 更新菜单树变量以便后续使用
            currentFlatMenus = updatedFlatMenus;

            // 为子菜单设置父菜单名称，确保可以找到父菜单
            const childrenWithParent = children.map(child => ({
              ...child,
              parentName: menu.name, // 确保子菜单知道其父菜单名称
            }));

            // 递归处理子菜单，不再传递parentMenuInfo
            await processRecursive(childrenWithParent);
          }

          continue;
        }

        try {
          // 统一的父菜单ID获取逻辑
          let parentId = null;

          // 1. 尝试从已添加菜单映射中获取
          if (menu.parentName && addedMenuMap.has(menu.parentName)) {
            parentId = addedMenuMap.get(menu.parentName).id;
            console.log(`从已添加菜单映射中找到"${menu.name}"的父菜单ID: ${parentId}`);
          }
          // 2. 从当前菜单树中查找
          else if (menu.parentName || menu.parentPerms) {
            parentId = findMenuId(currentFlatMenus, {
              parentName: menu.parentName,
              parentPerms: menu.parentPerms,
            });

            if (parentId !== '0') {
              console.log(`从当前菜单树中找到"${menu.name}"的父菜单ID: ${parentId}`);
            } else {
              console.log(`无法找到"${menu.name}"的父菜单"${menu.parentName || menu.parentPerms}"的ID`);
            }
          }

          // 设置父菜单ID
          if (parentId && parentId !== '0') {
            processedMenu.parentId = parentId;
            console.log(`设置"${menu.name}"的父菜单ID为: ${processedMenu.parentId}`);
          }

          // 对按钮菜单(type=2)特别检查parentId
          if ((menu.type === 2 || menu.type === '2') && !processedMenu.parentId && (menu.parentName || menu.parentPerms)) {
            const buttonParentId = findMenuId(currentFlatMenus, {
              parentName: menu.parentName,
              parentPerms: menu.parentPerms,
            });

            if (buttonParentId !== '0') {
              processedMenu.parentId = buttonParentId;
              console.log(`为按钮菜单"${menu.name}"设置父菜单ID: ${processedMenu.parentId}`);
            }
          }

          // 最后检查并记录
          console.log(`准备提交菜单"${menu.name}"的最终数据:`, {
            name: processedMenu.name,
            type: processedMenu.type,
            parentId: processedMenu.parentId || '未设置', // 检查最终是否有parentId
            parentName: menu.parentName || '无',
          });

          // 调用菜单保存API
          const saveUrl = `${domain}/manage/base/manage/iop/sys/menu/save`;

          console.log(`正在调用API添加菜单: ${menu.name}`, {
            url: saveUrl,
            menuData: processedMenu,
          });

          const response = await fetch(saveUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              b_accesstoken: b_accesstoken,
            },
            body: JSON.stringify(processedMenu),
          });

          if (!response.ok) {
            throw new Error(`保存菜单失败: ${response.status} ${response.statusText}`);
          }

          const responseData = await response.json();

          console.log(`添加菜单API响应:`, responseData);

          // 检查响应
          if (responseData.code !== 0) {
            throw new Error(`保存菜单失败: ${responseData.msg || '服务器返回错误'}`);
          }

          // 保存新添加的菜单ID
          const menuId = responseData.content?.id || '';
          addedMenuNames.push({
            name: menu.name,
            perms: menu.perms || ''
          });

          // 添加到菜单映射，以便子菜单可以引用
          addedMenuMap.set(menu.name, {
            id: menuId,
            name: menu.name,
          });

          // 添加成功结果
          const menuTypeText = menu.type === 1 ? '页面菜单' : menu.type === 2 ? '按钮菜单' : '菜单';
          results.push({
            menuId: menuId,
            name: menu.name,
            type: menu.type,
            success: true,
            message: `${menuTypeText}添加成功`,
            parentName: menu.parentName || '',
          });

          // 如果有子菜单，在处理子菜单之前刷新菜单树
          if (children && children.length > 0) {
            // 刷新菜单树数据，确保能找到最新添加的菜单
            console.log(`菜单"${menu.name}"添加成功，刷新菜单树后处理其子菜单...`);
            const updatedMenuTree = await fetchMenuTree(domain);
            const updatedFlatMenus = flattenMenuTree(updatedMenuTree);

            // 更新菜单树变量以便后续使用
            currentFlatMenus = updatedFlatMenus;

            // 为子菜单设置父菜单名称，确保可以找到父菜单
            const childrenWithParent = children.map(child => ({
              ...child,
              parentName: menu.name, // 确保子菜单知道其父菜单名称
            }));

            // 递归处理子菜单，不再传递parentMenuInfo
            await processRecursive(childrenWithParent);
          }
        } catch (error) {
          console.error(`添加菜单 ${menu.name} 失败:`, error);

          // 添加失败结果
          results.push({
            menuId: menu.id || '',
            name: menu.name,
            type: menu.type,
            success: false,
            message: error.message,
            parentName: menu.parentName || '',
          });
        }
      }
    };

    // 开始递归处理顶级菜单
    await processRecursive(menuData.menus);

    // 计算成功的添加和失败的添加
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    // 获取错误类型统计
    const parentErrorCount = results.filter(r => !r.success && r.message && r.message.includes('父菜单')).length;

    // 生成详细错误消息
    let detailedErrorMessage = null;
    if (failureCount > 0) {
      if (parentErrorCount > 0) {
        detailedErrorMessage = `存在${parentErrorCount}个父菜单问题`;
      }
    }

    // 如果存在父菜单问题，直接返回结果，跳过权限分配流程
    if (parentErrorCount > 0) {
      console.log(`检测到${parentErrorCount}个父菜单问题，跳过权限分配流程`);

      // 构建返回结果
      return {
        site: site.domain,
        success: false,
        error: `菜单添加存在父菜单问题，请先解决父菜单配置`,
        message: `菜单添加失败: ${detailedErrorMessage}`,
        menus: results,
        permissionAssigned: false,
        permissionError: `存在父菜单问题，无法执行权限分配`,
      };
    }

    let result = {};

    // 收集所有相关菜单ID（新增和已存在的）
    const allRelevantMenus = [];

    // 添加成功添加的菜单
    for (const menuItem of addedMenuNames) {
      allRelevantMenus.push(menuItem);
    }

    // 添加已存在的菜单（带权限编码）
    const existingMenus = results.filter(r => r.success && r.message && r.message.includes('已存在'));
    
    for (const menuResult of existingMenus) {
      // 这里可能会存在多个同名菜单，所以需要判断权限编码是否相同
      if (!allRelevantMenus.find(m => m.name === menuResult.name && m.perms === menuResult.perms)) {
        allRelevantMenus.push({ 
          name: menuResult.name,
          perms: menuResult.perms || ''
        });
      }
    }

    console.log(`共有 ${allRelevantMenus.length} 个菜单需要处理权限`);

    try {
      console.log('开始执行菜单权限分配流程...');

      // 1. 获取最新的菜单树，找出相关的菜单ID
      console.log('获取最新菜单树...');
      const updatedMenuTree = await fetchMenuTree(domain);
      const updatedFlatMenus = flattenMenuTree(updatedMenuTree);

      // 找出所有相关的菜单（使用与新增菜单相同的查找逻辑）
      const relevantMenus = [];
      for (const menuItem of allRelevantMenus) {
        // 使用findMenuId函数查找菜单ID，保持与新增菜单时的逻辑一致
        const menuId = findMenuId(updatedFlatMenus, {
          parentName: menuItem.name,
          parentPerms: menuItem.perms
        });
        
        if (menuId !== '0') {
          // 从扁平菜单列表中找到对应的完整菜单信息
          const foundMenu = updatedFlatMenus.find(m => m.id === menuId);
          if (foundMenu) {
            relevantMenus.push(foundMenu);
            console.log(`找到相关菜单: ${menuItem.name}, ID: ${foundMenu.id}, 权限编码: ${foundMenu.perms || '无'}`);
          }
        }
      }

      // 2. 获取当前用户信息
      console.log('获取当前用户信息...');
      const userId = await fetchCurrentUserInfo(domain);

      if (!userId) {
        throw new Error('无法获取用户ID');
      }

      console.log(`当前用户ID: ${userId}`);

      // 3. 获取用户角色菜单权限
      console.log('获取用户角色菜单权限...');
      const { userRoleList } = await fetchUserRoleMenus(domain, userId);

      // 获取用户角色ID 过滤掉 userId = 1 的账号
      const roleId = userRoleList?.filter(role => role.roleId !== 1)?.[0]?.roleId || '';

      console.log('当前用户角色ID:', roleId);

      // 获取角色菜单树
      console.log('获取角色拥有的菜单树...');
      const roleMenuTree = await fetchRoleMenuTree(domain, roleId);

      // 检查是否成功获取菜单树
      if (!roleMenuTree || (Array.isArray(roleMenuTree) && roleMenuTree.length === 0)) {
        console.error('获取角色菜单树失败，中止权限分配流程');

        // 构建失败结果
        return {
          site: site.domain,
          success: successCount > 0, // 如果有菜单添加成功，整体流程仍然部分成功
          message: successCount > 0 ? `部分菜单添加成功: ${successCount}/${results.length}，但权限分配失败` : '菜单添加和权限分配均失败',
          error: '获取角色菜单树失败，无法完成权限分配',
          menus: results,
          permissionAssigned: false,
          permissionError: '获取角色菜单树失败，请检查角色配置和网络连接',
        };
      }

      // 提取角色已有的菜单ID
      const roleMenuIds = extractMenuIdsFromTree(roleMenuTree.children || []);
      console.log(`角色已拥有 ${roleMenuIds.length} 个菜单权限`);

      // 记录相关菜单ID
      const relevantMenuIds = relevantMenus.map(menu => Number(menu.id));

      // 检查哪些菜单ID已经在角色权限中
      const menuIdsAlreadyInRole = relevantMenuIds.filter(menuId => roleMenuIds.includes(menuId));
      const menuIdsNeedingPermissions = relevantMenuIds.filter(menuId => !roleMenuIds.includes(menuId));

      console.log(`检查权限状态 - 已有权限的菜单: ${menuIdsAlreadyInRole.length}个, 需要分配权限的菜单: ${menuIdsNeedingPermissions.length}个`);

      // 4. 调用菜单权限分配接口
      if (roleId && menuIdsNeedingPermissions.length > 0) {
        console.log(`开始为角色ID: ${roleId} 分配菜单权限...需要分配的菜单ID: ${menuIdsNeedingPermissions.join(', ')}`);

        // 调用菜单权限分配函数
        const permissionResult = await assignMenuPermissionsToRole(domain, b_accesstoken, roleId, roleMenuIds, menuIdsNeedingPermissions);

        if (permissionResult.success) {
          // 更新结果对象，添加权限分配成功信息
          if (result.message) {
            result.message += '，所有菜单权限分配成功';
          }
          result.permissionAssigned = true;
          result.menuIdsNeedingPermissions = menuIdsNeedingPermissions;
        } else {
          // 更新结果对象，添加权限分配失败信息
          if (result.message) {
            result.message += '，但菜单权限分配失败: ' + permissionResult.message;
          }
          result.permissionAssigned = false;
          result.permissionError = permissionResult.message;
          result.menuIdsNeedingPermissions = menuIdsNeedingPermissions;
        }
      } else if (roleId && menuIdsAlreadyInRole.length === relevantMenuIds.length) {
        console.log(`跳过权限分配，所有相关菜单已有权限: ${menuIdsAlreadyInRole.join(', ')}`);

        // 所有菜单已经有权限，视为权限分配成功
        if (result.message) {
          result.message += '，菜单已有权限无需分配';
        }
        result.permissionAssigned = true;
        result.permissionMessage = '所有菜单已有权限';
        result.menuIdsAlreadyInRole = menuIdsAlreadyInRole;
      } else {
        console.log(`跳过权限分配，角色ID: ${roleId || '未获取'}, 相关菜单数: ${relevantMenuIds.length}`);

        // 更新结果对象，添加权限分配跳过信息
        if (result.message) {
          result.message += '，但跳过了权限分配';
        }
        result.permissionAssigned = false;
        result.permissionError = roleId ? '没有需要分配权限的菜单' : '无法获取角色ID，请检查权限设置';
      }

      // 构建最终返回结果
      if (successCount === results.length) {
        result = {
          ...result, // 保留之前设置的权限分配信息
          site: site.domain,
          success: true,
          message: result.message || `成功处理 ${successCount}/${results.length} 个菜单，所有相关菜单权限已处理`,
          detailedError: detailedErrorMessage,
          menus: results,
        };
      } else if (successCount > 0) {
        result = {
          ...result, // 保留之前设置的权限分配信息
          site: site.domain,
          success: true,
          message: result.message || `部分菜单添加成功: ${successCount}/${results.length}，但权限分配失败`,
          detailedError: detailedErrorMessage,
          menus: results,
          permissionAssigned: false,
          permissionError: `权限分配流程异常，请检查网络连接`,
        };
      } else {
        result = {
          ...result, // 保留之前设置的权限分配信息
          site: site.domain,
          success: false,
          error: detailedErrorMessage || '所有菜单添加失败',
          menus: results,
          permissionAssigned: false,
          permissionError: `权限分配流程异常，请检查网络连接`,
        };
      }
    } catch (error) {
      console.error('菜单权限分配流程失败:', error);

      // 即使权限分配失败，菜单添加结果仍然保持
      if (successCount === results.length) {
        result = {
          ...result, // 保留之前设置的权限分配信息
          site: site.domain,
          success: true,
          message: `所有菜单添加成功，但权限分配失败`,
          menus: results,
          permissionAssigned: false,
          permissionError: `权限分配流程异常，请检查网络连接`,
        };
      } else if (successCount > 0) {
        result = {
          ...result, // 保留之前设置的权限分配信息
          site: site.domain,
          success: true,
          message: `部分菜单添加成功: ${successCount}/${results.length}，但权限分配失败`,
          detailedError: detailedErrorMessage,
          menus: results,
          permissionAssigned: false,
          permissionError: `权限分配流程异常，请检查网络连接`,
        };
      }
    }

    return result;
  } catch (error) {
    console.error(`向站点 ${site.domain} 添加菜单失败:`, error);
    return {
      site: site.domain,
      success: false,
      error: error.message,
      menus: [],
    };
  }
};

/**
 * 为角色分配菜单权限
 * @param {string} domainUrl - 域名URL
 * @param {string} token - 访问令牌
 * @param {string} roleId - 角色ID
 * @param {Array} existingMenuIds - 已有的菜单权限ID列表
 * @param {Array} newMenuIds - 新增的菜单ID列表
 * @returns {Promise<Object>} - 权限分配结果
 */
export const assignMenuPermissionsToRole = async (domainUrl, token, roleId, existingMenuIds = [], newMenuIds = []) => {
  try {
    // 参数检查
    if (!domainUrl || !token || !roleId) {
      return {
        success: false,
        message: '缺少必要参数：域名、访问令牌或角色ID',
      };
    }

    if (!newMenuIds || newMenuIds.length === 0) {
      return {
        success: true,
        message: '无需分配新的菜单权限',
      };
    }

    const domain = domainUrl;

    // 过滤已经存在的菜单ID
    const actualNewMenuIds = newMenuIds.filter(id => !existingMenuIds.includes(id));

    if (actualNewMenuIds.length === 0) {
      console.log('所有菜单已经拥有权限，无需执行权限分配');
      return {
        success: true,
        message: '所有菜单已有权限，无需分配',
      };
    }

    // 合并所有来源的菜单ID并去重
    const allMenuIds = [
      ...new Set([
        ...existingMenuIds, // 从参数传入的已有权限
        ...actualNewMenuIds, // 实际需要新增的菜单
      ]),
    ];

    console.log(`权限合并明细 - 角色已有权限: ${existingMenuIds.length}个, 本次新增权限: ${actualNewMenuIds.length}个`);
    console.log(`合并后总共分配 ${allMenuIds.length} 个菜单权限`);

    // 如果没有菜单可分配，返回错误
    if (allMenuIds.length === 0) {
      return {
        success: false,
        message: '没有可分配权限的菜单',
      };
    }

    // 构建请求参数
    const menuAssignPermissionsUrl = `${domain}${API_PATHS.PERMISSION_ASSIGN}`;

    const menuAssignPermissionsList = allMenuIds.map(menuId => ({
      menuId: menuId,
    }));

    const permissionParams = {
      roleId: roleId,
      menuAssignPermissionsList,
    };

    // 发送请求
    const permissionResponse = await fetch(menuAssignPermissionsUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        b_accesstoken: token,
        Locallanguage: 'zh-CN',
      },
      body: JSON.stringify(permissionParams),
    });

    if (!permissionResponse.ok) {
      throw new Error(`分配权限请求失败: ${permissionResponse.status} ${permissionResponse.statusText}`);
    }

    const permissionResult = await permissionResponse.json();

    if (permissionResult.code !== 0) {
      throw new Error(`分配权限失败: ${permissionResult.msg || '服务器返回错误'}`);
    }

    console.log(`菜单权限分配成功! 角色ID: ${roleId}, 分配权限的菜单数: ${allMenuIds.length}`);

    return {
      success: true,
      message: '菜单权限分配成功',
    };
  } catch (error) {
    console.error('菜单权限分配请求失败:', error);
    return {
      success: false,
      message: error.message,
      error: error,
    };
  }
};

/**
 * 从菜单树中提取所有菜单ID
 * @param {Array} menuTree - 菜单树结构
 * @returns {Array} - 所有菜单ID的数组
 */
export const extractMenuIdsFromTree = menuTree => {
  if (!menuTree || !Array.isArray(menuTree)) {
    return [];
  }

  const menuIds = [];

  const extract = menus => {
    if (!menus || !Array.isArray(menus)) return;

    menus.forEach(menu => {
      // 添加当前菜单ID
      if (menu.id) {
        menuIds.push(Number(menu.id));
      }

      // 如果有子菜单，递归处理
      if (menu.children && Array.isArray(menu.children)) {
        extract(menu.children);
      }
    });
  };

  extract(menuTree);
  return menuIds;
};
