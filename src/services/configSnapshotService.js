/**
 * 配置快照管理服务
 * 负责处理各种类型配置的云端上传和同步逻辑
 */

import { cloudSyncService } from './cloudSyncService.js';
import { CONFIG_TYPES, CONFIG_SNAPSHOT_FIELDS } from '../constants/feishuConfig.js';
import { CommonQueries } from '../constants/queryFilters.js';

/**
 * 配置快照管理服务类
 */
class ConfigSnapshotService {
  constructor() {
    this.uploadQueue = [];
    this.isProcessing = false;
  }

  /**
   * 上传配置快照
   * @param {Object} params - 上传参数
   * @param {Object} params.configData - 配置数据
   * @param {string} params.configType - 配置类型
   * @param {string} params.context - 上下文信息
   * @param {string} params.description - 描述信息
   * @returns {Promise<Object>} 上传结果
   */
  async uploadSnapshot({ configData, configType, context = '', description = '' }) {
    try {
      if (!configData) {
        throw new Error('配置数据不能为空');
      }

      // 构建快照数据
      const snapshot = {
        snapshot_name: this.generateSnapshotName(configType),
        config_json: JSON.stringify(configData),
        config_type: configType,
        description: description || this.generateDescription(configType, context),
        created_by: this.getCreatedByName(configType),
        created_at: Date.now(), // Unix 时间戳（毫秒）
      };

      console.log('ConfigSnapshotService: 正在上传配置快照...', snapshot.snapshot_name);

      const result = await cloudSyncService.uploadSnapshot(snapshot);

      if (result.success) {
        console.log('ConfigSnapshotService: 配置快照上传成功:', result.recordId);
        return {
          success: true,
          recordId: result.recordId,
          message: '配置已自动备份到云端',
          snapshotName: snapshot.snapshot_name,
          createdAt: new Date(snapshot.created_at).toLocaleString(), // 转换为可读格式
          tip: '如需查看最新记录，请在飞书表格中按创建时间倒序排列',
        };
      } else {
        throw new Error(result.message || '上传失败');
      }
    } catch (error) {
      console.error('ConfigSnapshotService: 上传配置快照失败:', error);
      throw error;
    }
  }

  /**
   * 异步上传配置快照（不阻塞主流程）
   * @param {Object} params - 上传参数
   * @returns {Promise<void>} 无返回值，静默处理
   */
  async uploadSnapshotAsync(params) {
    // 添加到队列
    this.uploadQueue.push(params);

    // 如果没有在处理，开始处理队列
    if (!this.isProcessing) {
      this.processUploadQueue();
    }
  }

  /**
   * 处理上传队列
   */
  async processUploadQueue() {
    if (this.isProcessing || this.uploadQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.uploadQueue.length > 0) {
      const params = this.uploadQueue.shift();

      try {
        await this.uploadSnapshot(params);
        console.log('ConfigSnapshotService: 队列中的配置快照上传成功');
      } catch (error) {
        console.error('ConfigSnapshotService: 队列中的配置快照上传失败:', error);
        // 继续处理下一个，不中断队列
      }

      // 添加延迟，避免API限流
      await this.delay(1000);
    }

    this.isProcessing = false;
  }

  /**
   * 同步站点配置到云端
   * @param {Array} siteConfigs - 站点配置数组
   * @param {string} operation - 操作类型：'upload' | 'update'
   * @returns {Promise<Object>} 同步结果
   */
  async syncSiteConfigs(siteConfigs, operation = 'upload') {
    try {
      if (!siteConfigs || !Array.isArray(siteConfigs)) {
        throw new Error('站点配置数据格式不正确');
      }

      const configData = {
        configs: siteConfigs,
        timestamp: Date.now(), // Unix 时间戳（毫秒）
        operation: operation,
        count: siteConfigs.length,
      };

      const result = await this.uploadSnapshot({
        configData,
        configType: CONFIG_TYPES.SITE_CONFIG,
        context: `${operation === 'upload' ? '手动上传' : '手动更新'}${siteConfigs.length}个站点配置`,
        description: `站点配置${operation === 'upload' ? '上传' : '更新'}，共${siteConfigs.length}个站点`,
      });

      return result;
    } catch (error) {
      console.error('ConfigSnapshotService: 同步站点配置失败:', error);
      throw error;
    }
  }

  /**
   * 从云端获取最新的站点配置
   * @returns {Promise<Object>} 获取结果
   */
  async getLatestSiteConfigs() {
    try {
      console.log('ConfigSnapshotService: 正在获取最新站点配置...');

      // 查询站点配置类型的快照，使用正确的飞书API格式
      console.log('ConfigSnapshotService: 查询条件 config_type =', CONFIG_TYPES.SITE_CONFIG);
      console.log('ConfigSnapshotService: 字段映射 CONFIG_TYPE =', CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE);
      console.log('ConfigSnapshotService: 使用查询配置:', JSON.stringify(CommonQueries.latestSiteConfig, null, 2));


      const result = await cloudSyncService.querySnapshots(CommonQueries.latestSiteConfig);

      if (result.success && result.data && result.data.length > 0) {
        const latestSnapshot = result.data[0];
        console.log('ConfigSnapshotService: 获取到最新站点配置:', latestSnapshot.snapshotName);

        let configData;
        try {
          configData = JSON.parse(latestSnapshot.configJson || '{}');
        } catch (parseError) {
          console.error('ConfigSnapshotService: 解析配置JSON失败:', parseError);
          throw new Error(`配置数据解析失败: ${parseError.message}`);
        }

        // 处理不同的数据结构
        let configs = [];
        if (Array.isArray(configData)) {
          // 如果configData直接是数组（旧格式）
          configs = configData;
        } else if (configData.configs && Array.isArray(configData.configs)) {
          // 如果configData是对象且包含configs属性（新格式）
          configs = configData.configs;
        } else {
          console.warn('ConfigSnapshotService: 未识别的配置数据格式:', configData);
        }

        console.log('ConfigSnapshotService: 最终解析的站点配置数量:', configs.length);

        return {
          success: true,
          configs: configs,
          snapshot: {
            name: latestSnapshot.snapshotName,
            createdAt: latestSnapshot.createdAt,
            createdBy: latestSnapshot.createdBy,
            recordId: latestSnapshot.recordId,
          },
          message: '成功获取最新站点配置',
        };
      } else {
        console.log('ConfigSnapshotService: 未找到站点配置快照，查询结果详情:', result);
        return {
          success: false,
          configs: [],
          message: result.data?.length === 0 ? '云端暂无站点配置快照' : '查询站点配置失败',
        };
      }
    } catch (error) {
      console.error('ConfigSnapshotService: 获取最新站点配置失败:', error);
      console.error('ConfigSnapshotService: 错误堆栈:', error.stack);
      throw error;
    }
  }

  /**
   * 生成快照名称
   * @param {string} configType - 配置类型
   * @returns {string} 快照名称
   */
  generateSnapshotName(configType) {
    const typeName = this.getConfigTypeName(configType);
    const timestamp = this.formatDateTime(new Date());
    return `${typeName}-${timestamp}`;
  }

  /**
   * 生成描述信息
   * @param {string} configType - 配置类型
   * @param {string} context - 上下文信息
   * @returns {string} 描述信息
   */
  generateDescription(configType, context) {
    const typeName = this.getConfigTypeName(configType);
    return `${typeName}自动备份${context ? `，${context}` : ''}`;
  }

  /**
   * 获取配置类型名称
   * @param {string} configType - 配置类型
   * @returns {string} 配置类型名称
   */
  getConfigTypeName(configType) {
    switch (configType) {
      case CONFIG_TYPES.TRANSLATION_CONFIG:
        return '翻译配置';
      case CONFIG_TYPES.MENU_CONFIG:
        return '菜单配置';
      case CONFIG_TYPES.SITE_CONFIG:
        return '站点配置';
      default:
        return '未知配置';
    }
  }

  /**
   * 获取创建者名称
   * @param {string} configType - 配置类型
   * @returns {string} 创建者名称
   */
  getCreatedByName(configType) {
    switch (configType) {
      case CONFIG_TYPES.TRANSLATION_CONFIG:
        return '翻译系统';
      case CONFIG_TYPES.MENU_CONFIG:
        return '菜单管理系统';
      case CONFIG_TYPES.SITE_CONFIG:
        return '站点管理系统';
      default:
        return '系统用户';
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 格式化日期时间为 YYYY-MM-DD HH:mm:ss
   * @param {Date} date - 日期对象
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 获取队列状态
   * @returns {Object} 队列状态
   */
  getQueueStatus() {
    return {
      queueLength: this.uploadQueue.length,
      isProcessing: this.isProcessing,
    };
  }

  /**
   * 获取配置历史记录
   * @param {string} configType - 配置类型
   * @param {Object} options - 查询选项
   * @param {Object} [options.filter] - 自定义过滤条件
   * @param {Array} [options.sort] - 排序条件
   * @param {number} [options.pageSize] - 页面大小
   * @returns {Promise<Object>} 历史记录结果
   */
  async getConfigHistory(configType, options = {}) {
    try {
      console.log('ConfigSnapshotService: 获取配置历史记录，类型:', configType, '选项:', options);

      // 构建查询选项
      const queryOptions = {
        pageSize: options.pageSize || 50,
        ...options
      };

      // 如果没有提供自定义过滤条件，使用默认的配置类型过滤
      if (!options.filter) {
        queryOptions.filter = {
          conjunction: 'and',
          conditions: [
            {
              field_name: CONFIG_SNAPSHOT_FIELDS.CONFIG_TYPE,
              operator: 'is',
              value: [configType],
            },
          ],
        };
      }

      // 如果没有提供排序条件，默认按创建时间倒序
      if (!options.sort) {
        queryOptions.sort = [
          {
            field_name: CONFIG_SNAPSHOT_FIELDS.CREATED_AT,
            desc: true,
          },
        ];
      }

      console.log('ConfigSnapshotService: 最终查询选项:', JSON.stringify(queryOptions, null, 2));

      const result = await cloudSyncService.querySnapshots(queryOptions);

      if (result.success) {
        console.log('ConfigSnapshotService: 获取到历史记录数量:', result.data?.length || 0);
        return {
          success: true,
          data: result.data || [],
          hasMore: result.hasMore,
          pageToken: result.pageToken,
          message: '获取历史记录成功'
        };
      } else {
        return {
          success: false,
          data: [],
          message: '未找到历史记录'
        };
      }
    } catch (error) {
      console.error('ConfigSnapshotService: 获取配置历史记录失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
export const configSnapshotService = new ConfigSnapshotService();
export default configSnapshotService;
