import { API_PATHS } from '../constants';

/**
 * 文件上传服务
 * 负责处理文件上传相关操作
 */
export const fileUploadService = {
  /**
   * 使用API方式上传文件
   * @param {Object} uploadData - 上传数据
   * @param {string} uploadData.domain - 站点域名
   * @param {Array} uploadData.files - 文件信息数组
   * @param {string} uploadData.token - 认证token
   * @param {Object} fileContents - 文件内容映射
   * @returns {Promise<Object>} - 上传结果
   */
  async uploadFiles(uploadData, fileContents) {
    try {
      // 将文件信息转换为更简单的格式
      const simplifiedFiles = uploadData.files.map(file => ({
        id: file.id,
        filePath: file.filePath,
      }));

      // 格式化域名，确保包含协议前缀
      const domain = uploadData.domain;

      const b_accesstoken = uploadData.token;

      if (!b_accesstoken) {
        throw new Error('未提供有效的token，无法上传文件');
      }

      // 执行上传函数
      const results = [];

      // 处理所有文件上传
      for (const file of simplifiedFiles) {
        let fileInfo = null;
        let base64Content = null;

        try {
          // 获取文件信息和 Base64 内容
          fileInfo = fileContents[file.id];
          if (!fileInfo || !fileInfo.base64Content) {
            throw new Error(`文件元信息或 Base64 内容未找到: ${file.id}`);
          }
          base64Content = fileInfo.base64Content;
        } catch (error) {
          results.push({
            file: file.filePath,
            id: file.id,
            success: false,
            error: `文件准备失败: ${error.message || '未知错误'}`,
          });
          continue; // 跳过当前文件
        }

        // 如果准备成功，则尝试上传
        try {
          // 上传文件
          const uploadResult = await this.uploadSingleFile(domain, b_accesstoken, file, base64Content);
          results.push(uploadResult);
        } catch (error) {
          results.push({
            file: file.filePath,
            id: file.id,
            success: false,
            error: `上传失败: ${error.message || '未知错误'}`,
          });
        }
      }

      // 计算成功的上传和失败的上传
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      if (successCount === results.length) {
        return {
          success: true,
          results: results,
        };
      } else if (successCount > 0) {
        return {
          success: true,
          results: results,
          warning: `部分文件上传失败: ${failureCount}/${results.length}`,
        };
      } else {
        return {
          success: false,
          error: '所有文件上传失败',
          results: results,
        };
      }
    } catch (error) {
      console.error('API上传过程中出错:', error);
      return {
        success: false,
        error: error.message,
        results: [],
      };
    }
  },

  /**
   * 上传单个文件
   * @param {string} domain - 站点域名
   * @param {string} token - 认证token
   * @param {Object} file - 文件信息
   * @param {string} base64Content - Base64编码的文件内容
   * @returns {Promise<Object>} - 上传结果
   */
  async uploadSingleFile(domain, token, file, base64Content) {
    // 辅助函数：将 Base64 转换为 ArrayBuffer
    function base64ToArrayBuffer(base64) {
      const binary_string = atob(base64);
      const len = binary_string.length;
      const bytes = new Uint8Array(len);
      for (let i = 0; i < len; i++) {
        bytes[i] = binary_string.charCodeAt(i);
      }
      return bytes.buffer;
    }

    // 解码 Base64
    const fileArrayBuffer = base64ToArrayBuffer(base64Content);

    // 获取临时上传URL
    const tempKeyUrl = `${domain}${API_PATHS.OBS_TEMP_KEY}`;
    const tempKeyResponse = await fetch(tempKeyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        b_accesstoken: token,
      },
      body: JSON.stringify({
        expires: 630720000,
        filePath: file.filePath,
        type: 1,
      }),
    });

    if (!tempKeyResponse.ok) {
      throw new Error(`获取临时密钥失败: ${tempKeyResponse.status} ${tempKeyResponse.statusText}`);
    }

    const tempKeyData = await tempKeyResponse.json();
    if (!tempKeyData.content) {
      throw new Error('获取临时密钥响应中未找到有效的上传URL');
    }

    const uploadUrl = tempKeyData.content;

    if (!uploadUrl || typeof uploadUrl !== 'string' || !uploadUrl.startsWith('http')) {
      throw new Error(`获取到的上传URL格式无效`);
    }

    // 上传文件
    const headers = {
      'Content-Type': 'application/octet-stream',
    };

    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      headers: headers,
      body: fileArrayBuffer,
      mode: 'cors',
      credentials: 'omit',
    });

    if (!uploadResponse.ok) {
      throw new Error(`上传文件失败: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    return {
      file: file.filePath,
      id: file.id,
      success: true,
      site: domain,
    };
  },
};
