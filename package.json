{"name": "gq-unified-platform-chrome-extension", "description": "GQ Unified Platform Chrome Extension", "private": true, "version": "1.0.0", "type": "module", "author": {"name": "v-huangaolin"}, "scripts": {"dev": "vite", "build": "vite build", "build:extension": "vite build && node scripts/copy-manifest.js", "package": "node scripts/package-extension.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/pro-components": "^2.8.7", "@codemirror/lang-json": "^6.0.1", "@codemirror/theme-one-dark": "^6.1.2", "@uiw/react-codemirror": "^4.23.12", "antd": "^5.24.8", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-syntax-highlighter": "^15.6.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "consola": "^3.4.2", "crx": "^5.0.1", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "terser": "^5.39.0", "vite": "^6.3.1"}}