# 云端配置管理系统需求文档

## 📋 项目概述

本项目旨在为 Chrome 插件增加云端配置管理功能，通过飞书多维表格实现配置的云端存储、版本管理和团队协作。系统将支持站点配置、翻译配置、菜单配置等多种配置类型的自动化管理。

## 🎯 核心目标

1. **自动化配置备份**：关键操作完成后自动上传配置到云端
2. **版本管理**：支持配置的历史版本查询和回滚
3. **团队协作**：多人共享配置，支持按创建者、时间等维度查询
4. **无缝集成**：与现有 Chrome 插件功能深度集成，用户体验流畅

## 📊 数据结构设计

### ConfigSnapshots 表结构（飞书多维表格）

| 字段名          | 类型     | 说明           | 示例                                    |
| --------------- | -------- | -------------- | --------------------------------------- |
| snapshot_name   | 文本     | 快照名称       | "翻译完成-20250730-143022"              |
| config_json     | 长文本   | 配置 JSON 内容 | {"sites": [...], "translations": [...]} |
| config_type     | 单选     | 配置类型       | "站点配置"/"翻译配置"/"菜单配置"        |
| trigger_context | 文本     | 触发上下文     | "完成 127 条翻译"                       |
| created_by      | 文本     | 创建者         | "张三"                                  |
| created_at      | 日期时间 | 创建时间       | 2025-07-30 14:30:22                     |
| description     | 文本     | 描述信息       | "新增英文翻译，优化菜单结构"            |

## 🚀 功能模块任务拆分

## 任务一：飞书 API 集成基础设施

### 1.1 飞书应用权限配置

**优先级**：🔴 高
**描述**：配置飞书开放平台应用权限，确保能够访问多维表格 API

**具体任务**：

- [ ] 在飞书开发者后台开启机器人能力
- [ ] 申请云文档相关权限（读取、写入、管理）
- [ ] 创建专用群组并添加应用机器人
- [ ] 配置多维表格访问权限（群组授权或直接添加协作者）
- [ ] 发布应用并通过管理员审核

**验收标准**：

- 应用能够成功获取 `tenant_access_token`
- 能够通过 API 访问指定的多维表格
- 具有读写权限，能够创建和查询记录

### 1.2 CloudSyncService 服务开发

**优先级**：🔴 高
**描述**：开发核心的云端同步服务，封装飞书 API 调用逻辑

**具体任务**：

- [ ] 创建 `src/services/cloudSyncService.js`
- [ ] 实现 `getTenantAccessToken()` 方法（token 获取和缓存）
- [ ] 实现 `uploadSnapshot()` 方法（配置上传）
- [ ] 实现 `querySnapshots()` 方法（配置查询）
- [ ] 添加错误处理和重试机制
- [ ] 实现 API 限流控制（QPS 限制）

**技术要点**：

```javascript
class CloudSyncService {
  // Token管理
  async getTenantAccessToken()

  // 配置管理
  async uploadSnapshot(snapshot)
  async querySnapshots(filters)
  async deleteSnapshot(id)

  // 错误处理
  handleApiError(error)
  retryRequest(request, maxRetries)
}
```

**验收标准**：

- 所有 API 方法正常工作
- Token 自动刷新机制有效
- 错误处理完善，有友好的错误提示
- 支持请求重试和限流控制

### 1.3 安全配置管理

**优先级**：🟡 中
**描述**：安全存储和管理飞书应用凭证

**具体任务**：

- [ ] 在 `background.js` 中安全存储 APP_ID 和 APP_SECRET
- [ ] 使用 Chrome Storage API 加密存储敏感信息
- [ ] 实现凭证的安全传输机制
- [ ] 添加凭证有效性验证
- [ ] 配置 manifest.json 权限（飞书域名白名单）

**验收标准**：

- 敏感信息不暴露在前端代码中
- 凭证存储安全可靠
- 支持凭证更新和验证

## 任务二：自动触发机制

### 2.1 翻译完成自动上传

**优先级**：🔴 高
**描述**：翻译操作完成后自动上传配置快照

**具体任务**：

- [ ] 修改 `src/services/translationService.js`
- [ ] 在翻译完成回调中集成云端上传逻辑
- [ ] 生成翻译配置快照数据结构
- [ ] 添加上传成功/失败的用户提示
- [ ] 实现离线缓存机制（网络异常时）

**集成点**：

```javascript
// translationService.js
const handleTranslationComplete = async result => {
  // 原有逻辑...

  // 新增：自动上传
  try {
    await cloudSyncService.uploadSnapshot({
      snapshot_name: `翻译完成-${new Date().toISOString()}`,
      config_json: JSON.stringify(result),
      config_type: '翻译配置',
      trigger_type: 'translation_success',
      // ...其他字段
    });
  } catch (error) {
    // 错误处理
  }
};
```

**验收标准**：

- 翻译完成后自动触发上传
- 上传失败不影响翻译功能正常使用
- 有明确的成功/失败反馈

### 2.2 菜单权限分配自动上传

**优先级**：🔴 高
**描述**：菜单权限分配完成后自动上传配置快照

**具体任务**：

- [ ] 修改 `src/services/menuService.js`
- [ ] 在菜单权限分配完成回调中集成云端上传逻辑
- [ ] 生成菜单配置快照数据结构
- [ ] 添加批量操作的上传优化（避免频繁上传）
- [ ] 实现增量上传机制

**验收标准**：

- 菜单权限分配完成后自动触发上传
- 批量操作时合理控制上传频率
- 配置内容完整准确

### 2.3 站点配置变更监听

**优先级**：🟡 中
**描述**：监听站点配置的重要变更并自动上传

**具体任务**：

- [ ] 实现配置变更检测机制
- [ ] 定义需要自动上传的变更类型
- [ ] 添加变更摘要生成逻辑
- [ ] 实现防抖机制（避免频繁上传）

**验收标准**：

- 能够检测到重要的配置变更
- 自动上传时机合理
- 变更摘要信息准确

## 任务三：JSON 编辑器云端功能

### 3.1 CloudConfigBrowser 组件开发

**优先级**：🔴 高
**描述**：开发云端配置浏览器组件，支持查询、预览、加载历史版本

**具体任务**：

- [ ] 创建 `src/components/CloudConfigBrowser/index.jsx`
- [ ] 实现搜索过滤器界面（快照名称、创建者、时间范围、配置类型）
- [ ] 实现结果列表展示（分页、排序）
- [ ] 实现 JSON 内容预览面板
- [ ] 添加一键加载历史版本功能
- [ ] 实现版本对比功能
- [ ] 添加删除和重命名功能

**组件结构**：

```jsx
<CloudConfigBrowser>
  <SearchFilters />
  <ResultsList />
  <PreviewPanel />
  <VersionComparison />
</CloudConfigBrowser>
```

**验收标准**：

- 搜索功能完整，支持多维度过滤
- 分页和排序正常工作
- JSON 预览格式化良好
- 版本加载和对比功能正常

### 3.2 JSON 编辑器集成

**优先级**：🔴 高
**描述**：将云端配置功能集成到现有的 JSON 编辑器中

**具体任务**：

- [ ] 在 JSON 编辑器中添加"云端配置"标签页
- [ ] 集成 CloudConfigBrowser 组件
- [ ] 实现配置加载到编辑器的功能
- [ ] 添加手动上传当前配置的功能
- [ ] 实现配置同步状态指示器

**集成位置**：

- Options 页面的 JSON 编辑器
- Translation 页面的配置管理区域
- BatchAddMenu 页面的配置区域

**验收标准**：

- 云端配置功能与现有编辑器无缝集成
- 用户操作流程顺畅
- 数据同步状态清晰可见

### 3.3 离线支持和缓存

**优先级**：🟡 中
**描述**：实现离线模式下的配置管理

**具体任务**：

- [ ] 实现本地配置缓存机制
- [ ] 添加离线状态检测
- [ ] 实现离线队列（网络恢复后自动同步）
- [ ] 添加冲突解决机制
- [ ] 实现缓存清理策略

**验收标准**：

- 离线状态下基本功能可用
- 网络恢复后自动同步
- 冲突处理合理

## 任务四：用户体验优化

### 4.1 操作反馈和状态提示

**优先级**：🟡 中
**描述**：完善用户操作的反馈机制

**具体任务**：

- [ ] 添加上传进度指示器
- [ ] 实现操作成功/失败的 Toast 提示
- [ ] 添加网络状态指示器
- [ ] 实现操作历史记录
- [ ] 添加快捷操作按钮

**验收标准**：

- 所有操作都有明确反馈
- 进度指示清晰
- 错误信息友好易懂

### 4.2 性能优化

**优先级**：🟡 中
**描述**：优化云端配置功能的性能表现

**具体任务**：

- [ ] 实现配置列表虚拟滚动
- [ ] 添加搜索结果缓存
- [ ] 优化 JSON 预览的渲染性能
- [ ] 实现懒加载机制
- [ ] 添加请求去重逻辑

**验收标准**：

- 大量配置数据时界面响应流畅
- 搜索和过滤操作快速
- 内存使用合理

### 4.3 数据统计和分析

**优先级**：🟢 低
**描述**：提供配置使用的统计信息

**具体任务**：

- [ ] 实现配置使用频率统计
- [ ] 添加团队协作统计
- [ ] 实现配置大小和增长趋势分析
- [ ] 添加热门配置推荐
- [ ] 实现数据导出功能

**验收标准**：

- 统计数据准确
- 图表展示清晰
- 导出功能完整

## 任务五：测试和文档

### 5.1 单元测试

**优先级**：🟡 中
**描述**：为核心功能编写单元测试

**具体任务**：

- [ ] CloudSyncService 测试用例
- [ ] 自动触发机制测试
- [ ] CloudConfigBrowser 组件测试
- [ ] API 错误处理测试
- [ ] 离线功能测试

### 5.2 集成测试

**优先级**：🟡 中
**描述**：端到端功能测试

**具体任务**：

- [ ] 完整的配置上传下载流程测试
- [ ] 多用户协作场景测试
- [ ] 网络异常情况测试
- [ ] 性能压力测试

### 5.3 用户文档

**优先级**：🟡 中
**描述**：编写用户使用文档

**具体任务**：

- [ ] 功能使用指南
- [ ] 常见问题解答
- [ ] 故障排除指南
- [ ] 最佳实践建议

## 📈 实施计划建议

### 第一阶段：基础设施（1-2 周）

- 飞书 API 集成基础设施
- CloudSyncService 服务开发
- 基础的上传下载功能

### 第二阶段：自动化功能（1 周）

- 翻译完成自动上传
- 菜单权限分配自动上传
- 基础的错误处理

### 第三阶段：用户界面（1-2 周）

- CloudConfigBrowser 组件开发
- JSON 编辑器集成
- 基础的查询和预览功能

### 第四阶段：优化完善（1 周）

- 性能优化
- 用户体验优化
- 测试和文档完善

## 🔧 技术架构

### 核心服务层

```
src/services/
├── cloudSyncService.js     # 飞书API封装
├── translationService.js   # 翻译服务（集成云端上传）
├── menuService.js          # 菜单服务（集成云端上传）
└── configCacheService.js   # 配置缓存服务
```

### 组件层

```
src/components/
├── CloudConfigBrowser/     # 云端配置浏览器
├── ConfigSyncStatus/       # 同步状态组件
├── VersionComparison/      # 版本对比组件
└── UploadProgress/         # 上传进度组件
```

### 工具层

```
src/utils/
├── feishuApi.js           # 飞书API工具函数
├── configDiff.js          # 配置差异对比
└── compressionUtils.js    # 配置压缩工具
```

## ⚠️ 风险和注意事项

### 技术风险

1. **飞书 API 限流**：需要实现合理的请求控制机制
2. **跨域问题**：Chrome 插件环境下的 API 调用限制
3. **数据安全**：敏感配置信息的安全存储和传输

### 业务风险

1. **权限管理**：确保团队成员有适当的访问权限
2. **数据一致性**：多人同时编辑时的冲突处理
3. **备份策略**：重要配置的备份和恢复机制

### 解决方案

1. 实现完善的错误处理和重试机制
2. 添加离线模式支持
3. 建立配置版本管理和冲突解决机制
4. 定期备份重要配置数据

## 📋 验收标准

### 功能完整性

- [ ] 所有核心功能正常工作
- [ ] 自动触发机制稳定可靠
- [ ] 用户界面友好易用
- [ ] 错误处理完善

### 性能要求

- [ ] 配置上传响应时间 < 3 秒
- [ ] 配置查询响应时间 < 2 秒
- [ ] 界面操作响应时间 < 1 秒
- [ ] 支持 1000+配置记录的流畅操作

### 稳定性要求

- [ ] 网络异常时功能降级正常
- [ ] 长时间运行无内存泄漏
- [ ] 并发操作处理正确
- [ ] 数据一致性保证

---

**文档版本**：v1.0  
**创建时间**：2025-07-30  
**最后更新**：2025-07-30  
**负责人**：开发团队
