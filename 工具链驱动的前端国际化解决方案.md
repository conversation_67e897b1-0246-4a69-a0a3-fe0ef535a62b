# 工具链驱动的前端国际化解决方案

## 一、执行摘要 (Executive Summary)

本方案提出了一套以前端为主导、工具链为驱动的国际化（i18n）解决方案。它通过标准化的接口约定与自研的广汽国际平台 Chrome 插件`[1]`、VSCode 多语言开发助手`[2]`相结合，旨在解决传统 i18n 方案中翻译内容维护繁琐、开发体验差的核心痛点。方案的核心是"后端存储，前端配置"，前端通过请求头传递语言标识，动态获取翻译资源。配套的工具链则实现了多语言内容的自动化生成与开发过程中的实时预览，最终形成一个从翻译、开发到维护的高效闭环。

## 二、快速上手 (Quick Start)

本章节为开发者提供在项目中快速应用本方案的最小化流程。

1.  **获取翻译内容**：在 React/Vue 组件中，通过`t('some.key')`或`$t('some.key')`使用翻译。
2.  **查看翻译文本**：
    - 安装配套的 **VSCode 多语言开发助手**`[2]`。
    - 配置好后端翻译接口地址。
    - 扩展将自动识别代码中的翻译键，并在行内显示对应的中文，无需离开编辑器。
3.  **添加新翻译条目**：
    - 安装配套的 **广汽国际平台 Chrome 插件**`[1]`。
    - 打开"多语言翻译工具"页面。
    - 输入包含新文本的 JSON 数据，利用豆包 API 自动翻译成所有目标语言。
    - 导出生成的翻译文件并交由后端导入。

## 三、方案总览 (Architecture Overview)

### 1. 核心架构与数据流

本方案由前端应用、后端服务、数据库、缓存以及辅助工具链（广汽国际平台 Chrome 插件`[1]` 和 VSCode 多语言开发助手`[2]`）构成。其核心数据流如下：

```mermaid
flowchart TD
    subgraph "开发时"
        Dev["开发者"]
        VSCodeExt["VSCode扩展"]
        Code["代码编辑器"]
    end

    subgraph "运行时"
        User["用户"]
        FrontendApp["前端应用 (React/Vue)"]
        I18nLib["i18n库"]
        BrowserCache["浏览器缓存"]
    end

    subgraph "内容维护"
        Maintainer["内容维护者"]
        ChromePlugin["Chrome插件"]
        DoubaoAPI["豆包翻译API"]
    end

    subgraph "后端服务"
        API["后端API"]
        DB[(数据库)]
        Redis[(Redis缓存)]
    end

    %% 流程连接
    User --> FrontendApp
    FrontendApp -->|"使用 t('key')"| I18nLib
    I18nLib -->|"检查缓存"| BrowserCache
    BrowserCache -->|"缓存未命中"| I18nLib
    I18nLib -->|"请求翻译<br/>(header: language)"| API
    API -->|查询| DB
    API -->|查询| Redis
    API -->|"返回翻译"| FrontendApp
    FrontendApp -->|存储| BrowserCache
    FrontendApp -->|"渲染UI"| User

    Maintainer -->|使用| ChromePlugin
    ChromePlugin -->|调用| DoubaoAPI
    ChromePlugin -->|"导出翻译文件"| Maintainer
    Maintainer -->|导入| DB

    Dev -->|"编写代码<br/>t('common.submit')"| Code
    VSCodeExt -->|"从API获取翻译"| API
    VSCodeExt -->|"显示行内提示"| Code
```

### 2. 前后端接口约定

#### 请求头语言标识

- **标准格式**：所有接口请求头必须携带`language`字段。
- **值格式**：使用标准语言代码（如`zh-CN`、`en-US`、`es-ES`等）。
- **默认处理**：后端根据请求头中的语言标识返回对应语言的数据。

```http
GET /api/data HTTP/1.1
Host: example.com
Content-Type: application/json
language: zh-CN
```

#### 多语言接口设计

- **翻译资源获取**: `GET /api/translations?resourceType={type}`

  - `language`请求头用于自动筛选语言。
  - `resourceType`参数用于指定资源类型（如`ui`, `menu`, `error`等），便于按模块加载。

- **接口返回格式**:

```json
{
  "code": 0,
  "data": [
    { "code": "common.submit", "value": "提交" },
    { "code": "common.cancel", "value": "取消" },
    { "code": "user.login", "value": "登录" }
  ]
}
```

## 四、前端集成指南 (Implementation Guide)

### 1. 通用数据转换流程

所有从后端获取的翻译数据都需要通过一个通用转换器，将其从数组格式转换为 i18n 库所需的嵌套 JSON 对象格式。

```javascript
// 将后端数据 [{code, value}] 转换为 i18n 嵌套对象
function transformApiDataToI18n(apiData) {
  const i18nData = {};
  apiData.forEach(item => {
    const keys = item.code.split('.');
    let current = i18nData;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = item.value;
  });
  return i18nData;
}
```

### 2. React 生态集成 (react-i18next)

#### 初始化与语言切换

```javascript
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import axios from 'axios'; // 假设使用axios

// 1. 初始化i18n
async function initializeI18n(language = 'zh-CN') {
  axios.defaults.headers.common['language'] = language;

  const [uiResponse, menuResponse] = await Promise.all([
    axios.get('/api/translations?resourceType=ui'),
    axios.get('/api/translations?resourceType=menu'),
  ]);

  const resources = {
    [language]: {
      ui: transformApiDataToI18n(uiResponse.data.data),
      menu: transformApiDataToI18n(menuResponse.data.data),
    },
  };

  await i18n.use(initReactI18next).init({
    resources,
    lng: language,
    fallbackLng: 'zh-CN',
    ns: ['ui', 'menu'],
    defaultNS: 'ui',
    interpolation: { escapeValue: false },
  });
  return i18n;
}

// 2. 语言切换功能
export async function changeLanguage(language) {
  axios.defaults.headers.common['language'] = language;
  const [uiResponse] = await Promise.all([axios.get('/api/translations?resourceType=ui')]);
  i18n.addResourceBundle(language, 'ui', transformApiDataToI18n(uiResponse.data.data), true, true);
  await i18n.changeLanguage(language);
}
```

#### 在组件中使用

```jsx
import React from 'react';
import { useTranslation, Trans } from 'react-i18next';

function MyComponent() {
  const { t } = useTranslation(['ui', 'menu']);

  return (
    <div>
      <h1>{t('ui:common.title')}</h1>
      <button>{t('menu:dashboard')}</button>

      {/* 带参数和组件的翻译 */}
      <Trans i18nKey="ui:user.greeting" values={{ name: 'John' }}>
        你好, <strong>{{ name }}</strong>.
      </Trans>
    </div>
  );
}
```

### 3. Vue 生态集成 (vue-i18n)

#### 初始化与语言切换

```javascript
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import axios from 'axios';

Vue.use(VueI18n);

// 1. 创建并导出i18n实例
export async function createI18n(language = 'zh-CN') {
  axios.defaults.headers.common['language'] = language;
  const response = await axios.get('/api/translations?resourceType=ui');
  const messages = {
    [language]: transformApiDataToI18n(response.data.data),
  };

  const i18n = new VueI18n({
    locale: language,
    fallbackLocale: 'zh-CN',
    messages,
  });

  // 2. 挂载异步切换语言的方法
  i18n.setLanguageAsync = async function (lang) {
    if (this.availableLocales.includes(lang)) {
      this.locale = lang;
      return;
    }
    axios.defaults.headers.common['language'] = lang;
    const response = await axios.get('/api/translations?resourceType=ui');
    this.setLocaleMessage(lang, transformApiDataToI18n(response.data.data));
    this.locale = lang;
  };

  return i18n;
}
```

#### 在组件中使用

```vue
<template>
  <div>
    <h1>{{ $t('common.title') }}</h1>
    <button @click="changeLang('en-US')">Switch Language</button>
  </div>
</template>

<script>
export default {
  methods: {
    changeLang(lang) {
      this.$i18n.setLanguageAsync(lang);
    },
  },
};
</script>
```

### 4. 特殊场景处理

- **带参数的翻译**：`t('user.greeting', { name: userName })`
- **日期/数字格式化**：利用框架能力，如 `Intl.NumberFormat` 或 `date-fns`。
- **HTML 内容翻译**：使用 React 的 `<Trans>` 组件或 Vue 的 `v-html` 指令（注意 XSS 风险）。

## 五、工具链使用手册 (Toolchain Manual)

### 1. 广汽国际平台 Chrome 插件 [1]

此工具旨在解决多语言内容从无到有的创建效率问题。

#### 使用流程

```mermaid
flowchart TD
    User["内容维护者"] -->|"1. 打开插件"| PluginConfig["插件翻译配置页"]
    PluginConfig -->|"2. 配置API和语种"| User
    User -->|"3. 输入源JSON"| InputData["JSON数据输入<br/>(包含key, text等)"]
    PluginConfig -->|"4. 调用翻译API"| DoubaoAPI["豆包API"]
    DoubaoAPI -->|"5. 返回译文"| PluginConfig
    PluginConfig -->|"6. 生成文件"| OutputFile["多语种翻译文件"]
    User -->|"7. 导入系统"| Backend["后端系统"]
```

1.  **配置**：在插件配置页，设置豆包 API 密钥，并选择需要翻译的目标语种。
2.  **输入**：提供源语言的 JSON 数据，格式为 `{ "user.module.email": { "text": "邮箱", "resourceType": "Front"}}`。
3.  **处理**：插件调用豆包 API，自动将文本翻译成所有选定的语言。
4.  **导出**：生成标准格式的翻译文件，可直接用于导入后端系统，完成内容的初始化。

### 2. VSCode 多语言开发助手 [2]

此扩展旨在解决开发过程中翻译键与实际文本脱节的痛点，提升开发体验。

#### 使用流程

```mermaid
flowchart TD
    %% 初始化阶段节点
    InitVSCode["VSCode启动/扩展激活"]
    VSCodeExt_Init["VSCode扩展"]
    API["翻译接口"]
    ExtCache["扩展本地缓存"]

    %% 开发时阶段节点
    Dev["开发者"]
    CodeFile["代码文件"]
    VSCodeExt["VSCode扩展"]

    %% 初始化阶段流程
    subgraph "初始化 (一次性)"
        InitVSCode --> VSCodeExt_Init
        VSCodeExt_Init -->|"1. 拉取全部翻译"| API
        API -->|"2. 返回数据"| VSCodeExt_Init
        VSCodeExt_Init -->|"3. 存入本地缓存"| ExtCache
    end

    %% 开发时阶段流程
    subgraph "开发时 (实时)"
        Dev -->|"编写代码<br/>t('common.submit')"| CodeFile
        VSCodeExt -->|"4. 扫描翻译键"| CodeFile
        VSCodeExt -->|"5. 查询本地缓存"| ExtCache
        ExtCache -->|"6. 返回提交"| VSCodeExt
        VSCodeExt -->|"7. 在代码旁显示"| CodeFile
    end
```

1.  **配置与初始化**：安装扩展后，在 VSCode 设置中配置后端翻译 API 的地址。扩展在启动时会 **一次性拉取所有翻译** 并缓存在本地。
2.  **翻译与显示**：当您打开代码文件时，扩展会实时扫描文件中的翻译键（如`t()`），并 **从本地缓存中** 快速查找匹配的文本，以行内提示或悬浮窗的形式展示，实现快速的翻译预览。
3.  **搜索与管理**：通过扩展的侧边栏，可以快速搜索已缓存的翻译键或文本值，方便管理和定位。

## 六、最佳实践与规范 (Best Practices)

### 1. 开发规范

- **翻译键命名**：遵循 `模块.子模块.功能` 或 `页面.组件.描述` 的格式，如 `login.form.username`。
- **禁止硬编码**：所有面向用户的文本都必须通过 i18n 函数包裹。
- **默认语言**：开发时，组件内应使用翻译键，而不是硬编码的中文或英文。

### 2. 性能与优化

#### 按需加载流程

- **策略**：结合前端框架的路由懒加载机制，在进入新页面或模块时，再请求该模块对应的翻译资源。
- **实现**：通过`resourceType`参数向 API 请求特定模块的翻译包，并动态注入到 i18n 实例中。

```mermaid
flowchart TD
    subgraph 按需加载流程
        direction TB
        RouteChange["路由变化 (进入新模块)"]
        CheckModuleLoaded["检查模块语言包是否已加载"]
        LoadModule["请求模块语言包<br/>GET /api/translations?resourceType=module"]
        AddToI18n["添加到i18n实例"]

        RouteChange --> CheckModuleLoaded
        CheckModuleLoaded -->|"未加载"| LoadModule
        CheckModuleLoaded -->|"已加载"| Done((End))
        LoadModule --> AddToI18n --> Done
    end
```

#### 缓存与版本控制

- **浏览器缓存**：使用`localStorage`或`sessionStorage`缓存已获取的翻译资源，减少不必要的 API 请求。
- **缓存失效**：为解决数据更新问题，推荐采用 **版本号机制**。
  1.  后端提供一个版本接口 `GET /api/translations/version`。
  2.  前端在应用启动时请求该版本号，并与本地缓存的版本号对比。
  3.  若版本号不一致，则清空所有本地缓存，重新拉取所有需要的翻译资源。

### 3. 错误处理

国际化的错误信息处理同样重要。

1.  **API 返回错误码**：后端 API 在发生错误时，应返回统一的错误码，如 `E_USER_NOT_FOUND`。
2.  **错误码翻译**：将这些错误码作为一种`resourceType`（如`error`）纳入多语言管理体系。
3.  **前端展示**：前端在捕获到 API 错误后，使用错误码作为翻译键（`t('error:E_USER_NOT_FOUND')`），向用户展示友好的、已翻译的错误提示。

## 七、术语表 (Glossary)

| 术语     | 英文/缩写                   | 解释                                                                   |
| -------- | --------------------------- | ---------------------------------------------------------------------- |
| 国际化   | Internationalization (i18n) | 使软件应用能够适应不同语言和地区的过程。                               |
| 翻译键   | Translation Key             | 在代码中用于引用特定翻译文本的唯一标识符，如 `common.submit`。         |
| 资源类型 | Resource Type               | 用于对翻译资源进行分类的标识，如 `ui`, `menu`, `error`，便于按需加载。 |
| 命名空间 | Namespace (NS)              | i18n 库中的概念，与`resourceType`对应，用于组织翻译键。                |
| 后备语言 | Fallback Language           | 当当前语言的翻译缺失时，系统会使用的默认语言。                         |
| 豆包 API | Doubao API                  | 用于自动化翻译文本的第三方服务。                                       |

## 八、参考资料 (References)

[1] 广汽国际平台 Chrome 插件: `[待补充链接]`
[2] VSCode 多语言开发助手: `[待补充链接]`
