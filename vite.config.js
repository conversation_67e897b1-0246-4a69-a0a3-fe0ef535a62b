import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __dirname = fileURLToPath(new URL('.', import.meta.url));

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    // 开启源码映射，方便调试（开发环境使用）

    sourcemap: process.env.NODE_ENV === 'development',
    // 配置chunk大小警告的阈值，适当调高（Chrome扩展一般不超过4MB）
    chunkSizeWarningLimit: 1500,
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 配置浏览器兼容目标
    target: 'es2015',
    rollupOptions: {
      input: {
        popup: resolve(__dirname, 'popup.html'),
        options: resolve(__dirname, 'options.html'),
        translation: resolve(__dirname, 'translation.html'),
        batchMenu: resolve(__dirname, 'batchMenu.html'),
        background: resolve(__dirname, 'src/background.js'),
      },
      output: {
        // 控制CSS/图片等资源输出
        assetFileNames: 'assets/[name]-[hash][extname]',
        // 自定义chunk文件输出名称
        chunkFileNames: 'assets/[name]-[hash].js',
        // 入口文件输出名称
        entryFileNames: chunk => {
          // 对background保留原名
          if (chunk.name === 'background') {
            return 'background.js';
          }
          return 'assets/[name]-[hash].js';
        },
        manualChunks: {
          'antd-pro-components': ['@ant-design/pro-components'],
        },
      },
      // 外部化一些可能不需要打包的依赖
      external: [],
    },
    // 配置最小化选项
    minify: 'terser',
    terserOptions: {
      compress: {
        // 移除console和debugger
        drop_console: false,
        drop_debugger: true,
        // 优化
        passes: 2,
        // 移除未使用代码
        pure_getters: true,
      },
      output: {
        // 移除注释
        comments: false,
      },
    },
  },
  // 优化依赖预构建
  optimizeDeps: {
    // 强制预构建这些包
    include: ['react', 'react-dom', 'antd', '@ant-design/icons'],
    // 排除非常规包
    exclude: [],
  },
});
