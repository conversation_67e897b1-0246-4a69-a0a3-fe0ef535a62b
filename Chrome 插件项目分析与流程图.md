# Chrome 插件项目分析与流程图

## 插件核心组成分析

通过对代码结构分析，这是一个基于 React 和 Chrome 扩展 API 的多功能工具，主要包含三大核心功能：多站点 OBS 文件上传、多语言翻译处理、批量添加菜单项。

### 主要功能模块

1. **多站点 OBS 文件上传**：自动化处理文件到多个站点的 OBS 存储
2. **多语言翻译处理**：辅助多语言内容的导入、翻译及导出
3. **批量添加菜单项**：在多个站点后台批量创建菜单

### 技术架构

- 前端框架：React 19
- 构建工具：Vite 6
- UI 库：Ant Design 5 + Pro Components
- 扩展规范：Chrome Extension API (Manifest V3)
- 数据处理：xlsx.js

### 关键入口与核心组件

- `background.js`：后台服务 Worker，处理核心业务逻辑和消息
- 页面入口：`popup.jsx`、`options.jsx`、`translation.jsx`、`batchAddMenu.jsx`
- 服务层：各种业务逻辑服务，如`menuService.js`、`translationService.js`等
- 工具层：通用功能如 Excel 处理、文件操作、DOM 操作等

## 系统流程图

```mermaid
flowchart TB
    classDef uiClass fill:#3498db,stroke:#1b6698,color:white
    classDef bgClass fill:#9b59b6,stroke:#8e44ad,color:white
    classDef serviceClass fill:#2ecc71,stroke:#27ae60,color:white
    classDef utilClass fill:#e67e22,stroke:#d35400,color:white
    classDef dataClass fill:#f1c40f,stroke:#f39c12,color:black

    %% 主要入口
    UI["用户界面层"] --> Popup["弹出窗口<br/>(popup.jsx)"]:::uiClass
    UI --> Options["选项页面<br/>(options.jsx)"]:::uiClass
    UI --> Translation["翻译功能<br/>(translation.jsx)"]:::uiClass
    UI --> BatchMenu["批量菜单<br/>(batchAddMenu.jsx)"]:::uiClass

    %% 后台服务
    BG["后台服务<br/>(background.js)"]:::bgClass

    %% 服务层
    Services["服务层"] --> AuthService["认证服务<br/>(authService.js)"]:::serviceClass
    Services --> FileUploadService["文件上传服务<br/>(fileUploadService.js)"]:::serviceClass
    Services --> MenuService["菜单服务<br/>(menuService.js)"]:::serviceClass
    Services --> TranslationService["翻译服务<br/>(translationService.js)"]:::serviceClass
    Services --> ExcelService["Excel服务<br/>(excelService.js)"]:::serviceClass
    Services --> ProcessingService["处理服务<br/>(processingService.js)"]:::serviceClass
    Services --> MessageService["消息服务<br/>(messageService.js)"]:::serviceClass

    %% 工具层
    Utils["工具层"] --> ExcelUtils["Excel工具<br/>(excelUtils.js)"]:::utilClass
    Utils --> FileUtils["文件工具<br/>(fileUtils.js)"]:::utilClass
    Utils --> DomUtils["DOM工具<br/>(domUtils.js)"]:::utilClass
    Utils --> TranslateUtils["翻译工具<br/>(translate/*.js)"]:::utilClass

    %% 组件层
    Components["组件层"] --> FileUploader["文件上传器"]:::uiClass
    Components --> SiteConfigTable["站点配置表"]:::uiClass
    Components --> ImportModal["导入模态框"]:::uiClass
    Components --> ResultDisplay["结果显示"]:::uiClass
    Components --> JsonInputCard["JSON输入卡片"]:::uiClass

    %% 数据流
    Popup -- "用户交互" --> BG
    Options -- "配置更新" --> BG
    Translation -- "翻译请求" --> BG
    BatchMenu -- "菜单操作" --> BG

    BG -- "认证" --> AuthService
    BG -- "文件处理" --> FileUploadService
    BG -- "菜单处理" --> MenuService
    BG -- "翻译处理" --> TranslationService
    BG -- "状态管理" --> ProcessingService

    AuthService -- "使用" --> FileUtils
    FileUploadService -- "使用" --> ExcelUtils
    MenuService -- "使用" --> DomUtils
    TranslationService -- "使用" --> TranslateUtils

    ExcelService -- "使用" --> ExcelUtils
    ProcessingService -- "使用" --> MessageService

    %% 注明Chrome API
    ChromeAPI["Chrome扩展API"]:::dataClass
    BG -- "调用" --> ChromeAPI
    MessageService -- "调用" --> ChromeAPI
```

## 功能流程图

```mermaid
flowchart TB
    classDef processClass fill:#3498db,stroke:#1b6698,color:white
    classDef actionClass fill:#2ecc71,stroke:#27ae60,color:white
    classDef dataClass fill:#e67e22,stroke:#d35400,color:white
    classDef decisionClass fill:#9b59b6,stroke:#8e44ad,color:white

    %% 文件上传流程
    subgraph "多站点OBS文件上传流程"
        A1[用户选择文件]:::actionClass --> A2[配置目标站点]:::actionClass
        A2 --> A3{站点已认证?}:::decisionClass
        A3 -- 是 --> A5[上传文件处理]:::processClass
        A3 -- 否 --> A4[站点认证]:::processClass
        A4 --> A5
        A5 --> A6[OBS存储操作]:::processClass
        A6 --> A7[显示上传结果]:::actionClass
    end

    %% 多语言翻译流程
    subgraph "多语言翻译处理流程"
        B1[导入原始文本]:::actionClass --> B2[选择目标语言]:::actionClass
        B2 --> B3[配置翻译选项]:::actionClass
        B3 --> B4[执行翻译处理]:::processClass
        B4 --> B5[生成翻译结果]:::processClass
        B5 --> B6[导出多语言文件]:::actionClass
    end

    %% 批量添加菜单流程
    subgraph "批量添加菜单流程"
        C1[导入菜单数据]:::actionClass --> C2[配置目标站点]:::actionClass
        C2 --> C3{站点已认证?}:::decisionClass
        C3 -- 是 --> C5[解析菜单结构]:::processClass
        C3 -- 否 --> C4[站点认证]:::processClass
        C4 --> C5
        C5 --> C6[批量创建菜单]:::processClass
        C6 --> C7[显示操作结果]:::actionClass
    end

    %% 数据交互流程
    subgraph "后台数据处理流程"
        D1[接收前端消息]:::dataClass --> D2{消息类型判断}:::decisionClass
        D2 -- "文件上传" --> D3[调用文件上传服务]:::processClass
        D2 -- "翻译处理" --> D4[调用翻译服务]:::processClass
        D2 -- "菜单操作" --> D5[调用菜单服务]:::processClass
        D3 --> D6[更新处理状态]:::dataClass
        D4 --> D6
        D5 --> D6
        D6 --> D7[返回处理结果]:::dataClass
    end
```

## 组件交互图

```mermaid
sequenceDiagram
    participant 用户
    participant UI as 用户界面
    participant BG as 后台服务(background.js)
    participant Auth as 认证服务
    participant Service as 业务服务
    participant API as 外部API

    用户->>UI: 打开插件
    UI->>BG: 初始化请求
    BG->>UI: 返回当前状态

    alt 文件上传
        用户->>UI: 选择文件并配置站点
        UI->>BG: 发送START_FILE_UPLOAD消息
        BG->>Auth: 检查站点认证
        Auth-->>BG: 返回认证状态

        alt 需要认证
            BG->>API: 执行站点登录
            API-->>BG: 返回认证结果
        end

        BG->>Service: 调用上传服务
        Service->>API: 执行文件上传
        API-->>Service: 返回上传结果
        Service-->>BG: 处理结果
        BG-->>UI: 更新上传状态
        UI->>用户: 显示上传结果
    end

    alt 多语言翻译
        用户->>UI: 导入待翻译内容
        UI->>BG: 发送IMPORT_MULTI_LANGUAGE消息
        BG->>Service: 调用翻译服务
        Service->>API: 执行翻译操作
        API-->>Service: 返回翻译结果
        Service-->>BG: 处理结果
        BG-->>UI: 更新翻译状态
        UI->>用户: 显示翻译结果
    end

    alt 批量添加菜单
        用户->>UI: 导入菜单数据并配置站点
        UI->>BG: 发送BATCH_ADD_MENUS消息
        BG->>Auth: 检查站点认证
        Auth-->>BG: 返回认证状态

        alt 需要认证
            BG->>API: 执行站点登录
            API-->>BG: 返回认证结果
        end

        BG->>Service: 调用菜单服务
        Service->>API: 执行菜单创建
        API-->>Service: 返回操作结果
        Service-->>BG: 处理结果
        BG-->>UI: 更新菜单状态
        UI->>用户: 显示操作结果
    end
```
