---
description: 
globs: 
alwaysApply: true
---
# 项目概要与核心模块

## 1. 项目概述
广汽国际平台插件集合是一款Chrome浏览器扩展，用于自动化处理多站点的OBS文件上传、多语言翻译处理及批量添加菜单项等功能。

## 2. 核心功能
- **多站点OBS文件上传**: 自动化处理文件到多个站点的OBS存储。
- **多语言翻译处理**: 辅助多语言内容的导入、翻译及导出。
- **批量添加菜单项**: 在多个站点后台批量创建菜单。

## 3. 技术栈
- **前端框架**: React 19
- **构建工具**: Vite 6
- **UI组件库**: Ant Design 5 + Pro Components
- **扩展API**: Chrome Extension API (Manifest V3)
- **Excel处理**: xlsx.js

## 4. 主要入口文件
- `src/background.js`: 扩展的后台服务脚本 (Service Worker)。
- `src/popup.jsx`: 扩展弹出窗口UI入口。
- `src/options.jsx`: 扩展选项页UI入口。
- `src/translation.jsx`: 翻译功能主页面UI入口。
- `src/batchAddMenu.jsx`: 批量添加菜单功能主页面UI入口。

## 5. 项目结构重点
- **`src/components/`**: 存放可复用UI组件。
    - `FileUploader/`: 文件上传交互。
    - `SiteFormList/`: 站点配置表单管理。
    - `ResultDisplay/`: 操作结果展示。
    - `ImportMultiLanguageModal/`: 多语言文件导入模态框。
    - `CollapsibleCard/`: 可折叠内容卡片。
    - `LightStatisticComponents/`: 轻量级统计数据显示。
- **`src/pages/`**: 存放各功能页面的主组件。
    - `Popup/`: 弹出窗口。
    - `Options/`: 选项页。
    - `Translation/`: 翻译页。
    - `BatchAddMenu/`: 批量加菜单页。
- **`src/services/`**: 存放业务逻辑服务。
    - `menuService.js`: 处理菜单相关的业务逻辑。
- **`src/utils/`**: 存放通用工具函数。
    - `excelHandler.js`, `excelUtils.js`: Excel文件读写与数据处理。
    - `fileUtils.js`: 文件常用操作。
    - `domUtils.js`: DOM操作辅助。
    - `translate/`: 翻译相关辅助函数。
- **`src/background.js` (后台服务核心)**:
    - **消息处理**: 响应来自各页面的指令 (如 `START_FILE_UPLOAD`, `BATCH_ADD_MENUS`, `IMPORT_MULTI_LANGUAGE` 等)。
    - **核心流程**: 实现文件上传 (`startFileUpload`)、菜单添加 (`startMenuAddition`)、多语言导入 (`importMultiLanguage`) 等核心业务。
    - **状态与认证**: 管理操作状态 (`processingState`) 和站点认证 (`authenticateSite`, `tokenCache`)。
- **`public/manifest.json`**: Chrome扩展的配置文件，定义权限、后台脚本、页面等。
- **`vite.config.js`**: Vite构建配置文件。
