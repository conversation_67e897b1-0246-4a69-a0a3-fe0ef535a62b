---
description: 
globs: 
alwaysApply: true
---
# 开发与构建指南

## 1. 开发环境配置
### 前置要求
- Node.js ≥ 18.0.0
- pnpm ≥ 8.0.0
- Chrome浏览器

### 安装与启动
1.  **安装依赖**: `pnpm install`
2.  **启动开发模式**: `pnpm dev` (Vite会监视文件变动并热更新到 `dist/` 目录)
3.  **加载插件到Chrome**:
    *   打开 `chrome://extensions/`
    *   启用 "开发者模式"
    *   点击 "加载已解压的扩展程序"，选择项目中的 `dist` 目录。

## 2. 开发流程要点
### 添加新功能步骤
1.  **组件/页面创建**: 在 `src/components` 或 `src/pages` 创建相应文件/目录。
2.  **服务逻辑**: 如涉及复杂业务或API交互，在 `src/services` 中添加或修改服务。
3.  **工具函数**: 通用功能抽象到 `src/utils`。
4.  **入口与路由**: 更新 `src/` 下的相应入口文件 (`popup.jsx`, `options.jsx` 等)，或在 `vite.config.js` (针对多页面应用输入) 和 `public/manifest.json` (如 `action`, `options_page`, 或需要直接打开的HTML页面) 中配置。
5.  **后台通信**: 若新功能需与后台脚本交互：
    *   在 `src/background.js` 中添加消息监听器 (`chrome.runtime.onMessage`) 和处理逻辑。
    *   前端通过 `chrome.runtime.sendMessage` 发送消息。
6.  **权限更新**: 如需新Chrome API权限，务必更新 `public/manifest.json` 中的 `permissions` 和 `host_permissions`。
7.  **错误处理与日志**: 添加健壮的错误处理和必要的日志输出。

### 代码规范
- **命名**: 组件和目录使用 `PascalCase`，工具函数和变量使用 `camelCase`。
- **风格**: 使用React函数组件和Hooks，遵循组件化原则。
- **注释**: 代码中使用中文注释，说明关键逻辑和意图。
- **ESLint**: 遵循项目配置的ESLint规则 (`eslint.config.js`)。

## 3. 构建与发布
### 构建命令
- **开发构建**: `pnpm build` (生成未压缩的开发版本到 `dist/`)
- **生产构建 (用于插件)**: `pnpm build:extension` (生成优化和压缩的生产版本到 `dist/`)
- **打包扩展**: `pnpm package` (执行生产构建，并打包成 `.crx` 和 `.zip` 文件到 `release/` 目录)

### 发布步骤
1.  **更新版本号**: 在 `package.json` 和 `public/manifest.json` 中更新版本。
2.  **构建与打包**: 运行 `pnpm package`。
3.  **测试**: 严格测试打包后的扩展功能。
4.  **发布**: 上传 `release/` 目录下的 `.zip` 文件到Chrome Web Store，或分发 `.crx` 文件。

## 4. 调试技巧
- **扩展页面**: 使用 `chrome://extensions/` 管理和重载插件。
- **DevTools**: 
    - **弹出窗口 (Popup)**: 右键点击插件图标，选择"检查弹出窗口"。
    - **后台脚本 (Service Worker)**: 在 `chrome://extensions/` 页面，找到你的插件，点击"服务工作线程"或类似链接。
    - **选项页/功能页**: 直接在打开的页面上右键选择"检查"。
- **常见问题排查**:
    - **权限问题**: 仔细核对 `manifest.json` 中的权限声明。
    - **消息通信**: 确保 `onMessage` 监听器正确处理异步并返回 `true` (如果需要异步响应)。
    - **异步操作**: 正确使用 `async/await` 和 Promises。
    - **内容脚本注入**: 检查 `manifest.json` 中 `content_scripts` 的匹配规则和注入时机。
