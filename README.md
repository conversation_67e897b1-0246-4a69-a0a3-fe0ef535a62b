# 广汽国际平台插件集合 - 开发者文档

## 用户使用手册 (User Guide)

详细的用户操作说明，请参阅 [用户使用手册](./USAGE_GUIDE.md)。

## 目录

- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [开发环境设置](#开发环境设置)
- [构建流程](#构建流程)
- [扩展功能指南](#扩展功能指南)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [发布流程](#发布流程)

## 项目概述

广汽国际平台插件集合是一款Chrome浏览器扩展，用于自动化处理多站点的OBS文件上传、多语言翻译处理及批量添加菜单项等功能。本项目使用React + Vite构建，采用了现代前端开发技术栈和最佳实践。

## 技术栈

- **前端框架**：React 19
- **构建工具**：Vite 6
- **UI组件库**：Ant Design 5 + Pro Components
- **代码编辑器**：CodeMirror (用于JSON编辑)
- **状态管理**：React Hooks (通过 useContext 和 useReducer 实现)
- **扩展API**：Chrome Extension API (Manifest V3)
- **Excel处理**：xlsx.js
- **文件压缩**：JSZip
- **AI翻译**：火山引擎豆包API
- **代码规范**：ESLint

## 项目结构

```
gq-unified-platform-chrome-extension/
├── dist/                # 构建输出目录
├── public/              # 静态资源目录
│   ├── icons/           # 插件图标
│   ├── manifest.json    # Chrome扩展配置文件
├── scripts/             # 构建和辅助脚本
├── src/                 # 源代码目录
│   ├── assets/          # 图片、字体等资源文件
│   ├── components/      # 通用组件
│   │   ├── FileUploader/           # 文件上传组件
│   │   ├── ResultDisplay/          # 结果展示组件
│   │   ├── SiteFormList/           # 站点表单列表组件
│   │   ├── SiteConfigTable/        # 站点配置表格组件
│   │   ├── ImportMultiLanguageModal/ # 多语言导入模态框
│   │   ├── CollapsibleCard/        # 可折叠卡片组件
│   │   ├── BaseJsonInputCard/      # JSON输入卡片组件
│   │   └── LightStatisticComponents/ # 轻量统计组件
│   ├── pages/           # 页面组件
│   │   ├── Options/         # 选项页面
│   │   ├── Popup/           # 弹出窗口页面
│   │   ├── Translation/     # 翻译功能页面
│   │   └── BatchAddMenu/    # 批量添加菜单页面
│   ├── services/        # 服务逻辑 (例如：API请求、业务处理)
│   │   ├── authService.js       # 认证服务
│   │   ├── fileUploadService.js # 文件上传服务
│   │   ├── menuService.js       # 菜单相关服务
│   │   ├── translationService.js # 翻译服务
│   │   ├── excelService.js      # Excel处理服务
│   │   ├── excelHeaderService.js # Excel表头翻译服务
│   │   ├── languageService.js   # 语言处理服务
│   │   ├── processingService.js # 处理流程服务
│   │   ├── stateService.js      # 状态管理服务
│   │   └── messageService.js    # 消息通信服务
│   ├── styles/          # 样式文件 (例如：index.css, antd-override.css)
│   ├── utils/           # 工具函数
│   │   ├── excelHandler.js  # Excel文件内容解析
│   │   ├── fileUtils.jsx    # 文件操作工具
│   │   ├── excelUtils.js    # Excel数据处理与导出
│   │   ├── domUtils.js      # DOM操作工具
│   │   └── translate/       # 翻译相关工具函数
│   ├── background.js    # 后台服务脚本 (Manifest V3 Service Worker)
│   ├── options.jsx      # 选项页面入口 (React)
│   ├── popup.jsx        # 弹出窗口入口 (React)
│   ├── translation.jsx  # 翻译功能页面入口 (React)
│   └── batchAddMenu.jsx # 批量添加菜单功能入口 (React)
├── index.html           # HTML模板 (Vite 入口)
├── translation.html     # 翻译功能页面HTML模板
├── batchMenu.html       # 批量菜单页面HTML模板
├── options.html         # 选项页HTML模板
├── popup.html           # 弹出窗口HTML模板
├── package.json         # 项目依赖和脚本
├── pnpm-lock.yaml       # pnpm锁定文件
├── vite.config.js       # Vite配置
├── eslint.config.js     # ESLint配置
├── jsconfig.json        # JavaScript配置
├── key.pem              # 扩展签名私钥
├── Chrome 插件项目分析与流程图.md  # 项目分析文档
├── 工具链驱动的前端国际化解决方案.md  # 国际化方案文档
├── USAGE_GUIDE.md       # 用户使用手册
└── README.md            # 项目说明
```

## 开发环境设置

### 前置要求

- Node.js ≥ 18.0.0
- pnpm ≥ 8.0.0
- Chrome浏览器

### 安装步骤

1. 克隆代码库

   ```bash
   git clone [仓库地址]
   cd gq-unified-platform-chrome-extension
   ```

2. 安装依赖

   ```bash
   pnpm install
   ```

3. 启动开发服务器

   ```bash
   pnpm dev
   ```

4. 在Chrome中加载插件
   - 打开Chrome，访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目的`dist`目录

## 构建流程

### 开发构建

```bash
# 启动开发服务器
pnpm dev

# 构建开发版本
pnpm build
```

### 生产构建

```bash
# 构建生产版本
pnpm build:extension
```

生产构建会执行以下步骤：

1. 使用Vite构建优化的生产版本
2. 复制manifest.json和所需资源到dist目录
3. 压缩代码以提高性能

### 扩展打包

```bash
# 打包成Chrome扩展文件(.crx和.zip)
pnpm package
```

打包过程会执行以下步骤：

1. 执行生产构建（build:extension）
2. 生成私钥（如果不存在）并保存为key.pem
3. 将扩展打包为.crx文件（用于直接分发安装）
4. 将扩展打包为.zip文件（用于Chrome商店上传）
5. 输出文件保存在release目录中

**注意事项：**

- 生成的私钥（key.pem）用于签名，请妥善保存
- 相同的私钥确保更新版本时保持相同的扩展ID
- .crx文件可直接分发给用户安装
- .zip文件用于上传至Chrome Web Store

## 扩展功能指南

### 核心功能详解

#### 1. 多语言翻译功能

**支持的翻译模式**：
- **JSON翻译模式**：处理JSON格式的多语言文件，支持批量翻译和导出
- **Excel表头翻译模式**：专门用于翻译Excel文件的表头，生成多语言版本的Excel文件

**AI翻译集成**：
- 集成火山引擎豆包API，支持多种AI模型（Doubao-1.5-pro、DeepSeek等）
- 支持18种语言的自动翻译（中文、英语、西班牙语、葡萄牙语、阿拉伯语、泰语等）
- 可配置翻译参数（温度系数、模型选择等）

**功能特性**：
- 支持现有翻译文件的导入和增量翻译
- 自动生成多语言Excel文件并打包为ZIP
- 实时翻译进度显示和错误处理
- 支持JSON格式验证和语法高亮编辑

#### 2. 批量菜单管理功能

**菜单操作能力**：
- 支持多站点批量添加菜单项
- 自动处理菜单层级关系和权限分配
- 支持菜单数据的JSON格式导入
- 实时显示添加进度和结果

**权限管理**：
- 自动获取和分配菜单权限
- 支持角色菜单树的构建
- 集成权限分配API调用

#### 3. 文件上传增强功能

**上传能力**：
- 支持多文件并发上传到多个站点
- 自动获取OBS临时密钥
- 支持大文件分片上传
- 实时上传进度和状态监控

### 添加新功能

1. **创建组件**：在`src/components`目录下创建新组件目录。
2. **添加页面**：如需新页面，在`src/pages`下创建对应的页面组件和入口 `jsx` 文件 (如 `src/myNewPage.jsx`)，并在 `vite.config.js` 和 `public/manifest.json` (如果需要直接访问) 中配置。
3. **更新后台脚本**：如需处理新的消息或后台任务，在`background.js`中添加处理逻辑。
4. **更新manifest**：如需额外权限或注册新的后台脚本、内容脚本等，更新`public/manifest.json`。
5. **定义服务**：如涉及特定业务逻辑，可在 `src/services` 中创建或修改服务文件。
6. **添加工具函数**：通用功能函数应放在 `src/utils` 目录下。

### 后台服务开发

`background.js`是扩展的核心，负责处理所有的后台任务和消息通信。

- **消息处理**：使用`chrome.runtime.onMessage.addListener`处理来自弹出窗口、选项页、内容脚本等的消息。主要消息类型包括：
  - `GET_SITE_CONFIGS`: 获取站点配置信息
  - `SAVE_SITE_CONFIGS`: 保存站点配置信息
  - `CLEAR_SITE_CONFIGS`: 清除站点配置信息
  - `OPEN_TRANSLATION_PAGE`: 打开翻译页面
  - `OPEN_BATCH_MENU_PAGE`: 打开批量菜单添加页面
  - `PROCESS_COMPLETED`: 处理完成通知
  - `IMPORT_MULTI_LANGUAGE`: 导入多语言请求
  - `START_BROWSER_AUTOMATION`: 开始文件上传自动化处理
  - `GET_PROCESSING_STATUS`: 获取处理状态
  - `CANCEL_PROCESSING`: 取消处理过程
  - `BATCH_ADD_MENU`: 批量添加菜单项
- **核心功能实现**:
  - 文件上传处理流程 (通过`processingService.startSiteProcessing`实现)
  - 菜单添加处理流程 (通过`menuService.addMenusToSite`实现)
  - 多语言导入处理流程 (通过`languageService.importLanguageToSite`实现)
  - 站点登录认证 (通过`authService.authenticateSite`实现，并使用token缓存管理)
- **状态管理**：使用`processingState`对象管理所有处理状态，包括站点配置、文件内容、处理结果、登录状态和操作类型。
- **API交互**：与各站点API进行交互。
- **存储**：使用 `chrome.storage.local` 或 `chrome.storage.sync` 进行数据持久化。

### 浏览器API使用

扩展利用以下Chrome API：

- `chrome.storage` - 保存站点配置
- `chrome.runtime` - 消息通信
- `chrome.tabs` - 标签页交互
- `chrome.action` - 工具栏按钮控制

## 代码规范

本项目使用ESLint进行代码规范检查，配置文件为`eslint.config.js`。

### 代码风格指南

- 使用函数组件和React Hooks
- 遵循组件化开发原则
- 文件和目录使用PascalCase命名 (例如：`MyComponent.jsx`, `src/pages/MyPage/`)
- 工具函数和普通JS/TS变量使用camelCase命名 (例如：`excelHandler.js`, `myVariable`)
- 代码中使用中文注释

### 提交规范

提交信息应遵循以下格式：

```
<类型>(<范围>): <描述>

<详细说明>

<相关issue>
```

类型包括：feat、fix、docs、style、refactor、perf、test、chore等

## 测试指南

### 手动测试

1. 使用`pnpm build:extension`构建扩展
2. 在Chrome中加载扩展进行测试
3. 测试不同场景：
   - **文件上传功能**：
     - 多文件上传到多个站点
     - 不同大小的文件上传
     - 网络中断情况处理
     - 多站点配置验证
   - **翻译功能**：
     - JSON格式翻译测试
     - Excel表头翻译测试
     - 多语言输出验证
     - AI模型切换测试
     - 大批量翻译性能测试
   - **菜单管理功能**：
     - 批量菜单添加测试
     - 菜单层级关系验证
     - 权限分配功能测试
     - 多站点菜单同步测试

### 自动化测试（计划中）

计划添加Jest或Cypress进行自动化测试。

## 发布流程

### 版本控制

1. 更新`package.json`和`manifest.json`中的版本号
2. 更新更新日志

### 打包发布

1. 执行扩展打包命令
   ```bash
   pnpm package
   ```
2. 在Chrome Web Store开发者控制台上传生成的zip文件（位于release目录）
3. 填写更新说明
4. 提交审核

### 内部分发（测试版）

对于内部测试版，可通过以下方式分发：

1. 分享生成的.crx文件（位于release目录）
2. 用户可拖拽.crx文件到Chrome浏览器安装
3. 或者通过开发者模式加载dist目录
